using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using Levelbuild.Core.DataStoreInterface.Dto.Interfaces;

namespace Levelbuild.Core.DataStoreInterface.Dto;

/// <summary>
/// Most basic authentication which only contains the users name
/// </summary>
public class DataStoreAuthentication : IDataStoreAuthentication
{
	/// <summary>
	/// username for authentication
	/// </summary>
	public string Username { get; init; }
	
	/// <summary>
	/// user id for authentication
	/// </summary>
	public Guid UserId { get; init; }

	/// <summary>
	/// Configured language of authenticated user
	/// </summary>
	public string Language { get; init; } = "en";

	/// <summary>
	/// List of groups, the user belongs to
	/// </summary>
	public IList<string>? Groups { get; init; }

	/// <summary>
	/// Constructor
	/// </summary>
	/// <param name="username">Name of the user</param>
	/// <param name="userId"></param>
	/// <param name="groups">Contains a list of all the groups this user is a member of.</param>
	public DataStoreAuthentication(string username, Guid userId, IList<string>? groups)
	{
		Username = username;
		UserId = userId;
		Groups = groups;
	}

	/// <summary>
	/// Hash of combination of user properties
	/// </summary>
	/// <returns></returns>
	public string GetHash()
	{
		var toHash = Username + "##" + UserId + "##" + Language + "##" + JsonSerializer.Serialize(Groups);
		using var sha1 = SHA1.Create();
		var hash = sha1.ComputeHash(Encoding.UTF8.GetBytes(toHash));
		return Convert.ToBase64String(hash);
	}
}