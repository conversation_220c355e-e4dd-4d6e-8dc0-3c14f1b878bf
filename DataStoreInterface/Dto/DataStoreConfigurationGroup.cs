using Microsoft.Extensions.Localization;

namespace Levelbuild.Core.DataStoreInterface.Dto;

/// <summary>
/// Represents a section inside the configuration of the data store implementation
/// </summary>
public class DataStoreConfigurationGroup
{
	/// <summary>
	/// Group key
	/// </summary>
	public string Key { get; set; }
	
    /// <summary>
    /// Section label
    /// </summary>
    public string Name { get; set; }
    
    /// <summary>
    /// Optional section description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// configuration options contained inside this section
    /// </summary>
    public IList<DataStoreConfigurationOption> Options { get; } = new List<DataStoreConfigurationOption>();

	/// <summary>
	/// Constructor
	/// </summary>
	/// <param name="key">Group key</param>
	/// <param name="name">Section label</param>
	public DataStoreConfigurationGroup(string key, string name)
	{
		Key = key;
        Name = name;
    }
    
    /// <summary>
    /// Add a new configuration option to the section
    /// </summary>
    /// <param name="option">Configuration option</param>
    /// <returns>returns <see cref="DataStoreConfigurationGroup">this</see> to support chaining</returns>
    public DataStoreConfigurationGroup Add(DataStoreConfigurationOption option)
    {
        Options.Add(option);
        return this;
    }

	/// <summary>
	/// translate 
	/// </summary>
	/// <param name="localizer"></param>
	public void Localize(IStringLocalizer localizer)
	{
		Name = ValueOrDefault(localizer, "Groups/" + Key + "Name", Name);
		Description = Description != null ? ValueOrDefault(localizer, Key + "Description", Description) : Description;
		foreach (var option in Options)
		{
			option.Localize(localizer);
		}
	}
	
	private string ValueOrDefault(IStringLocalizer localizer, string key, string defaultValue)
	{
		var translation = localizer[key];
		return translation != $"[{key}]" ? translation : defaultValue;
	}
}