using Levelbuild.Core.DataStoreInterface.Enum;
using Microsoft.Extensions.Localization;

namespace Levelbuild.Core.DataStoreInterface.Dto;

/// <summary>
/// A single configuration option
/// </summary>
public class DataStoreConfigurationOption
{
	/// <summary>
	/// Option key
	/// </summary>
	public string Name { get; init; }
	
	/// <summary>
	/// Option value type
	/// </summary>
	// ReSharper disable once UnusedAutoPropertyAccessor.Global
	public DataStoreConfigurationOptionType Type { get; init; }
	
	/// <summary>
	/// Option label
	/// </summary>
	public string Label { get; set; }
	
	/// <summary>
	/// optional tooltip
	/// </summary>
	public string? Tooltip { get; set; }
	
	/// <summary>
	/// optional default value
	/// </summary>
	public object? DefaultValue { get; init; }
	
	/// <summary>
	/// Is this option required in order to initialize the data store?
	/// </summary>
	public bool Required { get; init; }
	
	/// <summary>
	/// Those are all the options available for this config setting. Only one can be selected.
	/// </summary>
	public IEnumerable<object>? Options { get; init; }
	
	/// <summary>
	/// Those are all the options available for this config setting. Only one can be selected.
	/// </summary>
	public IEnumerable<DataStoreConfigurationOptionGroup>? ShowIf { get; init; }
	
	/// <summary>
	/// New Configuration Option instance
	/// </summary>
	/// <param name="name">Option key</param>
	/// <param name="type">Option value type</param>
	/// <param name="label">Option label</param>
	public DataStoreConfigurationOption(string name, DataStoreConfigurationOptionType type, string label)
	{
		Name = name;
		Type = type;
		Label = label;
	}
	
	/// <summary>
	/// Set localizer for eg. default language, date time, timezone, ...
	/// </summary>
	/// <param name="localizer"></param>
	public void Localize(IStringLocalizer localizer)
	{
		Label = ValueOrDefault(localizer, "Options/" + Name + "Label", Label);
		Tooltip = Tooltip != null ? ValueOrDefault(localizer, "Options/" + Name + "Tooltip", Tooltip) : null;
	}
	
	private string ValueOrDefault(IStringLocalizer localizer, string key, string defaultValue)
	{
		var translation = localizer[key];
		return translation != $"[{key}]" ? translation : defaultValue;
	}
}