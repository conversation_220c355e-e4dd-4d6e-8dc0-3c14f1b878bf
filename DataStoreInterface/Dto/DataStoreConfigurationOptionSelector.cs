using Levelbuild.Core.DataStoreInterface.Enum;
// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Core.DataStoreInterface.Dto;

/// <summary>
/// Holds a single filter element like 'ConfigOption = "test"'
/// </summary>
public class DataStoreConfigurationOptionSelector
{
	/// <inheritdoc cref="DataStoreConfigurationOptionCompareType"/>
	public DataStoreConfigurationOptionCompareType Type { get; init; }
	
	/// <summary>
	/// the config option to test against
	/// </summary>
	public string ConfigOption { get; init; }
	
	/// <summary>
	/// the value to check against <see cref="ConfigOption"/>
	/// </summary>
	public object CompareValue { get; init; }
	
	/// <summary>
	/// 
	/// </summary>
	/// <param name="configOption"></param>
	/// <param name="compareValue"></param>
	/// <param name="type"></param>
	public DataStoreConfigurationOptionSelector(string configOption, object compareValue, DataStoreConfigurationOptionCompareType type = DataStoreConfigurationOptionCompareType.Equals)
	{
		Type = type;
		ConfigOption = configOption;
		CompareValue = compareValue;
	}
}