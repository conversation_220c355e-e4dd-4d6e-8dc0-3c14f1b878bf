using Levelbuild.Core.DataStoreInterface.Dto.Interfaces;

namespace Levelbuild.Core.DataStoreInterface.Dto;

/// <summary>
/// Configuration object for one customer
/// </summary>
public class DataStoreContext : IDataStoreContext
{
    /// <summary>
    /// Unique identifier for customer
    /// </summary>
    public string Identifier { get; init; }
	
    /// <summary>
    /// Optional description for customer
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="identifier"></param>
    public DataStoreContext(string identifier)
    {
        Identifier = identifier;
    }
}