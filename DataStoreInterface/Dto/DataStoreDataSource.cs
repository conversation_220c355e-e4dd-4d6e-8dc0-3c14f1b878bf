using Levelbuild.Core.DataStoreInterface.Dto.Interfaces;

namespace Levelbuild.Core.DataStoreInterface.Dto;

/// <summary>
/// DTO reflecting a data source (aka "table") and its available fields (aka "table columns")
/// </summary>
public class DataStoreDataSource : IDataStoreDataSource
{
    /// <summary>
    /// Data source name
    /// </summary>
    public string Name { get; init; }
    
    /// <summary>
    /// Available fields
    /// </summary>
    public IList<IDataStoreField> Fields { get; init; }

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="name">Data source name</param>
    /// <param name="fields">Available fields</param>
    public DataStoreDataSource(string name, IList<IDataStoreField> fields)
    {
        Name = name;
        Fields = fields;
    }
}