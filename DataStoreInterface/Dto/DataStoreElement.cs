using System.Text.Json.Serialization;
using Levelbuild.Core.DataStoreInterface.Dto.Interfaces;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.SharedDtos.Attributes;

namespace Levelbuild.Core.DataStoreInterface.Dto;

/// <summary>
/// Represents a single dataset
/// </summary>
[PublicApiInclude]
public class DataStoreElement : IResponseObject, IDataStoreElement
{
    /// <summary>
    /// Element Id
    /// </summary>
    public string Id { get; init; }
	
	/// <summary>
	/// Is inactive flag - standard false
	/// </summary>
	public bool IsInactive { get; set; } = false;
	
	/// <summary>
	/// Is favourite flag - standard false
	/// </summary>
	public bool IsFavourite { get; set; } = false;
	
	/// <summary>
	/// Contains all the groups that are allowed to access this element
	/// </summary>
	public IList<string> Groups { get; init; } = new List<string>();

	/// <summary>
    /// Available values referenced by name
    /// </summary>
    public IDictionary<string, object?> Values { get; init; }

	/// <summary>
	/// Available File info (if any)
	/// </summary>
	// ReSharper disable once UnusedAutoPropertyAccessor.Global
	public DataStoreFileInfo? FileInfo { get; set;}
	
	/// <summary>
	/// Constructor necessary for JSON deserialization.
	/// </summary>
	[JsonConstructor]
	public DataStoreElement()
	{
		Id = String.Empty;
		Values = new Dictionary<string, object?>();
	}

	/// <param name="id">Unique identifier</param>
	/// <param name="values">Available values</param>
	/// <param name="groups">Contains all the groups that are allowed to access this element</param>
	public DataStoreElement(string id, IDictionary<string, object?> values, IList<string> groups, bool isInactive = false, bool isFavourite = false)
	{
		Id = id;
		Values = values;
		Groups = groups;
		IsInactive = isInactive;
		IsFavourite = isFavourite;
		
	}

    /// <summary>
    /// Is there a file attached to the dataset?
    /// </summary>
    public bool HasFile()
    {
        return FileInfo != null;
    }
}