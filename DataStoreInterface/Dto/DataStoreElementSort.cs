using Levelbuild.Core.DataStoreInterface.Enum;

namespace Levelbuild.Core.DataStoreInterface.Dto;

/// <summary>
/// Used inside <see cref="DataStoreQuery"/>.OrderBy to tell the DataStore after which column the query has to be sorted and in which direction. 
/// </summary>
public class DataStoreElementSort
{
	/// <summary>
	/// 
	/// </summary>
	/// <param name="obj"></param>
	/// <returns></returns>
	public override bool Equals(object? obj)
	{
		return new DataStoreElementSortEqualityComparer().Equals(this, (DataStoreElementSort?) obj);
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="other"></param>
	/// <returns></returns>
	protected bool Equals(DataStoreElementSort other)
	{
		return new DataStoreElementSortEqualityComparer().Equals(this, other);
	}

	/// <summary>
    /// Field name
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// Should the data be sorted ascending or descending?
    /// </summary>
    public DataStoreElementSortDirection SortDirection { get; init; }

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="name">Field name</param>
    /// <param name="sortDirection">Sort direction</param>
    public DataStoreElementSort(string name, DataStoreElementSortDirection sortDirection = DataStoreElementSortDirection.Asc)
    {
        Name = name;
        SortDirection = sortDirection;
    }
	
	#region Operators
	
	/// <summary>
	/// A comparison.
	/// </summary>
	/// <param name="x"></param>
	/// <param name="y"></param>
	/// <returns>True if equal.</returns>
	public static bool operator ==(DataStoreElementSort? x, DataStoreElementSort? y)
	{
		return new DataStoreElementSortEqualityComparer().Equals(x, y);
	}

	/// <summary>
	/// A negated comparison.
	/// </summary>
	/// <param name="x"></param>
	/// <param name="y"></param>
	/// <returns>False if equal.</returns>
	public static bool operator !=(DataStoreElementSort? x, DataStoreElementSort? y)
	{
		return !new DataStoreElementSortEqualityComparer().Equals(x, y);
	}
	
	/// <summary>
	/// Generates a comparable hash code.
	/// </summary>
	/// <returns></returns>
	public override int GetHashCode()
	{
		return new DataStoreElementSortEqualityComparer().GetHashCode(this);
	}
	
	#endregion
}

/// <summary>
/// 
/// </summary>
public class DataStoreElementSortEqualityComparer : IEqualityComparer<DataStoreElementSort>
{
	/// <summary>
	/// A comparison.
	/// </summary>
	/// <param name="x"></param>
	/// <param name="y"></param>
	/// <returns>True if equal</returns>
	public bool Equals(DataStoreElementSort? x, DataStoreElementSort? y)
	{
		if (ReferenceEquals(x, y))
			return true;
		if (ReferenceEquals(x, null))
			return false;
		if (ReferenceEquals(y, null))
			return false;
		if (x.GetType() != y.GetType())
			return false;
		
		return GetHashCode(x) == GetHashCode(y);
	}

	/// <summary>
	/// Generates a comparable hash code.
	/// </summary>
	/// <param name="obj"></param>
	/// <returns></returns>
	public int GetHashCode(DataStoreElementSort obj)
	{
		return HashCode.Combine(obj.Name, (int)obj.SortDirection);
	}
}