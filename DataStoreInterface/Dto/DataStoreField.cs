using Levelbuild.Core.DataStoreInterface.Dto.Interfaces;
using Levelbuild.Core.DataStoreInterface.Enum;

namespace Levelbuild.Core.DataStoreInterface.Dto;

/// <summary>
/// DTO reflecting a DataStore field (aka "table column")
/// </summary>
public class DataStoreField : IDataStoreField
{
	/// <summary>
	/// Field name
	/// </summary>
	public string Name { get; init; }

	/// <summary>
	/// Value Type
	/// </summary>
	public DataStoreFieldType Type { get; init; }

	/// <summary>
	/// Maximum length in Bytes (only used for string fields)
	/// </summary>
	public int Length { get; init; }

	/// <summary>
	/// Number of decimal places to persist (only for double values)
	/// </summary>
	public int DecimalPlaces { get; init; }

	/// <summary>
	/// May the value be null?
	/// </summary>
	public bool Nullable { get; init; } = true;

	/// <summary>
	/// If the value is not allowed to be null, which default value should be used?
	/// </summary>
	public object? DefaultValue { get; init; }

	/// <summary>
	/// should the DataStore make sure, that the value is Unique?
	/// </summary>
	public bool Unique { get; init; }

	/// <summary>
	/// Is this field part of the primary key?
	/// </summary>
	public bool PrimaryKey { get; init; }

	/// <summary>
	/// If the field is defined as a multi value field, the value type changes from <see cref="Type"/> to List 
	/// </summary>
	public bool MultiValue { get; init; }

	/// <summary>
	/// Is the field only readable but not writeable?
	/// </summary>
	public bool Readonly { get; init; }

	/// <summary>
	/// Can this field be translated? Should return translated field values based on user language and default language, filters should take the user language and default field language into account.
	/// </summary>
	public bool Translatable { get; init; }

	/// <summary>
	/// If <see cref="Translatable"/>:
	/// Which languages are supported by this field.
	/// </summary>
	public IList<string> Languages { get; init; } = new List<string>();

	/// <summary>
	/// Constructor
	/// </summary>
	/// <param name="name">Field name</param>
	public DataStoreField(string name)
	{
		Name = name;
	}
}