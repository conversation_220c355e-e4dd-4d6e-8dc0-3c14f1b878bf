using System.Text.Json.Serialization;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.SharedDtos.Attributes;

// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Core.DataStoreInterface.Dto;

/// <summary>
/// Contains basic information about a file attached to a <see cref="DataStoreElement">dataset</see>.
/// </summary>
[PublicApiInclude]
public class DataStoreFileInfo
{
    /// <summary>
    /// FileId
    /// </summary>
    public string Id { get; init; }

    /// <summary>
    /// Filename
    /// </summary>
    public string Name { get; init; }

    /// <summary>
    /// Filesize in bytes
    /// </summary>
    public long Size { get; init; }
	
	/// <summary>
	/// is this a real file or a link to another element
	/// </summary>
	[PublicApiExclude]
	public long IsLink { get; init; }
	
	/// <summary>
	/// Constructor necessary for JSON deserialization.
	/// </summary>
	[JsonConstructor]
	public DataStoreFileInfo()
	{
		Id = String.Empty;
		Name = String.Empty;
		Size = default;
		IsLink = default;
	}
	
    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="id">FileId</param>
    /// <param name="name">Filename</param>
    /// <param name="size">Filesize in bytes</param>
    public DataStoreFileInfo(string id, string name, long size)
    {
        Id = id;
        Name = name;
        Size = size;
    }

	public FileInfoDto ToDto(Guid dataSourceId, string elementId)
	{
		return new FileInfoDto
		{
			DataSourceId = dataSourceId,
			ElementId = elementId,
			FileId = Id,
			FileName = Name,
			FileSize = Size
		};
	}
}