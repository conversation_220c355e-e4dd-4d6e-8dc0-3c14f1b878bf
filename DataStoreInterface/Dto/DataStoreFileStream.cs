
// ReSharper disable UnusedAutoPropertyAccessor.Global
namespace Levelbuild.Core.DataStoreInterface.Dto;

/// <summary>
/// DataStore specific FileStream containing additional information like the original file date and which may be extended in the future. 
/// </summary>
public class DataStoreFileStream : Stream
{
    /// <summary>
    /// File name
    /// </summary>
    public string Name { get; init; }

    /// <summary>
    /// File date
    /// </summary>
    public DateTime Date { get; init; }

	private long _localLength;
	
    private readonly Stream _stream;

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="name">File name</param>
    /// <param name="date">File date</param>
    /// <param name="length">File length in bytes</param>
    /// <param name="stream">File stream</param>
    public DataStoreFileStream(string name, DateTime date, Stream stream, long length = 0)
    {
        Name = name;
        Date = date;
        _stream = stream;
		_localLength = length;
	}

    /// <inheritdoc/>
    public override void Flush()
    {
        _stream.Flush();
    }
	
	/// <inheritdoc/>
	public override void Close()
	{
		try
		{
			_stream.Close();
		}
		catch (Exception e)
		{
			// this exception might occur without anything particularly bad happening
			// - the input steam was just not fully read, but the request has already been cancelled so this is the correct behaviour.
			if (e.Message.Contains("Bad CRC32 in GZIP stream."))
			{
				throw new Exception("This exception is expected when the request has been canceled prematurely.", e);
			}

			throw;
		}
	}

	/// <inheritdoc/>
    public override int Read(byte[] buffer, int offset, int count)
    {
        return _stream.Read(buffer, offset, count);
    }

	/// <inheritdoc/>
    public override long Seek(long offset, SeekOrigin origin)
    {
        return _stream.Seek(offset, origin);
    }

	/// <inheritdoc/>
    public override void SetLength(long value)
    {
		_localLength = value;
    }

	/// <inheritdoc/>
    public override void Write(byte[] buffer, int offset, int count)
    {
        _stream.Write(buffer, offset, count);
    }
    
	/// <inheritdoc/>
    public override bool CanRead => _stream.CanRead;
	/// <inheritdoc/>
    public override bool CanSeek => _stream.CanSeek;
	/// <inheritdoc/>
    public override bool CanWrite => _stream.CanWrite;
	/// <inheritdoc/>
    public override long Length => (_localLength == 0) ? _stream.Length : _localLength;
    
	/// <inheritdoc/>
    public override long Position
    {
        get => _stream.Position;
        set => _stream.Position = value;
    }
}