using Levelbuild.Core.FrontendDtos.Shared;
using Microsoft.Extensions.Localization;
// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Core.DataStoreInterface.Dto;

/// <summary>
/// Describes the implementation as well as the available configuration options.
/// </summary>
public class DataStoreInfo : IResponseObject
{
    /// <summary>
    /// Backend name
    /// </summary>
    public string Name { get; init; }

    /// <summary>
    /// Backend description
    /// </summary>
    public string Description { get; init; }
    
    /// <summary>
    /// Backend implementation Author(s)
    /// </summary>
    public string? Authors { get; init; }
    
    /// <summary>
    /// Company responsible for this implementation
    /// </summary>
    public string? Company { get; init; }

    /// <summary>
    /// Backend implementation version
    /// </summary>
    public Version Version { get; init; }

    /// <summary>
    /// Configuration Options, divided into groups for better overview
    /// </summary>
    public IList<DataStoreConfigurationGroup> ConfigurationGroups { get; init; }
    
    /// <summary>
    /// new DataStoreConfiguration instance
    /// </summary>
    /// <param name="name">Backend name</param>
    /// <param name="description">Backend description</param>
    /// /// <param name="version">Backend implementation version</param>
    /// <param name="configurationGroups">Configuration Groups</param>
    public DataStoreInfo(string name, string description, Version version, IList<DataStoreConfigurationGroup> configurationGroups)
    {
        Name = name;
        Description = description;
        Version = version;
        ConfigurationGroups = configurationGroups;
    }

	/// <summary>
	/// Translates the entry.
	/// </summary>
	/// <param name="localizer"></param>
	public void Localize(IStringLocalizer localizer)
	{
		foreach (var configurationGroup in ConfigurationGroups)
		{
			configurationGroup.Localize(localizer);
		}
	}
}