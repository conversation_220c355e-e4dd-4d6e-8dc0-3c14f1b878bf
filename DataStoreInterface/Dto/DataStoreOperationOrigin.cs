using Levelbuild.Core.DataStoreInterface.Enum;
// Re<PERSON><PERSON>per disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Core.DataStoreInterface.Dto;

/// <summary>
/// Dataset operations may be triggered by a different sources like human interactions or periodic tasks.
/// The DataStoreOperationOrigin contains all information about this operation origin in a generic format.
/// </summary>
public class DataStoreOperationOrigin
{
    /// <summary>
    /// Type of origin (who or what triggered the current action)
    /// </summary>
    public DataStoreOperationOriginType Type { get; init; }

    /// <summary>
    /// ID of the initiating element (workflow id, periodic task id...) 
    /// </summary>
    public long InitiatorId { get; init; }
    
    /// <summary>
    /// Name of the initiating element (workflow name, periodic task name...) 
    /// </summary>
    public string InitiatorName { get; init; }
    
    /// <summary>
    /// ID of the initiating action if any (workflow action id, periodic task action id...) 
    /// </summary>
    public long ActionId { get; init; }
    
    /// <summary>
    /// Name of the initiating action if any (workflow action name...) 
    /// </summary>
    public string? ActionName { get; init; }
	
	/// <summary>
	/// Optional comment for this operation
	/// </summary>
	public string? Comment { get; init; }

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="type">Type of origin</param>
	/// <param name="initiatorId">ID of the initiating element</param>
	/// <param name="initiatorName">Name of the initiating element</param>
    public DataStoreOperationOrigin(DataStoreOperationOriginType type, long initiatorId, string initiatorName)
    {
        Type = type;
		InitiatorId = initiatorId;
		InitiatorName = initiatorName;
	}
}