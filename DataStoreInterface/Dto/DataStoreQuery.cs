using Levelbuild.Core.DataStoreInterface.Filter;

namespace Levelbuild.Core.DataStoreInterface.Dto;

/// <summary>
/// Used to filter datasets inside a <see cref="DataStoreDataSource"/> with the help of a <see cref="DataStoreQuery"/>.
/// </summary>
public class DataStoreQuery
{
	/// <summary>
    /// Data source name
    /// </summary>
    public string DataSourceName { get; set; }

    /// <summary>
    /// Fields which need to be selected
    /// </summary>
    public IList<DataStoreQueryField>? Fields { get; private set; }

    /// <summary>
    /// Filters which need to be applied
    /// </summary>
    public QueryFilterGroup? Filter { get; private set; }

    /// <summary>
    /// Field names / Alias names which should be used for grouping the result set
    /// </summary>
    public IList<string>? GroupBy { get; private set; }

    /// <summary>
    /// Sorting which should be applied
    /// </summary>
    public IList<DataStoreElementSort>? OrderBy { get; private set; }
    
    /// <summary>
    /// Paging: Limit
    /// </summary>
    public int Limit { get; private set; }
    
    /// <summary>
    /// Paging: Offset
    /// </summary>
    public int Offset { get; private set; }

    /// <summary>
    /// If CountAll=true and limit is set and resultSet length = limit, we need an extra count to display proper paging
    /// </summary>
    public bool CountAll { get; private set; }
	
	/// <summary>
	/// Constructor
	/// </summary>
	/// <param name="fields">Fields which should be selected</param>
	public DataStoreQuery(List<DataStoreQueryField>? fields)
	{
		DataSourceName = string.Empty;
		Fields = fields;
	}
	
    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="dataSourceName">Data source name</param>
    /// <param name="fields">Fields which should be selected</param>
    public DataStoreQuery(string dataSourceName, List<DataStoreQueryField>? fields)
    {
        DataSourceName = dataSourceName;
        Fields = fields;
    }
    
    /// <summary>
    /// Set filter conditions
    /// </summary>
    /// <param name="filter"><see cref="QueryFilterGroup"/> containing filter conditions and possibly nested filter groups.</param>
    /// <returns>returns <see cref="DataStoreQuery">this</see> to support chaining</returns>
    public DataStoreQuery WithFilter(QueryFilterGroup filter)
    {
        Filter = filter;
        return this;
    }

    /// <summary>
    /// group the result set by field/alias names
    /// </summary>
    /// <param name="groupBy">List of field/alias names</param>
    /// <returns>returns <see cref="DataStoreQuery">this</see> to support chaining</returns>
    public DataStoreQuery WithGroupBy(IList<string> groupBy)
    {
        GroupBy = groupBy;
        return this;
    }
    
    /// <summary>
    /// Set sorting
    /// </summary>
    /// <param name="orderBy">List of <see cref="DataStoreElementSort"/></param>
    /// <returns>returns <see cref="DataStoreQuery">this</see> to support chaining</returns>
    public DataStoreQuery WithOrderBy(IList<DataStoreElementSort> orderBy)
    {
        OrderBy = orderBy;
        return this;
    }
    
    /// <summary>
    /// Set paging
    /// </summary>
    /// <param name="limit">maximum number of rows</param>
    /// <param name="offset">skip offset values</param>
    /// <returns>returns <see cref="DataStoreQuery">this</see> to support chaining</returns>
    public DataStoreQuery WithPaging(int limit, int offset = 0)
    {
        Limit = limit;
        Offset = offset;
        return this;
    }

    /// <summary>
    /// Set CountAll flag
    /// </summary>
    /// <returns>returns <see cref="DataStoreQuery">this</see> to support chaining</returns>
    public DataStoreQuery WithCountAll()
    {
        CountAll = true;
        return this;
    }
	
	/// <summary>
	/// Set fields
	/// </summary>
	/// <param name="fields">Fields to set</param>
	/// <returns>returns <see cref="DataStoreQuery">this</see> to support chaining</returns>
	public DataStoreQuery WithFields(IList<DataStoreQueryField> fields)
	{
		Fields = fields;
		return this;
	}
	
	/// <summary>
	/// Add additional fields
	/// </summary>
	/// <param name="fields">Fields to add</param>
	public void AddFields(ICollection<DataStoreQueryField> fields)
	{
		if (Fields == null)
			Fields = new List<DataStoreQueryField>(fields);
		
		((List<DataStoreQueryField>) Fields).AddRange(fields);
	}

	#region Operators
	
	/// <summary>
	/// Equals function override for objects
	/// </summary>
	/// <param name="obj"></param>
	/// <returns></returns>
	public override bool Equals(object? obj)
	{
		return new DataStoreQueryEqualityComparer().Equals(this, (DataStoreQuery?)obj);
	}
	
	/// <summary>
	/// A comparison.
	/// </summary>
	/// <param name="x"></param>
	/// <param name="y"></param>
	/// <returns>True if equal.</returns>
	public static bool operator ==(DataStoreQuery? x, DataStoreQuery? y)
	{
		return new DataStoreQueryEqualityComparer().Equals(x, y);
	}

	/// <summary>
	/// A negated comparison.
	/// </summary>
	/// <param name="x"></param>
	/// <param name="y"></param>
	/// <returns>False if equal.</returns>
	public static bool operator !=(DataStoreQuery? x, DataStoreQuery? y)
	{
		return !new DataStoreQueryEqualityComparer().Equals(x, y);
	}
	
	/// <summary>
	/// Generates a comparable hash code.
	/// </summary>
	/// <returns></returns>
	public override int GetHashCode()
	{
		return new DataStoreQueryEqualityComparer().GetHashCode(this);
	}

	#endregion
	
}

/// <summary>
/// Comparer for DataStoreQuery
/// </summary>
public class DataStoreQueryEqualityComparer : IEqualityComparer<DataStoreQuery>
{
	/// <summary>
	/// A comparison.
	/// </summary>
	/// <param name="x"></param>
	/// <param name="y"></param>
	/// <returns>True if equal</returns>
	public bool Equals(DataStoreQuery? x, DataStoreQuery? y)
	{
		if (ReferenceEquals(x, y))
			return true;
		
		if (ReferenceEquals(x, null) || ReferenceEquals(y, null))
			return false;
		
		if (x.GetType() != y.GetType())
			return false;

		if (ReferenceEquals(x.Fields, null) && !ReferenceEquals(y.Fields, null))
			return false;
		
		if (!ReferenceEquals(x.Fields, null) && ReferenceEquals(y.Fields, null))
			return false;
		
		if (ReferenceEquals(x.GroupBy, null) && !ReferenceEquals(y.GroupBy, null))
			return false;
		
		if (!ReferenceEquals(x.GroupBy, null) && ReferenceEquals(y.GroupBy, null))
			return false;
		
		if (ReferenceEquals(x.OrderBy, null) && !ReferenceEquals(y.OrderBy, null))
			return false;
		
		if (!ReferenceEquals(x.OrderBy, null) && ReferenceEquals(y.OrderBy, null))
			return false;

		var fieldsEqual = (ReferenceEquals(x.Fields, null) && ReferenceEquals(y.Fields, null)) || 
						   x.Fields!.SequenceEqual(y.Fields!, new DataStoreQueryFieldEqualityComparer());
		
		var groupingsEqual = (ReferenceEquals(x.GroupBy, null) && ReferenceEquals(y.GroupBy, null)) || 
						  x.GroupBy!.SequenceEqual(y.GroupBy!);
		
		var sortingsEqual = (ReferenceEquals(x.OrderBy, null) && ReferenceEquals(y.OrderBy, null)) || 
							 x.OrderBy!.SequenceEqual(y.OrderBy!, new DataStoreElementSortEqualityComparer());
		
		return x.DataSourceName == y.DataSourceName && 
			   fieldsEqual && 
			   x.Filter == y.Filter && 
			   groupingsEqual && 
			   sortingsEqual && 
			   x.Limit == y.Limit && 
			   x.Offset == y.Offset && 
			   x.CountAll == y.CountAll;
	}

	/// <summary>
	/// Generates a comparable hash code.
	/// </summary>
	/// <param name="obj"></param>
	/// <returns></returns>
	public int GetHashCode(DataStoreQuery obj)
	{
		HashCode fieldsHash = new(), 
				 groupingsHash = new(), 
				 sortingsHash = new();
		if (obj.Fields != null)
		{
			foreach (var field in obj.Fields)
			{
				fieldsHash.Add(field.GetHashCode());
			}
		}

		if (obj.GroupBy != null)
		{
			foreach (var grouping in obj.GroupBy)
			{
				groupingsHash.Add(grouping.GetHashCode());
			}
		}

		if (obj.OrderBy != null)
		{
			foreach (var sorting in obj.OrderBy)
			{
				sortingsHash.Add(sorting.GetHashCode());
			}
		}

		return HashCode.Combine(obj.DataSourceName, fieldsHash.ToHashCode(), obj.Filter?.GetHashCode(), groupingsHash.ToHashCode(), sortingsHash.ToHashCode(), obj.Limit, obj.Offset, obj.CountAll);
	}
}