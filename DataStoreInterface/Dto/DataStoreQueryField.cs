
namespace Levelbuild.Core.DataStoreInterface.Dto;

/// <summary>
/// A field which needs to be selected from a <see cref="DataStoreDataSource"/> with the help of a <see cref="DataStoreQuery"/>.
/// Name may contain aggregate functions as well as simple mathematical operations. In this case an alias needs to be set.
/// </summary>
public class DataStoreQueryField
{
	/// <summary>
	/// Equals override for DataStoreQueryField
	/// </summary>
	/// <param name="other"></param>
	/// <returns></returns>
	protected bool Equals(DataStoreQueryField other)
	{
		return new DataStoreQueryFieldEqualityComparer().Equals(this, other);
	}

	/// <summary>
	/// Equals override for objects
	/// </summary>
	/// <param name="obj"></param>
	/// <returns></returns>
	public override bool Equals(object? obj)
	{
		return new DataStoreQueryFieldEqualityComparer().Equals(this, (DataStoreQueryField?)obj);
	}

	/// <summary>
    /// Name of the field or formula which describes what needs to be selected
    /// </summary>
    public string Name { get; init; }
    
    /// <summary>
    /// optional alias for the selected column
    /// </summary>
    public string? Alias { get; init; }

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="name">Field name or formula</param>
    /// <param name="alias">Column alias</param>
    public DataStoreQueryField(string name, string? alias = null)
    {
        Name = name;
        Alias = alias;
    }

	#region Operators
	
	/// <summary>
	/// A comparison.
	/// </summary>
	/// <param name="x"></param>
	/// <param name="y"></param>
	/// <returns>True if equal.</returns>
	public static bool operator ==(DataStoreQueryField? x, DataStoreQueryField? y)
	{
		return new DataStoreQueryFieldEqualityComparer().Equals(x, y);
	}

	/// <summary>
	/// A negated comparison.
	/// </summary>
	/// <param name="x"></param>
	/// <param name="y"></param>
	/// <returns>False if equal.</returns>
	public static bool operator !=(DataStoreQueryField? x, DataStoreQueryField? y)
	{
		return !new DataStoreQueryFieldEqualityComparer().Equals(x, y);
	}
	
	/// <summary>
	/// Generates a comparable hash code.
	/// </summary>
	/// <returns></returns>
	public override int GetHashCode()
	{
		return new DataStoreQueryFieldEqualityComparer().GetHashCode(this);
	}
	
	#endregion
}

/// <summary>
/// Comparer for DataStoreQueryField
/// </summary>
public class DataStoreQueryFieldEqualityComparer : IEqualityComparer<DataStoreQueryField>
{
	/// <summary>
	/// Comparison
	/// </summary>
	/// <param name="x"></param>
	/// <param name="y"></param>
	/// <returns>True if equal</returns>
	public bool Equals(DataStoreQueryField? x, DataStoreQueryField? y)
	{
		if (ReferenceEquals(x, y))
			return true;
		if (ReferenceEquals(x, null))
			return false;
		if (ReferenceEquals(y, null))
			return false;
		if (x.GetType() != y.GetType())
			return false;

		return GetHashCode(x) == GetHashCode(y);
	}

	/// <summary>
	/// Generates a comparable hash code.
	/// </summary>
	/// <param name="obj"></param>
	/// <returns></returns>
	public int GetHashCode(DataStoreQueryField obj)
	{
		return HashCode.Combine(obj.Name, obj.Alias);
	}
}