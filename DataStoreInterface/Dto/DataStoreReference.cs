
// ReSharper disable UnusedAutoPropertyAccessor.Global
namespace Levelbuild.Core.DataStoreInterface.Dto;

/// <summary>
/// Represents a reference to a <see cref="DataStoreElement"/>
/// </summary>
public class DataStoreReference
{
	/// <summary>
	/// Name of the data source
	/// </summary>
	public string DataSourceName { get; init; }
	
    /// <summary>
    /// Element Id
    /// </summary>
    public string ElementId { get; init; }

	/// <summary>
	/// if empty, references always the latest file version of the target element
	/// if not empty, references a specific file version
	/// </summary>
	public string? FileId { get; init; }

	/// <summary>
	/// Constructor
	/// </summary>
	/// <param name="dataSourceName">referenced data source name</param>
	/// <param name="elementId">referenced element id</param>
	/// <param name="fileId">optional referenced file id (if this specific file revision should be kept)</param>
	public DataStoreReference(string dataSourceName, string elementId, string? fileId = null)
	{
		DataSourceName = dataSourceName;
		ElementId = elementId;
		FileId = fileId;
	}
}