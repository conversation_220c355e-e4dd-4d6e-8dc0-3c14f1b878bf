using Levelbuild.Core.DataStoreInterface.Dto.Interfaces;

namespace Levelbuild.Core.DataStoreInterface.Dto;

/// <summary>
/// A List which contains <see cref="IDataStoreElement"/>s as well as a total count
/// </summary>
/// <typeparam name="T"></typeparam>
public class DataStoreResultSet<T> : List<T> where T : IDataStoreElement
{
    /// <summary>
    /// If paging is used, CountAll contains the total number of available elements without paging
    /// </summary>
    public int CountTotal { get; init; }
  
    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="countTotal">Total number of available elements</param>
    public DataStoreResultSet(int countTotal)
    {
        CountTotal = countTotal;
    }
}