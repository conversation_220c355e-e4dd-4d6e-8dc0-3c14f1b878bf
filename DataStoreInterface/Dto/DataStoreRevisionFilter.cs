using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.DataStoreInterface.Filter;
// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Core.DataStoreInterface.Dto;

/// <summary>
/// Filter object used to filter revisions of a single dataset
/// </summary>
public class DataStoreRevisionFilter
{
    /// <summary>
    /// Property to filter
    /// </summary>
    public DataStoreRevisionProperty Property { get; init; }
    
    /// <summary>
    /// Compare operation which should be used
    /// </summary>
    public QueryFilterOperator Operator { get; init; }

    /// <summary>
    /// Compare value
    /// </summary>
    public object? CompareValue { get; init; }

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="property">Property to filter</param>
    /// <param name="operator">Compare operation</param>
    /// <param name="compareValue">Compare value</param>
    public DataStoreRevisionFilter(DataStoreRevisionProperty property, QueryFilterOperator @operator,
        object? compareValue = null)
    {
        Property = property;
        CompareValue = compareValue;
        Operator = @operator;
    }
}