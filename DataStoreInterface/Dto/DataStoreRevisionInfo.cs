using Levelbuild.Core.DataStoreInterface.Dto.Interfaces;
using Levelbuild.Core.DataStoreInterface.Enum;
// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Core.DataStoreInterface.Dto;

/// <summary>
/// Contains the metadata of a single revision
/// </summary>
public class DataStoreRevisionInfo : IDataStoreElement
{
	/// <summary>
	/// Revision Id
	/// </summary>
	public string Id { get; init; }

	/// <summary>
	/// Contains all the groups that are allowed to access this element
	/// </summary>
	public IList<string> Groups { get; init; } = new List<string>();

	/// <summary>
	/// Revision Date
	/// </summary>
	public DateTime Date { get; init; }

	/// <summary>
	/// User who changed the dataset
	/// </summary>
	public string Username { get; init; }

	/// <summary>
	/// Revision comment
	/// </summary>
	public string? Comment { get; init; }

	/// <summary>
	/// Operation Type (Create, Update, Delete)
	/// </summary>
	public DataStoreOperationType Type { get; init; }

	/// <summary>
	/// Origin Type (who initiated the manipulation of the dataset)
	/// </summary>
	public DataStoreOperationOriginType OriginType { get; init; }

	/// <summary>
	/// ID of the initiating element (workflow id, periodic task id...) 
	/// </summary>
	public long InitiatorId { get; init; }

	/// <summary>
	/// Name of the initiating element (workflow name, periodic task name...) 
	/// </summary>
	public string? InitiatorName { get; init; }

	/// <summary>
	/// ID of the initiating action if any (workflow action id, periodic task action id...) 
	/// </summary>
	public long ActionId { get; init; }

	/// <summary>
	/// Name of the initiating action if any (workflow action name...) 
	/// </summary>
	public string? ActionName { get; init; }

	/// <summary>
	/// Indicates if file was changed in this revision
	/// </summary>
	public bool HasFile { get; init; } = false;

	/// <summary>
	/// Constructor
	/// </summary>
	/// <param name="id">Revision Id</param>
	/// <param name="date">Revision Date</param>
	/// <param name="username">Revision User</param>
	/// <param name="type">Operation type</param>
	public DataStoreRevisionInfo(string id, DateTime date, string username, DataStoreOperationType type)
	{
		Id = id;
		Date = date;
		Username = username;
		Type = type;
	}
}