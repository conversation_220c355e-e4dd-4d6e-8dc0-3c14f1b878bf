using Levelbuild.Core.DataStoreInterface.Enum;
// Re<PERSON><PERSON>per disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Core.DataStoreInterface.Dto;

/// <summary>
/// Used to filter revisions of a single dataset
/// </summary>
public class DataStoreRevisionQuery
{
    /// <summary>
    /// Data source name
    /// </summary>
    public string DataSourceName { get; init; }
        
    /// <summary>
    /// Element Id
    /// </summary>
    public string ElementId { get; init; }

    /// <summary>
    /// Filter conditions
    /// </summary>
    public IList<DataStoreRevisionFilter> Filters { get; private set; } = new List<DataStoreRevisionFilter>();
    
    /// <summary>
    /// sortings
    /// </summary>
    public IList<DataStoreRevisionSort> OrderBy { get; private set; } = new List<DataStoreRevisionSort>();
    
    /// <summary>
    /// Paging: Limit
    /// </summary>
    public int Limit { get; private set; }

    /// <summary>
    /// Paging: Offset
    /// </summary>
    public int Offset { get; private set; }
    
    /// <summary>
    /// If CountAll=true and limit is set and resultSet length = limit, we need an extra count to display proper paging
    /// </summary>
    public bool CountAll { get; private set; }

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="dataSourceName">Data source name</param>
    /// <param name="elementId">Element Id</param>
    public DataStoreRevisionQuery(string dataSourceName, string elementId)
    {
        DataSourceName = dataSourceName;
        ElementId = elementId;
    }
    
    /// <summary>
    /// Set filter conditions
    /// </summary>
    /// <param name="filters">List of <see cref="DataStoreRevisionQuery"/></param>
    /// <returns>returns <see cref="DataStoreRevisionQuery">this</see> to support chaining</returns>
    public DataStoreRevisionQuery WithFilters(IList<DataStoreRevisionFilter> filters)
    {
        Filters = filters;
        return this;
    }
    
    /// <summary>
    /// Set sortings
    /// </summary>
    /// <param name="orderBy">List of <see cref="DataStoreRevisionProperty"/></param>
    /// <returns>returns <see cref="DataStoreRevisionQuery">this</see> to support chaining</returns>
    public DataStoreRevisionQuery WithOrderBy(IList<DataStoreRevisionSort> orderBy)
    {
        OrderBy = orderBy;
        return this;
    }

    /// <summary>
    /// Set paging
    /// </summary>
    /// <param name="limit">maximum number of rows</param>
    /// <param name="offset">skip offset values</param>
    /// <returns>returns <see cref="DataStoreRevisionQuery">this</see> to support chaining</returns>
    public DataStoreRevisionQuery WithPaging(int limit, int offset = 0)
    {
        Limit = limit;
        Offset = offset;
        return this;
    }
    
    /// <summary>
    /// Set CountAll flag
    /// </summary>
    /// <returns>returns <see cref="DataStoreRevisionQuery">this</see> to support chaining</returns>
    public DataStoreRevisionQuery WithCountAll()
    {
        CountAll = true;
        return this;
    }
}