using Levelbuild.Core.DataStoreInterface.Enum;
// Re<PERSON><PERSON>per disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Core.DataStoreInterface.Dto;

/// <summary>
/// Used inside <see cref="DataStoreRevisionQuery"/>.OrderBy to tell the DataStore after which properties the revisions need to be sorted and in which direction.  
/// </summary>
public class DataStoreRevisionSort
{
	/// <summary>
	/// property by which to sort
	/// </summary>
	public DataStoreRevisionProperty Property { get; init; }

	/// <summary>
	/// Should the data be sorted ascending or descending?
	/// </summary>
	public DataStoreElementSortDirection SortDirection { get; init; }

	/// <summary>
	/// Constructor
	/// </summary>
	/// <param name="property">Property</param>
	/// <param name="sortDirection">Sort direction</param>
	public DataStoreRevisionSort(DataStoreRevisionProperty property, DataStoreElementSortDirection sortDirection = DataStoreElementSortDirection.Asc)
	{
		Property = property;
		SortDirection = sortDirection;
	}
}