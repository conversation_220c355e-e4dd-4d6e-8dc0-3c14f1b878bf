using Levelbuild.Core.DataStoreInterface.Enum;
// Re<PERSON><PERSON>per disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Core.DataStoreInterface.Dto;

/// <summary>
/// Reflects the value of one field inside a specific revision.
/// If the field value changed in this revision, the object also contains the previous value for comparison.
/// </summary>
public class DataStoreRevisionValue
{
    /// <summary>
    /// Field name
    /// </summary>
    public string Name { get; init; }
    
    /// <summary>
    /// Field type
    /// </summary>
    public DataStoreFieldType Type { get; init; }
    
    /// <summary>
    /// Field value
    /// </summary>
    public object? Value { get; init; }

    /// <summary>
    /// has the value changed from the previous revision to this revision?
    /// </summary>
    public bool Changed { get; init; } = false;
    
    /// <summary>
    /// IF the value changed, this property contains the previous value for comparison
    /// </summary>
    public object? PreviousValue { get; init; }

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="name">Field name</param>
    /// <param name="type">Field type</param>
    /// <param name="value">Field value</param>
    public DataStoreRevisionValue(string name, DataStoreFieldType type, object? value)
    {
        Name = name;
        Type = type;
        Value = value;
    }
}