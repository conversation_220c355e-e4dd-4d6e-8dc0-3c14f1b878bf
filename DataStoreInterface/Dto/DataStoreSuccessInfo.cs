using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.SharedDtos;
// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Core.DataStoreInterface.Dto;

/// <summary>
/// Success object returned by every operation which creates or updates a dataset
/// </summary>
public class DataStoreSuccessInfo
{
    /// <summary>
    /// ID of the created/manipulated/found element
    /// </summary>
    public string ElementId { get; init; }
    
    /// <summary>
    /// Which type of operation was executed?
    /// </summary>
    public DataStoreOperationType OperationType { get; init; }
    
    /// <summary>
    /// May contain the resulting element (should only be set if it has no negative performance impact)
    /// </summary>
    public DataStoreElement? ElementData { get; init; }

    /// <summary>
    /// May contain a performance breakdown of the operation from within the <see cref="IDataStore"/> implementation. 
    /// </summary>
    public PerformanceMetrics? PerformanceInfo { get; init; }

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="elementId">ID of the created/manipulated element</param>
    /// <param name="operationType">Which type of operation was executed?</param>
    public DataStoreSuccessInfo(string elementId, DataStoreOperationType operationType)
    {
        ElementId = elementId;
        OperationType = operationType;
    }
}