namespace Levelbuild.Core.DataStoreInterface.Dto.Interfaces;

/// <summary>
/// Minimum information needed to authenticate against a <see cref="IDataStore"/>.
/// </summary>
public interface IDataStoreAuthentication
{
    /// <summary>
    /// Name of the user who wants to use the connection (needed for example to write proper revisions)
    /// </summary>
    public string Username { get; init; }
	
	/// <summary>
	/// Guid of the user who wants to use the connection (needed for example to write proper revisions)
	/// </summary>
	public Guid UserId { get; init; }

	/// <summary>
	/// The user language.
	/// </summary>
	public string Language { get; init; }
	
	/// <summary>
	/// Contains all the groups this user is a member of. Can be used by the backend to limit access to the data.
	/// </summary>
	public IList<string>? Groups { get; init; }
	
    /// <summary>
    /// Returns a Hash which depends on all information that are used to establish the connection.
    /// May be used to implement Connection pooling.
    /// </summary>
    /// <returns>Hash</returns>
    public string GetHash();
}