namespace Levelbuild.Core.DataStoreInterface.Dto.Interfaces;

/// <summary>
/// Interface is for internal use only! Please use the <see cref="DataStoreDataSource"/> DTO whenever possible.
/// </summary>
public interface IDataStoreDataSource
{
    /// <summary>
    /// Name of the data source
    /// </summary>
    public string Name { get; init; }
    
    /// <summary>
    /// List of fields in data source
    /// </summary>
    public IList<IDataStoreField> Fields { get; init; } 
}