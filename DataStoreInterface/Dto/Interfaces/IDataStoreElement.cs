namespace Levelbuild.Core.DataStoreInterface.Dto.Interfaces;

/// <summary>
/// Interface is only used to enable <see cref="DataStoreResultSet{T}"/> to handle both <see cref="DataStoreElement"/> and <see cref="DataStoreRevisionInfo"/>.
/// </summary>
public interface IDataStoreElement
{
	/// <summary>
	/// Unique Identifier
	/// </summary>
	public string Id { get; init; }

	/// <summary>
	/// All user groups that are allowed to access this element
	/// </summary>
	public IList<string> Groups { get; init; }
}