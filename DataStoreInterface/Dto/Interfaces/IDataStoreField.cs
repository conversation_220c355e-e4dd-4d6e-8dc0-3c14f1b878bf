using Levelbuild.Core.DataStoreInterface.Enum;

namespace Levelbuild.Core.DataStoreInterface.Dto.Interfaces;

/// <summary>
/// Interface is for internal use only! Please use the <see cref="DataStoreField"/> DTO whenever possible.
/// </summary>
public interface IDataStoreField
{
	/// <summary>
	/// Name of the field
	/// </summary>
	public string Name { get; init; }

	/// <summary>
	/// data type of the field
	/// </summary>
	public DataStoreFieldType Type { get; init; }

	/// <summary>
	/// Field length (eg. for string types)
	/// </summary>
	public int Length { get; init; }

	/// <summary>
	/// decimal places for double type
	/// </summary>
	public int DecimalPlaces { get; init; }

	/// <summary>
	/// Field is nullable?
	/// </summary>
	public bool Nullable { get; init; }

	/// <summary>
	/// Default value (especially for not nullable fields)
	/// </summary>
	public object? DefaultValue { get; init; }

	/// <summary>
	/// Field value must be unique?
	/// </summary>
	public bool Unique { get; init; }

	/// <summary>
	/// Is primary key field in data source?
	/// </summary>
	public bool PrimaryKey { get; init; }

	/// <summary>
	/// Field is MultiValue field? / Has Array field type in database or another table?
	/// </summary>
	public bool MultiValue { get; init; }

	/// <summary>
	/// Field is readonly? 
	/// </summary>
	public bool Readonly { get; init; }

	/// <summary>
	/// Field can be translated?
	/// </summary>
	public bool Translatable { get; init; }

	/// <summary>
	/// List containing several supported languages
	/// </summary>
	public IList<string> Languages { get; init; }
}