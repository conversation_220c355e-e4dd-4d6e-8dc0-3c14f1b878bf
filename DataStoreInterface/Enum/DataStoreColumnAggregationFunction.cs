namespace Levelbuild.Core.DataStoreInterface.Enum;

/// <summary>
/// Enum of all supported aggregate functions
/// </summary>
public enum DataStoreColumnAggregationFunction
{
	/// <summary>
	/// Average
	/// </summary>
	Avg,
	/// <summary>
	/// Sum
	/// </summary>
	Sum,
	/// <summary>
	/// Count
	/// </summary>
	Count,
	/// <summary>
	/// Max
	/// </summary>
	Max,
	/// <summary>
	/// Min
	/// </summary>
	Min,
}