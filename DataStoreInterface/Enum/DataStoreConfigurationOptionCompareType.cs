using System.Text.Json.Serialization;

namespace Levelbuild.Core.DataStoreInterface.Enum;

/// <summary>
/// Compare Enum for DataStoreConfigurationOptionCompareType
/// </summary>
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum DataStoreConfigurationOptionCompareType
{
	/// <summary>
	/// Equality type
	/// </summary>
	Equals,
	
	/// <summary>
	/// Not equal type
	/// </summary>
	NotEquals,
}