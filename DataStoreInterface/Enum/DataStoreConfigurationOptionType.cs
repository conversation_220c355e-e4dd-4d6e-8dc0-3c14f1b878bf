using System.Text.Json.Serialization;

namespace Levelbuild.Core.DataStoreInterface.Enum;

/// <summary>
/// Configuration field type
/// </summary>
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum DataStoreConfigurationOptionType
{
    /// <summary>
    /// Config type String
    /// </summary>
    String,
	
	/// <summary>
	/// Config type Int
	/// </summary>
	Int,
	
	/// <summary>
	/// Config type Double
	/// </summary>
	Double,
	
	/// <summary>
	/// Config type Password
	/// </summary>
	Password,
	
	/// <summary>
	/// Config type Text
	/// </summary>
	Text,
	
	/// <summary>
	/// Config type Boolean
	/// </summary>
	Boolean
}