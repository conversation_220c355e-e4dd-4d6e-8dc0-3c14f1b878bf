using System.Text.Json.Serialization;
using Microsoft.OpenApi.Extensions;

namespace Levelbuild.Core.DataStoreInterface.Enum;

/// <summary>
/// Sort Direction
/// </summary>
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum DataStoreElementSortDirection
{
    /// <summary>
    /// Sort direction Asc
    /// </summary>
    Asc, 
	/// <summary>
	/// Sort direction Desc
	/// </summary>
	Desc
}


/// <summary>
/// additional methods for enum
/// </summary>
public static class DataStoreElementSortDirectionExtensions
{
	/// <summary>
	/// 
	/// </summary>
	/// <param name="sortDirection"></param>
	/// <returns></returns>
	public static DataStoreElementSortDirection Parse(string sortDirection)
	{
		if (sortDirection.ToLower().Equals(DataStoreElementSortDirection.Asc.GetDisplayName().ToLower()))
			return DataStoreElementSortDirection.Asc;
		return DataStoreElementSortDirection.Desc;
	}
}