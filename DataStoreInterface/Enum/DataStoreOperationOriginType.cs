namespace Levelbuild.Core.DataStoreInterface.Enum;

/// <summary>
/// Who or what initiated the CRUD action?
/// </summary>
public enum DataStoreOperationOriginType
{
    /// <summary>
    /// Operation initiated by a user input.
    /// </summary>
    User = 0,
    
    /// <summary>
    /// Operation initiated by a index rule action.
    /// </summary>
    IndexRule = 1,
    
    /// <summary>
    /// Operation initiated by a user workflow action.
    /// </summary>
    UserWorkflow = 2,
    
    /// <summary>
    /// Operation initiated by a periodic task.
    /// </summary>
    PeriodicTask = 3,
	
	/// <summary>
	/// Operation initiated via public API (if it was our own app, this should be visible )
	/// </summary>
	PublicApi = 4, 
	
	/// <summary>
	/// Operation initiated by neccessary system internals
	/// </summary>
	System = 5
}