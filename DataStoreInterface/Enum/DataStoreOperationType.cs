namespace Levelbuild.Core.DataStoreInterface.Enum;

/// <summary>
/// Which type of operation was executed?
/// </summary>
public enum DataStoreOperationType
{
    /// <summary>
    /// New dataset created
    /// </summary>
    Create,
    
    /// <summary>
    /// Existing dataset updated with the given values
    /// </summary>
    Update,
    
    /// <summary>
    /// Existing dataset deleted
    /// </summary>
    Delete,
    
    /// <summary>
    /// create skipped because dataset already existed
    /// </summary>
    Skip,
    
    /// <summary>
    /// existing dataset completely overwritten (values not given were set null, files not given wer removed)
    /// </summary>
    Overwrite
}