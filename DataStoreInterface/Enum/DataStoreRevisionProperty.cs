namespace Levelbuild.Core.DataStoreInterface.Enum;

/// <summary>
/// Revision properties which can be used for filtering
/// </summary>
public enum DataStoreRevisionProperty
{
    /// <summary>
    /// Revision property Date
    /// </summary>
    Date, 
	/// <summary>
	/// Revision property Username
	/// </summary>
	Username, 
	/// <summary>
	/// Revision property Comment
	/// </summary>
	Comment, 
	/// <summary>
	/// Revision property Type
	/// </summary>
	Type, 
	/// <summary>
	/// Revision property OriginType
	/// </summary>
	OriginType
}