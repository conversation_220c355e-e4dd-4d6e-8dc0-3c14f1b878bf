namespace Levelbuild.Core.DataStoreInterface.Exceptions;

/// <summary>
/// Exception for errors in context configuration (missing information)
/// </summary>
public class ContextConfigurationException : ApplicationException
{
	/// <summary>
	/// Exception for errors in context configuration
	/// </summary>
	public ContextConfigurationException()
	{
	}

	/// <summary>
	/// Exception for errors in context configuration
	/// </summary>
	/// <param name="message"></param>
	public ContextConfigurationException(string message) : base(message)
	{
	}

	/// <summary>
	/// Exception for errors in context configuration
	/// </summary>
	/// <param name="message"></param>
	/// <param name="inner"></param>
	public ContextConfigurationException(string message, Exception inner) : base(message, inner)
	{
	}
}