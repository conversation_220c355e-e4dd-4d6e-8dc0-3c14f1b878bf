namespace Levelbuild.Core.DataStoreInterface.Exceptions;

/// <summary>
/// Exception for missing Context
/// </summary>
public class ContextNotFoundException : ApplicationException
{
    /// <summary>
    /// Exception for missing Context
    /// </summary>
    public ContextNotFoundException()
    {
    }

    /// <summary>
	/// Exception for missing Context
    /// </summary>
    /// <param name="message"></param>
    public ContextNotFoundException(string message) : base(message)
    {
    }

    /// <summary>
	/// Exception for missing Context
    /// </summary>
    /// <param name="message"></param>
    /// <param name="inner"></param>
    public ContextNotFoundException(string message, Exception inner) : base(message, inner)
    {
    }
}