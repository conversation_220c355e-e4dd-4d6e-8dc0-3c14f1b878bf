namespace Levelbuild.Core.DataStoreInterface.Exceptions;

/// <summary>
/// Exception for missing DataSource
/// </summary>
public class DataSourceNotFoundException : ApplicationException
{
    /// <summary>
	/// Exception for missing DataSource
    /// </summary>
    public DataSourceNotFoundException()
    {
    }

    /// <summary>
	/// Exception for missing DataSource
    /// </summary>
    /// <param name="message"></param>
    public DataSourceNotFoundException(string message) : base(message)
    {
    }

    /// <summary>
	/// Exception for missing DataSource
    /// </summary>
    /// <param name="message"></param>
    /// <param name="inner"></param>
    public DataSourceNotFoundException(string message, Exception inner) : base(message, inner)
    {
    }
}