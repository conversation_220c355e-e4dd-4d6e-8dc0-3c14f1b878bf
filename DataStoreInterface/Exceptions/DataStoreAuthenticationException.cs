namespace Levelbuild.Core.DataStoreInterface.Exceptions;

/// <summary>
/// Exception on authentication error for data store 
/// </summary>
public class DataStoreAuthenticationException : ApplicationException
{
    /// <summary>
    /// Exception on authentication error for data store
    /// </summary>
    public DataStoreAuthenticationException()
    {
    }

    /// <summary>
    /// Exception on authentication error for data store
    /// </summary>
    /// <param name="message"></param>
    public DataStoreAuthenticationException(string message) : base(message)
    {
    }

    /// <summary>
    /// Exception on authentication error for data store
    /// </summary>
    /// <param name="message"></param>
    /// <param name="inner"></param>
    public DataStoreAuthenticationException(string message, Exception inner) : base(message, inner)
    {
    }
}