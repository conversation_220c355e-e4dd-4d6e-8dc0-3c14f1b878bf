namespace Levelbuild.Core.DataStoreInterface.Exceptions;

/// <summary>
/// Exception for missing or incorrect data store configuration
/// </summary>
public class DataStoreConfigurationException : ApplicationException
{
    /// <summary>
    /// Exception for missing or incorrect data store configuration
    /// </summary>
    public DataStoreConfigurationException()
    {
    }

    /// <summary>
    /// Exception for missing or incorrect data store configuration
    /// </summary>
    /// <param name="message"></param>
    public DataStoreConfigurationException(string message) : base(message)
    {
    }

    /// <summary>
    /// Exception for missing or incorrect data store configuration
    /// </summary>
    /// <param name="message"></param>
    /// <param name="inner"></param>
    public DataStoreConfigurationException(string message, Exception inner) : base(message, inner)
    {
    }
}