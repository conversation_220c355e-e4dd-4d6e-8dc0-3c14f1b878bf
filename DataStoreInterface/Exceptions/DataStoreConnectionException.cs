namespace Levelbuild.Core.DataStoreInterface.Exceptions;

/// <summary>
/// Exception for failing connection to data store
/// </summary>
public class DataStoreConnectionException : ApplicationException
{
    /// <summary>
    /// Exception for failing connection to data store
    /// </summary>
    public DataStoreConnectionException()
    {
    }

    /// <summary>
    /// Exception for failing connection to data store
    /// </summary>
    /// <param name="message"></param>
    public DataStoreConnectionException(string message) : base(message)
    {
    }

    /// <summary>
    /// Exception for failing connection to data store
    /// </summary>
    /// <param name="message"></param>
    /// <param name="inner"></param>
    public DataStoreConnectionException(string message, Exception inner) : base(message, inner)
    {
    }
}