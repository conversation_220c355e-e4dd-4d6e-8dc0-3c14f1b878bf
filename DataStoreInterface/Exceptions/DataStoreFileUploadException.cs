namespace Levelbuild.Core.DataStoreInterface.Exceptions;

/// <summary>
/// Exception for failing data store file upload
/// </summary>
public class DataStoreFileUploadException : ApplicationException
{
    /// <summary>
    /// Exception for failing data store file upload
    /// </summary>
    public DataStoreFileUploadException()
    {
    }

    /// <summary>
    /// Exception for failing data store file upload
    /// </summary>
    /// <param name="message"></param>
    public DataStoreFileUploadException(string message) : base(message)
    {
    }

    /// <summary>
    /// Exception for failing data store file upload
    /// </summary>
    /// <param name="message"></param>
    /// <param name="inner"></param>
    public DataStoreFileUploadException(string message, Exception inner) : base(message, inner)
    {
    }
}