namespace Levelbuild.Core.DataStoreInterface.Exceptions;

/// <summary>
/// Exception for failing data store operation
/// </summary>
public class DataStoreOperationException : ApplicationException
{
    /// <summary>
    /// Exception for failing data store operation
    /// </summary>
    public DataStoreOperationException()
    {
    }

    /// <summary>
    /// Exception for failing data store operation
    /// </summary>
    /// <param name="message"></param>
    public DataStoreOperationException(string message) : base(message)
    {
    }

    /// <summary>
    /// Exception for failing data store operation
    /// </summary>
    /// <param name="message"></param>
    /// <param name="inner"></param>
    public DataStoreOperationException(string message, Exception inner) : base(message, inner)
    {
    }
}