namespace Levelbuild.Core.DataStoreInterface.Exceptions;

/// <summary>
/// Exception for failing data store query
/// </summary>
public class DataStoreQueryException : ApplicationException
{
    /// <summary>
    /// Exception for failing data store query
    /// </summary>
    public DataStoreQueryException()
    {
    }

    /// <summary>
    /// Exception for failing data store query
    /// </summary>
    /// <param name="message"></param>
    public DataStoreQueryException(string message) : base(message)
    {
    }

    /// <summary>
    /// Exception for failing data store query
    /// </summary>
    /// <param name="message"></param>
    /// <param name="inner"></param>
    public DataStoreQueryException(string message, Exception inner) : base(message, inner)
    {
    }
}