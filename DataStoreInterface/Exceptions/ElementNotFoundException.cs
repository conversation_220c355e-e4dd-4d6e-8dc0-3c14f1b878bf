namespace Levelbuild.Core.DataStoreInterface.Exceptions;

/// <summary>
/// Exception for element not found in data store
/// </summary>
public class ElementNotFoundException : ApplicationException
{
    /// <summary>
    /// Exception for element not found in data store
    /// </summary>
    public ElementNotFoundException()
    {
    }

    /// <summary>
    /// Exception for element not found in data store
    /// </summary>
    /// <param name="message"></param>
    public ElementNotFoundException(string message) : base(message)
    {
    }

    /// <summary>
    /// Exception for element not found in data store
    /// </summary>
    /// <param name="message"></param>
    /// <param name="inner"></param>
    public ElementNotFoundException(string message, Exception inner) : base(message, inner)
    {
    }
}