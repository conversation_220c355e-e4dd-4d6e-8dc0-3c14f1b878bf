namespace Levelbuild.Core.DataStoreInterface.Exceptions;

/// <summary>
/// Exception for insufficient rights for data operations
/// </summary>
public class InsufficientRightsException : ApplicationException
{
	/// <summary>
	/// Exception for insufficient rights for data operations
	/// </summary>
	public InsufficientRightsException()
	{
	}

	/// <summary>
	/// Exception for insufficient rights for data operations
	/// </summary>
	/// <param name="message"></param>
	public InsufficientRightsException(string message) : base(message)
	{
	}

	/// <summary>
	/// Exception for insufficient rights for data operations
	/// </summary>
	/// <param name="message"></param>
	/// <param name="inner"></param>
	public InsufficientRightsException(string message, Exception inner) : base(message, inner)
	{
	}
}