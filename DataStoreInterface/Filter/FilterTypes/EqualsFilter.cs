namespace Levelbuild.Core.DataStoreInterface.Filter.FilterTypes;

/// <summary>
/// Compare using <see cref="QueryFilterOperator"/>.Equals
/// </summary>
public class EqualsFilter : QueryFilter {
	/// <summary>
	/// Compare Field against Value
	/// </summary>
	public EqualsFilter(QueryFilterField sourceField, object compareValue) : base(sourceField, QueryFilterOperator.Equals, compareValue) {}
	
	/// <summary>
	/// Compare Field against Field or Subselect
	/// </summary>
	public EqualsFilter(QueryFilterField sourceField, QueryFilterField compareField) : base(sourceField, QueryFilterOperator.Equals, compareField) {}
	
	/// <summary>
	/// Compare Value against Field or Subselect
	/// </summary>
	public EqualsFilter(object sourceValue, QueryFilterField compareField) : base(sourceValue, QueryFilterOperator.Equals, compareField) {}
}