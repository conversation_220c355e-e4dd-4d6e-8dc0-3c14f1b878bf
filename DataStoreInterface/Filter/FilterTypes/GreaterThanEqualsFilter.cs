namespace Levelbuild.Core.DataStoreInterface.Filter.FilterTypes; 

/// <summary>
/// Compare using <see cref="QueryFilterOperator"/>.GreaterThanEquals
/// </summary>
public class GreaterThanEqualsFilter : QueryFilter {
	/// <summary>
	/// Compare Field against Value
	/// </summary>
	public GreaterThanEqualsFilter(QueryFilterField sourceField, object compareValue) : base(sourceField, QueryFilterOperator.GreaterThanEquals, compareValue) {}
	
	/// <summary>
	/// Compare Field against Field or Subselect
	/// </summary>
	public GreaterThanEqualsFilter(QueryFilterField sourceField, QueryFilterField compareField) : base(sourceField, QueryFilterOperator.GreaterThanEquals, compareField) {}
	
	/// <summary>
	/// Compare Value against Field or Subselect
	/// </summary>
	public GreaterThanEqualsFilter(object sourceValue, QueryFilterField compareField) : base(sourceValue, QueryFilterOperator.GreaterThanEquals, compareField) {}
}