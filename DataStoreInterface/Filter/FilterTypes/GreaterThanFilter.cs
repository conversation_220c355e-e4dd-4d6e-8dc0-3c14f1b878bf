namespace Levelbuild.Core.DataStoreInterface.Filter.FilterTypes; 

/// <summary>
/// Compare using <see cref="QueryFilterOperator"/>.GreaterThan
/// </summary>
public class GreaterThanFilter : QueryFilter {
	/// <summary>
	/// Compare Field against Value
	/// </summary>
	public GreaterThanFilter(QueryFilterField sourceField, object compareValue) : base(sourceField, QueryFilterOperator.GreaterThan, compareValue) {}
	
	/// <summary>
	/// Compare Field against Field or Subselect
	/// </summary>
	public GreaterThanFilter(QueryFilterField sourceField, QueryFilterField compareField) : base(sourceField, QueryFilterOperator.GreaterThan, compareField) {}
	
	/// <summary>
	/// Compare Value against Field or Subselect
	/// </summary>
	public GreaterThanFilter(object sourceValue, QueryFilterField compareField) : base(sourceValue, QueryFilterOperator.GreaterThan, compareField) {}
}