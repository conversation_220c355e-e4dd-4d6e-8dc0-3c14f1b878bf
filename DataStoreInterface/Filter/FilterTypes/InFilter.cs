using System.Collections;

namespace Levelbuild.Core.DataStoreInterface.Filter.FilterTypes; 

/// <summary>
/// Compare using <see cref="QueryFilterOperator"/>.In
/// </summary>
public class InFilter : QueryFilter
{
	/// <summary>
	/// Compare Field against Array of values
	/// </summary>
	public InFilter(QueryFilterField sourceField, params object[] compareValues) : base(sourceField, QueryFilterOperator.In, compareValues) {}

	/// <summary>
	/// Compare Field against List of values
	/// </summary>
	public InFilter(QueryFilterField sourceField, IList compareValues) : base(sourceField, QueryFilterOperator.In, compareValues) {}
	
	/// <summary>
	/// Compare Field against Field or Subselect
	/// </summary>
	public InFilter(QueryFilterField sourceField, QueryFilterField compareField) : base(sourceField, QueryFilterOperator.In, compareField) {}
	
	/// <summary>
	/// Compare Value against Field or Subselect
	/// </summary>
	public InFilter(object sourceValue, QueryFilterField compareField) : base(sourceValue, QueryFilterOperator.In, compareField) {}
}