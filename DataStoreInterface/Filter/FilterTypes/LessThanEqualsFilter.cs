namespace Levelbuild.Core.DataStoreInterface.Filter.FilterTypes; 

/// <summary>
/// Compare using <see cref="QueryFilterOperator"/>.LessThanEquals
/// </summary>
public class LessThanEqualsFilter : QueryFilter {
	/// <summary>
	/// Compare Field against Value
	/// </summary>
	public LessThanEqualsFilter(QueryFilterField sourceField, object compareValue) : base(sourceField, QueryFilterOperator.LessThanEquals, compareValue) {}
	
	/// <summary>
	/// Compare Field against Field or Subselect
	/// </summary>
	public LessThanEqualsFilter(QueryFilterField sourceField, QueryFilterField compareField) : base(sourceField, QueryFilterOperator.LessThanEquals, compareField) {}
	
	/// <summary>
	/// Compare Value against Field or Subselect
	/// </summary>
	public LessThanEqualsFilter(object sourceValue, QueryFilterField compareField) : base(sourceValue, QueryFilterOperator.LessThanEquals, compareField) {}
}