namespace Levelbuild.Core.DataStoreInterface.Filter.FilterTypes; 

/// <summary>
/// Compare using <see cref="QueryFilterOperator"/>.LessThan
/// </summary>
public class LessThanFilter : QueryFilter {
	/// <summary>
	/// Compare Field against Value
	/// </summary>
	public LessThanFilter(QueryFilterField sourceField, object compareValue) : base(sourceField, QueryFilterOperator.LessThan, compareValue) {}
	
	/// <summary>
	/// Compare Field against Field or Subselect
	/// </summary>
	public LessThanFilter(QueryFilterField sourceField, QueryFilterField compareField) : base(sourceField, QueryFilterOperator.LessThan, compareField) {}
	
	/// <summary>
	/// Compare Value against Field or Subselect
	/// </summary>
	public LessThanFilter(object sourceValue, QueryFilterField compareField) : base(sourceValue, QueryFilterOperator.LessThan, compareField) {}
}