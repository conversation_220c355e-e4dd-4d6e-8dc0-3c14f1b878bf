namespace Levelbuild.Core.DataStoreInterface.Filter.FilterTypes; 

/// <summary>
/// Compare using <see cref="QueryFilterOperator"/>.Like
/// </summary>
public class LikeFilter : QueryFilter {
	/// <summary>
	/// Compare Field against Value
	/// </summary>
	public LikeFilter(QueryFilterField sourceField, object compareValue) : base(sourceField, QueryFilterOperator.Like, compareValue) {}
	
	/// <summary>
	/// Compare Field against Field or Subselect
	/// </summary>
	public LikeFilter(QueryFilterField sourceField, QueryFilterField compareField) : base(sourceField, QueryFilterOperator.Like, compareField) {}
	
	/// <summary>
	/// Compare Value against Field or Subselect
	/// </summary>
	public LikeFilter(object sourceValue, QueryFilterField compareField) : base(sourceValue, QueryFilterOperator.Like, compareField) {}
}