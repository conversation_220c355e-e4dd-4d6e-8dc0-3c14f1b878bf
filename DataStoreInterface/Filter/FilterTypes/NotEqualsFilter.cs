namespace Levelbuild.Core.DataStoreInterface.Filter.FilterTypes; 

/// <summary>
/// Compare using <see cref="QueryFilterOperator"/>.NotEquals
/// </summary>
public class NotEqualsFilter : QueryFilter {
	/// <summary>
	/// Compare Field against Value
	/// </summary>
	public NotEqualsFilter(QueryFilterField sourceField, object compareValue) : base(sourceField, QueryFilterOperator.NotEquals, compareValue) {}
	
	/// <summary>
	/// Compare Field against Field or Subselect
	/// </summary>
	public NotEqualsFilter(QueryFilterField sourceField, QueryFilterField compareField) : base(sourceField, QueryFilterOperator.NotEquals, compareField) {}
	
	/// <summary>
	/// Compare Value against Field or Subselect
	/// </summary>
	public NotEqualsFilter(object sourceValue, QueryFilterField compareField) : base(sourceValue, QueryFilterOperator.NotEquals, compareField) {}
}