using System.Collections;

namespace Levelbuild.Core.DataStoreInterface.Filter.FilterTypes; 

/// <summary>
/// Compare using <see cref="QueryFilterOperator"/>.NotIn
/// </summary>
public class NotInFilter : QueryFilter {
	/// <summary>
	/// Compare Field against Array of values
	/// </summary>
	public NotInFilter(QueryFilterField sourceField, params object[] compareValues) : base(sourceField, QueryFilterOperator.NotIn, compareValues) {}

	/// <summary>
	/// Compare Field against List of values
	/// </summary>
	public NotInFilter(QueryFilterField sourceField, IList compareValues) : base(sourceField, QueryFilterOperator.NotIn, compareValues) {}
	
	/// <summary>
	/// Compare Field against Field or Subselect
	/// </summary>
	public NotInFilter(QueryFilterField sourceField, QueryFilterField compareField) : base(sourceField, QueryFilterOperator.NotIn, compareField) {}
	
	/// <summary>
	/// Compare Value against Field or Subselect
	/// </summary>
	public NotInFilter(object sourceValue, QueryFilterField compareField) : base(sourceValue, QueryFilterOperator.NotIn, compareField) {}
}