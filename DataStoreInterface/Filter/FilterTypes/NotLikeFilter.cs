namespace Levelbuild.Core.DataStoreInterface.Filter.FilterTypes; 

/// <summary>
/// Compare using <see cref="QueryFilterOperator"/>.NotLike
/// </summary>
public class NotLikeFilter : QueryFilter {
	/// <summary>
	/// Compare Field against Value
	/// </summary>
	public NotLikeFilter(QueryFilterField sourceField, object compareValue) : base(sourceField, QueryFilterOperator.NotLike, compareValue) {}
	
	/// <summary>
	/// Compare Field against Field or Subselect
	/// </summary>
	public NotLikeFilter(QueryFilterField sourceField, QueryFilterField compareField) : base(sourceField, QueryFilterOperator.NotLike, compareField) {}
	
	/// <summary>
	/// Compare Value against Field or Subselect
	/// </summary>
	public NotLikeFilter(object sourceValue, QueryFilterField compareField) : base(sourceValue, QueryFilterOperator.NotLike, compareField) {}
}