using System.Collections;
using System.Text;

namespace Levelbuild.Core.DataStoreInterface.Filter.FilterTypes;

/// <summary>
/// abstract definition of a filter condition
/// </summary>
public abstract class QueryFilter
{
	/// <summary>
	/// 
	/// </summary>
	/// <param name="other"></param>
	/// <returns></returns>
	protected bool Equals(QueryFilter other)
	{
		return new QueryFilterEqualityComparer().Equals(this, other);
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="obj"></param>
	/// <returns></returns>
	public override bool Equals(object? obj)
	{
		if(obj is QueryFilter queryFilter)
			return new QueryFilterEqualityComparer().Equals(this, queryFilter);
		return false;
	}

	#region Properties

	/// <summary>
	/// Determines what's contained inside the <see cref="SourceValue"/> (either a Fieldname or a plain value)
	/// </summary>
	public QueryFilterValueType SourceValueType { get; init; } = QueryFilterValueType.None;

	/// <summary>
	/// Left side of the comparison
	/// </summary>
	public object? SourceValue { get; set; }

	/// <summary>
	/// Compare operation
	/// </summary>
	public QueryFilterOperator Operator { get; init; }

	/// <summary>
	/// Determines what's contained inside the <see cref="CompareValue"/> (either a Fieldname, a plain value or a subselect)
	/// </summary>
	public QueryFilterValueType CompareValueType { get; init; } = QueryFilterValueType.None;

	/// <summary>
	/// Right side of the comparison
	/// </summary>
	public object? CompareValue { get; set; }

	/// <summary>
	/// If the <see cref="CompareValueType"/> is set to Subselect, the <see cref="CompareValueSource"/> contains the name of the data source to select against
	/// </summary>
	public string? CompareValueSource { get; init; }

	/// <summary>
	/// If the <see cref="CompareValueType"/> is set to Subselect, the <see cref="CompareValueConditions"/> contains the filter conditions to apply whilst querying the <see cref="CompareValueSource"/>
	/// </summary>
	public QueryFilterGroup CompareValueConditions { get; init; } = new(QueryFilterLinkType.Or);

	#endregion

	#region Contructors

	/// <summary>
	/// Compare Field against Value
	/// </summary>
	/// <param name="sourceField">Data Source Field</param>
	/// <param name="operator">Compare Operator</param>
	/// <param name="compareValue">Manual Compare Value</param>
	protected QueryFilter(QueryFilterField sourceField, QueryFilterOperator @operator, object? compareValue = null)
	{
		SourceValueType = QueryFilterValueType.Field;
		SourceValue = sourceField.Name;
		Operator = @operator;
		if (Operator != QueryFilterOperator.IsNull && Operator != QueryFilterOperator.IsNotNull)
		{
			CompareValueType = QueryFilterValueType.Value;
			CompareValue = compareValue;
		}
	}

	/// <summary>
	/// Compare Field against Field
	/// </summary>
	/// <param name="sourceField">Data Source Field</param>
	/// <param name="operator">Compare Operator</param>
	/// <param name="compareField">Compare Field (either from the base data source or a subselect)</param>
	protected QueryFilter(QueryFilterField sourceField, QueryFilterOperator @operator, QueryFilterField compareField)
	{
		SourceValueType = QueryFilterValueType.Field;
		SourceValue = sourceField.Name;
		Operator = @operator;
		if (compareField.DataSourceName != null)
		{
			CompareValueType = QueryFilterValueType.Subselect;
			CompareValue = compareField.Name;
			CompareValueSource = compareField.DataSourceName;
			CompareValueConditions = compareField.DataSourceFilters;
		}
		else
		{
			CompareValueType = QueryFilterValueType.Field;
			CompareValue = compareField.Name;
		}
	}

	/// <summary>
	/// Compare Value against Field
	/// </summary>
	/// <param name="sourceValue">Manual Value</param>
	/// <param name="operator">Compare Operator</param>
	/// <param name="compareField">Compare Field (either from the base data source or a subselect)</param>
	protected QueryFilter(object sourceValue, QueryFilterOperator @operator, QueryFilterField compareField)
	{
		SourceValueType = QueryFilterValueType.Value;
		SourceValue = sourceValue;
		Operator = @operator;
		if (compareField.DataSourceName != null)
		{
			CompareValueType = QueryFilterValueType.Subselect;
			CompareValue = compareField.Name;
			CompareValueSource = compareField.DataSourceName;
			CompareValueConditions = compareField.DataSourceFilters;
		}
		else
		{
			CompareValueType = QueryFilterValueType.Field;
			CompareValue = compareField.Name;
		}
	}

	/// <summary>
	/// Check if a subselect returns values or not.
	/// </summary>
	/// <param name="operator">only supports Exists/NotExists Operator</param>
	/// <param name="compareField">needs to be a subselect</param>
	protected QueryFilter(QueryFilterOperator @operator, QueryFilterField compareField)
	{
		if (@operator != QueryFilterOperator.Exists && @operator != QueryFilterOperator.NotExists)
			throw new Exception("Subselect without source value to compare against is only allowed when using Exists/NotExists");

		if (compareField.DataSourceName == null)
			throw new Exception("Exists/NotExists filtering is only available in combination with a subselect");

		Operator = @operator;
		CompareValueType = QueryFilterValueType.Subselect;
		CompareValue = compareField.Name;
		CompareValueSource = compareField.DataSourceName;
		CompareValueConditions = compareField.DataSourceFilters;
	}

	/// <summary>
	/// Check if a datasource fulltext search matches.
	/// </summary>
	/// <param name="operator">only supports Exists/NotExists Operator</param>
	/// <param name="compareValue">needs to be a subselect</param>
	protected QueryFilter(QueryFilterOperator @operator, string compareValue)
	{
		if (@operator != QueryFilterOperator.FulltextSearch)
			throw new Exception("Filter by string without passing a compare field requires fulltext search.");

		Operator = @operator;
		CompareValueType = QueryFilterValueType.Value;
		CompareValue = compareValue;
	}

	protected QueryFilter(QueryFilterOperator @operator, bool favouritesOnly)
	{
		if (@operator != QueryFilterOperator.Favourite)
			throw new Exception("Filter by boolean without passing a compare field requires favourite function.");

		Operator = @operator;
		CompareValueType = QueryFilterValueType.Value;
		CompareValue = favouritesOnly;
	}

	/// <summary>
	/// Returns a string representation of the filter.
	/// </summary>
	/// <returns></returns>
	public override string ToString()
	{
		var stringBuilder = new StringBuilder();

		if (CompareValueType == QueryFilterValueType.Subselect && (Operator == QueryFilterOperator.Exists || Operator == QueryFilterOperator.NotExists))
		{
			stringBuilder.Append($"{CompareValueSource}.{CompareValue} {Operator.GetSign()}");

			if (CompareValueConditions.Filters.Count > 0 || CompareValueConditions.FilterGroups.Count > 0)
				stringBuilder.Append($" WHERE {CompareValueConditions.ToString()}");
		}
		else
		{
			switch (SourceValueType)
			{
				case QueryFilterValueType.Field:
					stringBuilder.Append($"{SourceValue} ");
					break;
				case QueryFilterValueType.Value:
					if (SourceValue is string)
						stringBuilder.Append($@"""{SourceValue}"" ");
					else if (SourceValue is IEnumerable)
					{
						stringBuilder.Append("[");

						var enumerator = ((IEnumerable)SourceValue).GetEnumerator();
						using var enumeratorDisposable = enumerator as IDisposable;
						var last = !enumerator.MoveNext();
						object current;
						while (!last)
						{
							current = enumerator.Current!;

							last = !enumerator.MoveNext();
							if (last)
							{
								if (current is string)
									stringBuilder.Append($@"""{current}""");
								else
									stringBuilder.Append($"{current}");
							}
							else
							{
								if (current is string)
									stringBuilder.Append($@"""{current}"", ");
								else
									stringBuilder.Append($"{current}, ");
							}
						}
						stringBuilder.Append("] ");
					}
					else
						stringBuilder.Append($"{SourceValue} ");

					break;
			}

			stringBuilder.Append($"{Operator.GetSign()}");

			switch (CompareValueType)
			{
				case QueryFilterValueType.Field:
					stringBuilder.Append($" {CompareValue}");
					break;
				case QueryFilterValueType.Value:
					if (CompareValue is string)
						stringBuilder.Append($@" ""{CompareValue}""");
					else if (CompareValue is IEnumerable)
					{
						stringBuilder.Append(" [");

						var enumerator = ((IEnumerable)CompareValue).GetEnumerator();
						using var enumeratorDisposable = enumerator as IDisposable;
						var last = !enumerator.MoveNext();
						object current;
						while (!last)
						{
							current = enumerator.Current!;

							last = !enumerator.MoveNext();
							if (last)
							{
								if (current is string)
									stringBuilder.Append($@"""{current}""");
								else
									stringBuilder.Append($"{current}");
							}
							else
							{
								if (current is string)
									stringBuilder.Append($@"""{current}"", ");
								else
									stringBuilder.Append($"{current}, ");
							}
						}
						stringBuilder.Append("]");
					}
					else
						stringBuilder.Append($" {CompareValue}");

					break;
				case QueryFilterValueType.Subselect:
					if (CompareValueConditions.Filters.Count > 0 || CompareValueConditions.FilterGroups.Count > 0)
						stringBuilder.Append($" ({CompareValueSource}.{CompareValue} WHERE {CompareValueConditions.ToString()})");
					else
						stringBuilder.Append($" {CompareValueSource}.{CompareValue}");

					break;
			}
		}

		return stringBuilder.ToString();
	}

	#endregion

	#region Operators

	/// <summary>
	/// A comparison.
	/// </summary>
	/// <param name="x"></param>
	/// <param name="y"></param>
	/// <returns>True if equal.</returns>
	public static bool operator ==(QueryFilter? x, QueryFilter? y)
	{
		return new QueryFilterEqualityComparer().Equals(x, y);
	}

	/// <summary>
	/// A negated comparison.
	/// </summary>
	/// <param name="x"></param>
	/// <param name="y"></param>
	/// <returns>False if equal.</returns>
	public static bool operator !=(QueryFilter? x, QueryFilter? y)
	{
		return !new QueryFilterEqualityComparer().Equals(x, y);
	}

	/// <summary>
	/// Generates a comparable hash code.
	/// </summary>
	/// <returns></returns>
	public override int GetHashCode()
	{
		return new QueryFilterEqualityComparer().GetHashCode(this);
	}

	#endregion
}

/// <summary>
/// Comparer for Query Filter
/// </summary>
public class QueryFilterEqualityComparer : IEqualityComparer<QueryFilter>
{
	/// <summary>
	/// A comparison
	/// </summary>
	/// <param name="x"></param>
	/// <param name="y"></param>
	/// <returns>True if equal</returns>
	public bool Equals(QueryFilter? x, QueryFilter? y)
	{
		if (ReferenceEquals(x, y))
			return true;
		if (ReferenceEquals(x, null))
			return false;
		if (ReferenceEquals(y, null))
			return false;
		if (x.GetType() != y.GetType())
			return false;

		if (x.Operator != y.Operator)
			return false;

		return GetHashCode(x) == GetHashCode(y);
	}

	/// <summary>
	/// Generates a comparable hash code.
	/// </summary>
	/// <param name="obj"></param>
	/// <returns></returns>
	public int GetHashCode(QueryFilter obj)
	{
		return HashCode.Combine(obj.ToString());
	}
}