namespace Levelbuild.Core.DataStoreInterface.Filter;

/// <summary>
/// Contains the definition of a Field or Field based Function to filter against
/// </summary>
public class QueryFilterField
{
	/// <summary>
	/// Field name or function definiton like SUM(FIELDNAME). May be null in cases where the subselect is only used for EXISTS/NOTEXISTS checks
	/// </summary>
	public string? Name { get; init; }
	
	/// <summary>
	/// Name of the data source to query against (if filtering using a subselect)
	/// </summary>
	public string? DataSourceName { get; init; }

	/// <summary>
	/// Filters to apply against the subselect <see cref="DataSourceName">data source</see>
	/// </summary>
	public QueryFilterGroup DataSourceFilters { get; init; } = new(QueryFilterLinkType.Or);

	/// <summary>
	/// filter against field or field function
	/// </summary>
	/// <param name="name">field name or field function</param>
	public QueryFilterField(string name)
	{
		Name = name;
	}
	
	/// <summary>
	/// filter against another datasource (subselect)
	/// </summary>
	/// <param name="dataSourceName">data source name</param>
	/// <param name="dataSourceFilters">optional filters to apply</param>
	public QueryFilterField(string dataSourceName, QueryFilterGroup? dataSourceFilters = null)
	{
		DataSourceName = dataSourceName;
		if (dataSourceFilters != null)
			DataSourceFilters = dataSourceFilters;
	}
	
	/// <summary>
	/// filter against a specific value or aggregate function using a subselect
	/// </summary>
	/// <param name="name">subselect field or field function</param>
	/// <param name="dataSourceName">data source name</param>
	/// <param name="dataSourceFilters">optional filters to apply</param>
	public QueryFilterField(string name, string dataSourceName, QueryFilterGroup? dataSourceFilters = null)
	{
		Name = name;
		DataSourceName = dataSourceName;
		if (dataSourceFilters != null)
			DataSourceFilters = dataSourceFilters;
	}
}