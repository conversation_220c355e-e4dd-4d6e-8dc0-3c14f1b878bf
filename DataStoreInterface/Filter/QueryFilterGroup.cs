
using System.Text;
using Levelbuild.Core.DataStoreInterface.Filter.FilterTypes;

namespace Levelbuild.Core.DataStoreInterface.Filter; 

/// <summary>
/// Represents a group of filter conditions. Inside this group, all conditions (and sub groups) are linked by either <see cref="LinkType"/>.AND or <see cref="LinkType"/>.OR.
/// </summary>
public class QueryFilterGroup {
	/// <summary>
	/// Equals function for QueryFilterGroup
	/// </summary>
	/// <param name="other"></param>
	/// <returns></returns>
	protected bool Equals(QueryFilterGroup other)
	{
		return new QueryFilterGroupEqualityComparer().Equals(this, other);
	}

	/// <summary>
	/// Equals function for object
	/// </summary>
	/// <param name="obj"></param>
	/// <returns></returns>
	public override bool Equals(object? obj)
	{
		if(obj is QueryFilterGroup queryFilterGroup)
			return new QueryFilterGroupEqualityComparer().Equals(this, queryFilterGroup);
		return false;
	}


	#region Attributes

	private List<QueryFilter> _filters;
	private List<QueryFilterGroup> _filterGroups;

	#endregion

	#region Properties

	/// <summary>
	/// Filter conditions
	/// </summary>
	public List<QueryFilter> Filters => _filters;

	/// <summary>
	/// Sub Groups
	/// </summary>
	public List<QueryFilterGroup> FilterGroups => _filterGroups;

	/// <summary>
	/// Link type deciding if the filters are connected with AND or OR
	/// </summary>
	public QueryFilterLinkType LinkType { get; set; }

	#endregion

	#region Constructors
    
	/// <summary>
	/// Constructor
	/// </summary>
	public QueryFilterGroup() {
		_filters = new List<QueryFilter>();
		_filterGroups = new List<QueryFilterGroup>();
		
		LinkType = QueryFilterLinkType.And;
	}
    
	/// <summary>
	/// Constructor
	/// </summary>
	public QueryFilterGroup(QueryFilterLinkType linkType) {
		_filters = new List<QueryFilter>();
		_filterGroups = new List<QueryFilterGroup>();
		
		LinkType = linkType;
	}

	/// <summary>
	/// Constructor
	/// </summary>
	public QueryFilterGroup(List<QueryFilter> filters, QueryFilterLinkType linkType = QueryFilterLinkType.And) {
		_filters = filters;
		_filterGroups = new List<QueryFilterGroup>();
		
		LinkType = linkType;
	}
	
	/// <summary>
	/// Constructor
	/// </summary>
	public QueryFilterGroup(List<QueryFilter> filters, List<QueryFilterGroup> filterGroups, QueryFilterLinkType linkType = QueryFilterLinkType.And) {
		_filters = filters;
		_filterGroups = filterGroups;
		
		LinkType = linkType;
	}

	#endregion

	#region Methods

	/// <summary>
	/// Add a new filter to the group
	/// </summary>
	/// <param name="filter"></param>
	/// <returns>returns <see cref="QueryFilterGroup">this</see> to support chaining</returns>
	public QueryFilterGroup AddFilter(QueryFilter filter) {
		_filters.Add(filter);
		return this;
	}

	/// <summary>
	/// Add a new filter group to the group
	/// </summary>
	/// <param name="filterGroup"></param>
	/// <returns>returns <see cref="QueryFilterGroup">this</see> to support chaining</returns>
	public QueryFilterGroup AddFilterGroup(QueryFilterGroup filterGroup) {
		_filterGroups.Add(filterGroup);
		return this;
	}

	/// <summary>
	/// Returns a string representation of the filter group.
	/// </summary>
	/// <returns></returns>
	public override string ToString()
	{
		var stringBuilder = new StringBuilder();

		if (_filters.Count > 0 && _filterGroups.Count > 0)
			stringBuilder.Append("(");
			
		for (int i = 0; i < _filters.Count; i++)
		{
			stringBuilder.Append(_filters[i]);

			if (i < _filters.Count - 1)
				stringBuilder.Append($" {LinkType.GetSign()} ");
		}
		
		if (_filters.Count > 0 && _filterGroups.Count > 0)
			stringBuilder.Append($") {LinkType.GetSign()} ");
		
		for (int i = 0; i < _filterGroups.Count; i++)
		{
			stringBuilder.Append($"({_filterGroups[i]})");

			if (i < _filterGroups.Count - 1)
				stringBuilder.Append($" {LinkType.GetSign()} ");
		}
		
		return stringBuilder.ToString();
	}

	#endregion

	#region Operators

	/// <summary>
	/// A comparison.
	/// </summary>
	/// <param name="x"></param>
	/// <param name="y"></param>
	/// <returns>True if equal.</returns>
	public static bool operator ==(QueryFilterGroup? x, QueryFilterGroup? y)
	{
		return new QueryFilterGroupEqualityComparer().Equals(x, y);
	}

	/// <summary>
	/// A negated comparison.
	/// </summary>
	/// <param name="x"></param>
	/// <param name="y"></param>
	/// <returns>False if equal.</returns>
	public static bool operator !=(QueryFilterGroup? x, QueryFilterGroup? y)
	{
		return !new QueryFilterGroupEqualityComparer().Equals(x, y);
	}
	
	/// <summary>
	/// Generates a comparable hash code.
	/// </summary>
	/// <returns></returns>
	public override int GetHashCode()
	{
		return new QueryFilterGroupEqualityComparer().GetHashCode(this);
	}

	#endregion
	
}

/// <summary>
/// Comparer for QueryFilterGroup
/// </summary>
public class QueryFilterGroupEqualityComparer : IEqualityComparer<QueryFilterGroup>
{
	/// <summary>
	/// A comparison
	/// </summary>
	/// <param name="x"></param>
	/// <param name="y"></param>
	/// <returns>True if equal</returns>
	public bool Equals(QueryFilterGroup? x, QueryFilterGroup? y)
	{
		if (ReferenceEquals(x, y))
			return true;
		if (ReferenceEquals(x, null))
			return false;
		if (ReferenceEquals(y, null))
			return false;
		if (x.GetType() != y.GetType())
			return false;

		return GetHashCode(x) == GetHashCode(y);
	}

	/// <summary>
	/// Generates a comparable hash code.
	/// </summary>
	/// <param name="obj"></param>
	/// <returns></returns>
	public int GetHashCode(QueryFilterGroup obj)
	{
		return HashCode.Combine(obj.ToString());
	}
}