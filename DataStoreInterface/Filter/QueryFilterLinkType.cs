using Levelbuild.Core.DataStoreInterface.Filter.FilterTypes;
using Levelbuild.Core.SharedUtilities;

namespace Levelbuild.Core.DataStoreInterface.Filter
{
	/// <summary>
	/// <see cref="QueryFilter"/>s inside a <see cref="QueryFilterGroup"/> may be linked either using "And" or "Or"
	/// </summary>
	public enum QueryFilterLinkType {
		/// <summary>
		/// LinkType And
		/// </summary>
		[QueryFilterLinkTypeDescriptor("AND")]
		And,
		
		/// <summary>
		/// LinkType Or
		/// </summary>
		[QueryFilterLinkTypeDescriptor("OR")]
		Or
	}
	
	/// <summary>
	/// 
	/// </summary>
	[AttributeUsage(AttributeTargets.Field)]
	public class QueryFilterLinkTypeDescriptorAttribute : Attribute
	{
		/// <summary>
		/// 
		/// </summary>
		public string Sign { get; }

		/// <summary>
		/// 
		/// </summary>
		/// <param name="sign"></param>
		public QueryFilterLinkTypeDescriptorAttribute(string sign)
		{
			Sign = sign;
		}
	}

	/// <summary>
	/// Extensions for QueryFilterLinkType
	/// </summary>
	public static class QueryFilterLinkTypeExtensions
	{
		/// <summary>
		/// 
		/// </summary>
		/// <param name="enumValue"></param>
		/// <returns></returns>
		public static string GetSign(this QueryFilterLinkType enumValue)
		{
			var attr = GetAttributes(enumValue);
			return attr.Sign;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="enumValue"></param>
		/// <returns></returns>
		public static string GetString(this QueryFilterLinkType enumValue)
		{
			return EnumUtils<QueryFilterLinkType>.GetTranslatableString(enumValue);
		}

		private static QueryFilterLinkTypeDescriptorAttribute GetAttributes(QueryFilterLinkType enumValue)
		{
			return (EnumUtils<QueryFilterLinkType>.GetAttributeFromEnum<QueryFilterLinkTypeDescriptorAttribute>(enumValue) as
						QueryFilterLinkTypeDescriptorAttribute)!;
		}
	}
} 