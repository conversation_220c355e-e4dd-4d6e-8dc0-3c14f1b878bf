using Levelbuild.Core.SharedUtilities;

namespace Levelbuild.Core.DataStoreInterface.Filter
{
	/// <summary>
	/// Used inside QueryFilters to define how the CompareField value should be compared against the CompareValue
	/// </summary>
	public enum QueryFilterOperator
	{
		/// <summary>
		/// Equals =
		/// </summary>
		[QueryFilterOperatorDescriptor("=")]
		Equals,

		/// <summary>
		/// NotEquals !=
		/// </summary>
		[QueryFilterOperatorDescriptor("!=")]
		NotEquals,

		/// <summary>
		/// Like LIKE
		/// </summary>
		[QueryFilterOperatorDescriptor("LIKE")]
		Like,

		/// <summary>
		/// NotLike NOT LIKE
		/// </summary>
		[QueryFilterOperatorDescriptor("NOT LIKE")]
		NotLike,

		/// <summary>
		/// GreaterThan >
		/// </summary>
		[QueryFilterOperatorDescriptor(">")]
		GreaterThan,

		/// <summary>
		/// GreaterThanEquals >=
		/// </summary>
		[QueryFilterOperatorDescriptor(">=")]
		GreaterThanEquals,

		/// <summary>
		/// LessThan 
		/// </summary>
		[QueryFilterOperatorDescriptor("<")]
		LessThan,

		/// <summary>
		/// LessThanEquals
		/// </summary>
		[QueryFilterOperatorDescriptor("<=")]
		LessThanEquals,

		/// <summary>
		/// In IN
		/// </summary>
		[QueryFilterOperatorDescriptor("IN")]
		In,

		/// <summary>
		/// NotIn NOT IN
		/// </summary>
		[QueryFilterOperatorDescriptor("NOT IN")]
		NotIn,

		/// <summary>
		/// IsNull IS NULL
		/// </summary>
		[QueryFilterOperatorDescriptor("IS NULL")]
		IsNull,

		/// <summary>
		/// IsNotNull IS NOT NULL
		/// </summary>
		[QueryFilterOperatorDescriptor("IS NOT NULL")]
		IsNotNull,

		/// <summary>
		/// Exists EXISTS
		/// </summary>
		[QueryFilterOperatorDescriptor("EXISTS")]
		Exists,

		/// <summary>
		/// NotExists DOES NOT EXIST
		/// </summary>
		[QueryFilterOperatorDescriptor("DOES NOT EXIST")]
		NotExists,

		/// <summary>
		/// FulltextSearch @@
		/// </summary>
		[QueryFilterOperatorDescriptor("@@")]
		FulltextSearch,
		
		/// <summary>
		/// Favourite FAVOURITE
		/// </summary>
		[QueryFilterOperatorDescriptor("FAVOURITE")]
		Favourite
	}

	/// <summary>
	/// QueryFilterOperatorDescriptorAttribute valid on field
	/// </summary>
	[AttributeUsage(AttributeTargets.Field)]
	public class QueryFilterOperatorDescriptorAttribute : Attribute
	{
		/// <summary>
		/// Get sign
		/// </summary>
		public string Sign { get; }

		/// <summary>
		/// Constructor
		/// </summary>
		/// <param name="sign"></param>
		public QueryFilterOperatorDescriptorAttribute(string sign)
		{
			Sign = sign;
		}
	}

	/// <summary>
	/// Constructor
	/// </summary>
	public static class QueryFilterOperatorExtensions
	{
		/// <summary>
		/// Get sign
		/// </summary>
		/// <param name="enumValue"></param>
		/// <returns></returns>
		public static string GetSign(this QueryFilterOperator enumValue)
		{
			var attr = GetAttributes(enumValue);
			return attr.Sign;
		}

		/// <summary>
		/// get string
		/// </summary>
		/// <param name="enumValue"></param>
		/// <returns></returns>
		public static string GetString(this QueryFilterOperator enumValue)
		{
			return EnumUtils<QueryFilterOperator>.GetTranslatableString(enumValue);
		}

		private static QueryFilterOperatorDescriptorAttribute GetAttributes(QueryFilterOperator enumValue)
		{
			return (EnumUtils<QueryFilterOperator>.GetAttributeFromEnum<QueryFilterOperatorDescriptorAttribute>(enumValue) as
						QueryFilterOperatorDescriptorAttribute)!;
		}
	}
}