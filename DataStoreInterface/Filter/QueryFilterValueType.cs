namespace Levelbuild.Core.DataStoreInterface.Filter;

/// <summary>
/// Enum for QueryFilterValueType
/// </summary>
public enum QueryFilterValueType
{
	/// <summary>
	/// Filter value type None
	/// </summary>
	None,
	/// <summary>
	/// Filter value type Field
	/// </summary>
	Field,
	/// <summary>
	/// Filter value type Value
	/// </summary>
	Value,
	/// <summary>
	/// Filter value type Subselect
	/// </summary>
	Subselect
}