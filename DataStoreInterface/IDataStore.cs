using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Dto.Interfaces;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Serilog;

namespace Levelbuild.Core.DataStoreInterface;

/// <summary>
/// Needs to be implemented by each DataStore implementation (aka Backend) and is used to load the available
/// configuration options as well as instantiate the DataStore and get a new <see cref="IDataStoreConnection"/>.
/// </summary>
public interface IDataStore
{
    /// <summary>
    /// Get the Configuration Object for this backend.
    /// (contains meta information about the implementation as well as the available configuration options)
    /// </summary>
    public static virtual DataStoreInfo GetInfo() => throw new NotImplementedException();

    /// <summary>
    /// Get a new Instance of the backend implementation.
    /// </summary>
    /// <param name="configValues">Configuration values</param>
	/// <param name="logger">The logger of the datastore instance</param>
    /// <exception cref="DataStoreConfigurationException">thrown if the configuration is missing required values</exception>
    public static virtual IDataStore GetInstance(IDictionary<string, object> configValues, ILogger logger) => throw new NotImplementedException();
 
    /// <summary>
    /// Returns a list of available context names.
    /// </summary>
    /// <returns>List of <see cref="IDataStoreContext"/> representing all available contexts within the data store.</returns>
    public IList<IDataStoreContext> GetContexts();

    /// <summary>
    /// Gets a detailed context information for a specific context identified by its name.
    /// </summary>
    /// <param name="identifier">name identifying this context</param>
    /// <returns><see cref="IDataStoreContext"/> describing the context or null if there is no context with the given name.</returns>
    public IDataStoreContext? GetContext(string identifier);
    
    /// <summary>
    /// Get a new Connection from an already instantiated backend instance.
    /// </summary>
    /// <param name="authentication">User authentication</param>
    /// <exception cref="DataStoreConnectionException">thrown if the connection can not be established</exception>
    /// <exception cref="DataStoreAuthenticationException">thrown if the credentials are wrong</exception>
    public IDataStoreConnection GetConnection(IDataStoreAuthentication? authentication);
	
	/// <summary>
	/// Get a new Connection to a specific context from an already instantiated backend instance.
	/// </summary>
	/// <param name="context">optional context (used for data stores which support multi tenancy)</param>
	/// <param name="authentication">User authentication</param>
	/// <exception cref="DataStoreConnectionException">thrown if the connection can not be established</exception>
	/// <exception cref="ContextNotFoundException">thrown if there was no context with the given identifier.</exception>
	/// <exception cref="DataStoreAuthenticationException">thrown if the credentials are wrong</exception>
	public IDataStoreConnection GetConnection(string context, IDataStoreAuthentication? authentication);
}