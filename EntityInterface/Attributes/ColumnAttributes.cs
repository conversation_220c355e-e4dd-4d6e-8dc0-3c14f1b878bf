namespace Levelbuild.Core.EntityInterface.Attributes;

/// <summary>
/// Marks an entity's column as json type.
/// </summary>
[AttributeUsage(AttributeTargets.Property | AttributeTargets.Field)]
public class JsonColumn : Attribute { }

/// <summary>
/// Marks an entity's column to not be persisted in db schema.
/// </summary>
[AttributeUsage(AttributeTargets.Property | AttributeTargets.Field)]
public class DoNotPersist : Attribute { }

/// <summary>
/// Marks an entity's column as text type.
/// </summary>
[AttributeUsage(AttributeTargets.Property | AttributeTargets.Field)]
public class TextAttribute : Attribute { }

/// <summary>
/// Marks an entity's column as short string (max 100 characters) type.
/// </summary>
[AttributeUsage(AttributeTargets.Property | AttributeTargets.Field)]
public class ShortStringAttribute : Attribute { }
