namespace Levelbuild.Core.EntityInterface.Attributes;

/// <summary>
/// Custom attribute to set cascading deletion behaviour
/// </summary>
[AttributeUsage(AttributeTargets.Property)]
public class CascadeDeleteAttribute : Attribute
{
}

/// <summary>
/// Custom attribute to set blocking deletion behaviour
/// </summary>
[AttributeUsage(AttributeTargets.Property)]
public class BlockingDeleteAttribute : Attribute
{
}

/// <summary>
/// Custom attribute to set deletion behaviour to set references to null
/// </summary>
[AttributeUsage(AttributeTargets.Property)]
public class SetNullAttribute : Attribute
{
}