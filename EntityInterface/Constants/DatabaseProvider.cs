namespace Levelbuild.Core.EntityInterface.Constants;

/// <summary>
/// Class that holds information about the WebApp's current database type.
/// </summary>
public record DatabaseProvider
{
	/// <summary>
	/// PostgreSQL
	/// </summary>
	public static DatabaseProvider Postgres = new (nameof(Postgres));
	
	/// <summary>
	/// Microsoft SQL Server
	/// </summary>
	public static DatabaseProvider SqlServer = new (nameof(SqlServer));
	
	/// <summary>
	/// Name of the db type.
	/// </summary>
	public string Name { get; private init; }

	/// <summary>
	/// Constructor.
	/// </summary>
	/// <param name="name"></param>
	protected DatabaseProvider(string name)
	{
		Name = name;
	}
}