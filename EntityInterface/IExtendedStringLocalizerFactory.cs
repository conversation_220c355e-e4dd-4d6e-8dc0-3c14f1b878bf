using Microsoft.Extensions.Localization;

namespace Levelbuild.Core.EntityInterface;

/// <summary>
/// Extension to the default IStringLocalizerFactory to allow additional parameters for localization
/// </summary>
public interface IExtendedStringLocalizerFactory : IStringLocalizerFactory
{
	/// <summary>
	/// Create a new StringLocalizer instance based on controller name and view name
	/// </summary>
	/// <param name="baseName">usually controller name</param>
	/// <param name="location">usually view name</param>
	/// <param name="ignoreWarnings">should this localizer throw warnings, when translations are missing</param>
	/// <returns></returns>
	public IStringLocalizer Create(string baseName, string location, bool ignoreWarnings);
}