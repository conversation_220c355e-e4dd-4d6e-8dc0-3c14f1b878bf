using Levelbuild.Core.EntityInterface.Constants;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Core.EntityInterface;

/// <summary>
/// describes a EFCore entity which has at least a unique id as well as some basic configuration methods
/// </summary>
public interface IPersistentEntity
{
	/// <summary>
	/// Unique ID of the entity
	/// </summary>
	public Guid Id { get; protected set; }
	
	/// <summary>
	/// Used to describe relations, column types etc.
	/// </summary>
	/// <param name="modelBuilder">The <see cref="ModelBuilder"/> instance provided by the DbContext.</param>
	/// <param name="databaseProvider">The configured DB provider of the application. (i.e. PostgreSQL)</param>
	public void ConfigureModel(ModelBuilder modelBuilder, DatabaseProvider databaseProvider);
	
	/// <summary>
	/// Sets the string localizer factory.
	/// </summary>
	/// <param name="factory"></param>
	public void SetStringLocalizerFactory(IExtendedStringLocalizerFactory factory);
	
	/// <summary>
	/// Sets the database context.
	/// </summary>
	/// <param name="context"></param>
	public void SetContext(DbContext context);

	/// <summary>
	/// identifier of entity containing both entity class name and entity id
	/// </summary>
	public string Identifier => $"{GetType().Name}#{Id}";
}