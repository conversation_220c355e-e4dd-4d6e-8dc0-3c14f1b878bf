using System.Text.RegularExpressions;

namespace Levelbuild.Core.FileInterface;

/// <summary>
/// Abstract class to implement special directory info 
/// </summary>
public abstract class DirectoryInfo
{
	// only alphanumerical or '_', must start and end with '/', can not have '/tree//test/', max length 255 chars
	private static readonly Regex DirectoryRegex = new Regex("^\\/([a-zA-Z0-9_-]+\\/)*$");
	
	/// <summary>
	/// Base path of directory
	/// </summary>
	protected string BaseDir { get; }
	
	/// <summary>
	/// fullpath of the directory
	/// </summary>
	public string Path { get; }
	
	/// <summary>
	/// Full path of directory
	/// </summary>
	protected string AbsolutePath => BaseDir + Path;
	
	/// <summary>
	/// Constructor
	/// </summary>
	/// <param name="baseDir"></param>
	/// <param name="path"></param>
	/// <exception cref="ArgumentException"></exception>
	protected DirectoryInfo(string baseDir, string path)
	{
		BaseDir = baseDir;
		Path = path;
		
		if (BaseDir.EndsWith("/"))
			BaseDir = BaseDir.Substring(0, BaseDir.Length - 1);
		
		if (!Path.EndsWith("/"))
			Path += "/";
		if (!Path.StartsWith("/"))
			Path = "/" + Path;
		
		if (!DirectoryRegex.IsMatch(Path))
			throw new ArgumentException($"{Path} does not match {DirectoryRegex}");
		if (Path.Length > FileStore.MaxPathLength)
			throw new ArgumentException($"{Path} is longer than {FileStore.MaxPathLength} characters.");
	}
	
	/// <summary>
	/// Checks if represented directory exists. This is true if at least one file has this directory as parent.
	/// </summary>
	/// <returns>true if exists, fals otherwise</returns>
	public abstract bool Exists();
	
	/// <summary>
	/// deletes the directory of the FileSystemDirectoryInfo object, if exists
	/// </summary>
	public abstract void Delete();
	
	/// <summary>
	/// Lists all directories in represented folder
	/// </summary>
	/// <returns>List of directories</returns>
	public abstract List<DirectoryInfo> ListDirectories();
	
	/// <summary>
	/// Lists all files in represented folder
	/// </summary>
	/// <returns>List of files</returns>
	public abstract List<FileInfo> ListFiles();
	
	/// <summary>
	/// parent folder of directory
	/// </summary>
	/// <returns>parent directory</returns>
	public DirectoryInfo GetParent()
	{
		var path = Path.Substring(0, Path.Substring(0, Path.Length - 1).LastIndexOf("/", StringComparison.Ordinal));
		return GetWithPath(path);
	}
	
	/// <summary>
	/// Returns directory object of given path
	/// </summary>
	/// <param name="path"></param>
	/// <returns></returns>
	protected abstract DirectoryInfo GetWithPath(string path);
	
	/// <summary>
	/// overrides ToString method
	/// </summary>
	/// <returns>the fullPath of the FileSystemDirectoryInfo object</returns>
	public override string ToString()
	{
		return Path;
	}
}