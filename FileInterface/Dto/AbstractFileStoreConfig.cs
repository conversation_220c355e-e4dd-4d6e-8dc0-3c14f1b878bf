namespace Levelbuild.Core.FileInterface.Dto;

/// <summary>
/// abstract configuration class for file store
/// </summary>
public abstract class AbstractFileStoreConfig
{
	/// <summary>
	/// The base directory used for this File abstraction instance.
	/// All other paths are relative to this one.
	/// </summary>
	public string RootPath { get; set; }
	
	/// <summary>
	/// 
	/// </summary>
	/// <param name="rootPath"></param>
	public AbstractFileStoreConfig(string rootPath)
	{
		RootPath = rootPath;
	}
}