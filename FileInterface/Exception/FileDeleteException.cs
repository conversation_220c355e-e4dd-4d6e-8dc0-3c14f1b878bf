namespace Levelbuild.Core.FileInterface.Exception;

/// <summary>
/// Exception on deleting a file object
/// </summary>
public class FileDeleteException : System.Exception
{
	/// <summary>
	/// Constructor
	/// </summary>
	/// <param name="message"></param>
	public FileDeleteException(string message) : base(message)
	{
	}
	
	/// <summary>
	/// Constructor
	/// </summary>
	/// <param name="message"></param>
	/// <param name="inner"></param>
	public FileDeleteException(string message, System.Exception inner) : base(message, inner)
	{
	}
}