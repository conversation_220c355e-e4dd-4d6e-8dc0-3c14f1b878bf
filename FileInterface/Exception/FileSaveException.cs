namespace Levelbuild.Core.FileInterface.Exception;

/// <summary>
/// Exception on saving a file object
/// </summary>
public class FileSaveException : System.Exception
{
	/// <summary>
	/// Constructor
	/// </summary>
	/// <param name="message"></param>
	public FileSaveException(string message) : base(message)
	{
	}
	
	/// <summary>
	/// Constructor
	/// </summary>
	/// <param name="message"></param>
	/// <param name="inner"></param>
	public FileSaveException(string message, System.Exception inner) : base(message, inner)
	{
	}
}