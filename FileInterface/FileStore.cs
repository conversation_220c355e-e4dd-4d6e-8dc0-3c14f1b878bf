using Levelbuild.Core.FileInterface.Dto;

namespace Levelbuild.Core.FileInterface;

/// <summary>
/// Abstract file store to implement
/// </summary>
public abstract class FileStore
{
	internal const int MaxPathLength = 255;
	
	private AbstractFileStoreConfig _fileStoreConfig;
	
	/// <summary>
	/// Base path of file store
	/// </summary>
	// ReSharper disable once UnusedAutoPropertyAccessor.Global
	protected string BaseDir { get; }
	
	/// <summary>
	/// Constructor with needed config object
	/// </summary>
	/// <param name="fileStoreConfig"></param>
	public FileStore(AbstractFileStoreConfig fileStoreConfig)
	{
		_fileStoreConfig = fileStoreConfig;
		BaseDir = _fileStoreConfig.RootPath ??
				  throw new ArgumentException(
					  $"{this.GetType().Name}: Base directory was not set through config option: {nameof(fileStoreConfig.RootPath)}.");
	}
	
	/// <summary>
	/// Creates a Directory object, to use instead of a constructor
	/// </summary>
	/// <param name="path">full relative path</param>
	/// <returns></returns>
	public abstract DirectoryInfo GetDirectory(string path);
	
	/// <summary>
	/// Creates a File object, to use instead of a constructor
	/// </summary>
	/// <param name="path">the relative path</param>
	/// <returns></returns>
	public abstract FileInfo GetFile(string path);
	
	/// <summary>
	/// Configuration object to initialize file store (e.g. for Azure, ...)
	/// </summary>
	/// <returns></returns>
	public AbstractFileStoreConfig GetFileStoreConfig()
	{
		return _fileStoreConfig;
	}
}