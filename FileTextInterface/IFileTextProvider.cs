namespace Levelbuild.Core.FileTextInterface;

/// <summary>
/// This interface should be implemented in order to support fulltext search for this specific file type.
/// </summary>
public interface IFileTextProvider
{
	/// <summary>
	/// This method is used to determine which FileContentProvider should be used for the current file.
	/// </summary>
	/// <param name="fullFileName">The filename of the file in the form "Example.txt"</param>
	/// <param name="fileExtension">The file extension, beginning with the last dot in the filename, e.g. "txt"</param>
	/// <param name="first256Bytes">The first 256 bytes of the file content. Can be less if the file is not big enough to fill this buffer.</param>
	/// <param name="bufferedBytes">The full byte stream.</param>
	/// <returns>True if this implementation can extract content for a file with the provided metadata. False otherwise. Will be called to find the correct content extraction implementation for any provided file</returns>
	public bool CanExtractContent(string fullFileName, string fileExtension, byte[] first256Bytes, int bufferedBytes);

	/// <summary>
	/// This method is called once per file.
	/// </summary>
	/// <param name="fileStream">The file content</param>
	/// <param name="contentLengthLimitHint">The maximum number of characters that will be used. If the string is longer than specified here, only the first contentLengthLimitHint characters will be used.</param>
	/// <returns>The text content of the file that will be used for fulltext search</returns>
	public string? GetText(Stream fileStream, int contentLengthLimitHint);
}