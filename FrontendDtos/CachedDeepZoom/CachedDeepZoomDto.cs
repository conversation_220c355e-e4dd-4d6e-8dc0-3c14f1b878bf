using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.SharedDtos.Attributes;

namespace Levelbuild.Core.FrontendDtos.CachedDeepZoom;

[PublicApiInclude]
public class CachedDeepZoomDto : EntityDto
{
	public string DeepZoomImageId { get; set; }
	 
	public string FileId { get; set; }
	
	public int? Overlap { get; set; }
	
	public int? TileSize { get; set; }
	
	public int? Dpi { get; set; }
	
	public int? Width { get; set; }
	
	public int? Height { get; set; }
	
	public DateTime? LastTouched { get; set; }
	
	public CachedDeepZoomState State { get; set; }
	
	public string? ErrorMessage { get; set; }
	
	public int? ErrorCount { get; set; }
	
	[JsonConstructor]
	public CachedDeepZoomDto()
	{
	}

	public CachedDeepZoomDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects)
	{
	}

	/// <summary>
	/// are we allowed to start a new render thread for this image?
	/// </summary>
	/// <returns></returns>
	public bool AllowRendering()
	{
		if (State == CachedDeepZoomState.Ready)
			return false;

		// Retry if rendering is no longer running (indicated by LastTouched not being updated anymore)
		if (State == CachedDeepZoomState.Caching && (!LastTouched.HasValue || DateTime.Now.Subtract(LastTouched.Value).TotalSeconds > 60))
			return true;

		// retry failed rendering for at max 5 times
		if (State == CachedDeepZoomState.Failed && ErrorCount < 5)
			return true;

		return false;
	}
}