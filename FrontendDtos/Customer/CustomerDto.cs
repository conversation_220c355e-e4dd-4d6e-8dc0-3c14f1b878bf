using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.SharedDtos.Attributes;
#pragma warning disable CS8618, CS9264

namespace Levelbuild.Core.FrontendDtos.Customer;

[PublicApiInclude]
public class CustomerDto : EntityDto
{
	[HeaderValue]
	public string DisplayName { get; set; }
	
	[HeaderValue]
	[PublicApiExclude]
	public string? Slug { get; set; }
	
	[HeaderValue]
	public bool? Enabled { get; set; }
	
	[JsonConstructor]
	public CustomerDto() {}
	
	public CustomerDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects) {}
}