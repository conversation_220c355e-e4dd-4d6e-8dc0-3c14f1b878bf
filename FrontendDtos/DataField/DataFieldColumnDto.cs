using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.SharedDtos.Attributes;

namespace Levelbuild.Core.FrontendDtos.DataField;

/// <summary>
/// Columns 
/// </summary>
[PublicApiInclude]
public class DataFieldColumnDto : EntityDto
{
	/// <summary>
	/// Parent field id
	/// </summary>
	public Guid DataFieldId { get; set; }
	
	/// <summary>
	/// Parent field dto
	/// </summary>
	[PublicApiExclude]
	public DataFieldDto? DataField { get; set; }
	
	/// <summary>
	/// column position
	/// </summary>
	[HeaderValue]
	public int? Position { get; set; }
	
	/// <summary>
	/// Display field id
	/// </summary>
	public Guid DisplayFieldId { get; set; }
	
	/// <summary>
	/// Display field dto
	/// </summary>
	[PublicApiExclude]
	public DataFieldDto? DisplayField { get; set; }
	
	/// <summary>
	/// Name of the display field
	/// </summary>
	[HeaderValue]
	public String? DisplayFieldName { get; set; }
	
	
	/// <inheritdoc />
	[JsonConstructor]
	public DataFieldColumnDto() {}
	
	/// <inheritdoc />
	public DataFieldColumnDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects) {}
}