using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.DataSource;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.SharedDtos.Attributes;
using Levelbuild.Core.SharedDtos.Enums;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Core.FrontendDtos.DataField;

[PublicApiInclude]
public class DataFieldDto : EntityDto
{
	public Guid? DataSourceId { get; set; }

	[PublicApiExclude]
	public DataSourceDto? DataSource { get; set; }

	[HeaderValue]
	public string? Name { get; set; }
	
	public string? NameTranslated { get; set; }

	[HeaderValue]
	[PublicApiExclude]
	public string? Slug { get; set; }

	[HeaderValue]
	public DataFieldType FieldType { get; set; }

	[HeaderValue]
	public DataType? Type { get; set; }

	[JsonConverter(typeof(InputDataTypeConverter))]
	public InputDataType? InputDataType { get; set; }

	[HeaderValue]
	public string? ListIcon { get; set; }

	[HeaderValue]
	public int? Length { get; set; }

	public int? DecimalPlaces { get; set; }

	public bool? RichText { get; set; }

	public bool? Mandatory { get; set; }

	public bool? Unique { get; set; }

	[HeaderValue]
	public bool? Multi { get; set; }

	[HeaderValue]
	public bool? SystemField { get; set; }
	
	[HeaderValue]
	public bool? AutoGenerated { get; set; }

	public DataFieldFormatType? FormatType { get; set; }

	public string? LabelTrue { get; set; }
	
	public string? LabelTrueTranslated { get; set; }

	public string? LabelFalse { get; set; }
	
	public string? LabelFalseTranslated { get; set; }

	//lookup
	public Guid? LookupSourceId { get; set; }

	[PublicApiExclude]
	public DataSourceDto? LookupSource { get; set; }

	public Guid? LookupDisplayFieldId { get; set; }

	[PublicApiExclude]
	public DataFieldDto? LookupDisplayField { get; set; }
	
	public bool? ColumnView { get; set; }
	
	public bool? FilterSelf { get; set; }
	
	//virtual
	public Guid? VirtualLookupFieldId { get; set; }

	[PublicApiExclude]
	public DataFieldDto? VirtualLookupField { get; set; }

	public Guid? VirtualDataFieldId { get; set; }

	[PublicApiExclude]
	public DataFieldDto? VirtualDataField { get; set; }
	
	[PublicApiExclude]
	public List<Guid>? ReferencingVirtualFieldIds { get; set; }
	
	public string? DefaultValue { get; set; }
	
	public ICollection<DataFieldColumnDto>? Columns { get; set; }
	
	public ICollection<DataFieldFilterDto>? Filters { get; set; }
	
	public string? Sign { get; set; }
	
	[JsonConstructor]
	public DataFieldDto()
	{
	}

	public DataFieldDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects)
	{
	}
}