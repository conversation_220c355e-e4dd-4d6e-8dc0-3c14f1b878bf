using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.DataSource;
using Levelbuild.Core.SharedDtos.Attributes;

namespace Levelbuild.Core.FrontendDtos.DataField;

[PublicApiInclude]
public class DataFieldFilterDto : DataSourceFilterDto
{
	[HeaderValue]
	public Guid? DataFieldId { get; set; }
	
	[PublicApiExclude]
	public DataFieldDto? DataField { get; set; }
	
	[JsonConstructor]
	public DataFieldFilterDto() {}
	
	public DataFieldFilterDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects) {}
	
}