using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.SharedDtos.Enums;

namespace Levelbuild.Core.FrontendDtos.DataField;

/// <summary>
/// Includes only those information which are needed to format a plain value of this field properly
/// </summary>
public class DataFieldFormatDto
{
	/// <summary>
	/// field name
	/// </summary>
	public string Name { get; set; }
	
	/// <summary>
	/// field type
	/// </summary>
	public DataType? Type { get; set; }
	
	/// <summary>
	/// Optional unit symbol
	/// </summary>
	public string? Sign { get; set; }
	
	/// <summary>
	///  number of decimal places (if field is of type double)
	/// </summary>
	public int? DecimalPlaces { get; set; }
	
	public DataFieldFormatDto(DataFieldDto fieldDto)
	{
		Name = fieldDto.Name!;
		Type = fieldDto.FieldType == DataFieldType.LookupField ? fieldDto.LookupDisplayField?.Type : fieldDto.Type;
		Sign = fieldDto.FieldType == DataFieldType.LookupField ? fieldDto.LookupDisplayField?.Sign : fieldDto.Sign;
		DecimalPlaces = fieldDto.FieldType == DataFieldType.LookupField ? fieldDto.LookupDisplayField?.DecimalPlaces : fieldDto.DecimalPlaces;
	}
}