using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.SharedDtos.Attributes;

namespace Levelbuild.Core.FrontendDtos.DataField;

/// <summary>
/// Default Sorting in Pages
/// </summary>
public class DataFieldSortingDto : EntityDto
{
	/// <summary>
	/// Parent entity
	/// </summary>
	public Guid PageId { get; set; }
	
	/// <summary>
	/// Is used for multiple default sorting e.g. 1#name, 2#id 
	/// </summary>
	[HeaderValue]
	public int? Position { get; set; }
	
	/// <summary>
	/// Order by this field 
	/// </summary>
	[PublicApiExclude]
	public DataFieldDto? Field { get; set; }
	
	/// <summary>
	/// ID of the order by field
	/// </summary>
	public Guid? FieldId { get; set; }
	
	/// <summary>
	/// Name of the order by field
	/// </summary>
	[HeaderValue]
	[PublicApiExclude]
	public string? FieldName { get; set; }
	
	/// <summary>
	/// Sort direction 
	/// </summary>
	[HeaderValue]
	public SortDirection Direction { get; set; }
	
	/// <inheritdoc />
	[JsonConstructor]
	public DataFieldSortingDto() {}
	
	/// <inheritdoc />
	public DataFieldSortingDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects) {}
}