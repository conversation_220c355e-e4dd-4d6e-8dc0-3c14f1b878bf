using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.DataField;
using Levelbuild.Core.FrontendDtos.DataStoreConfig;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Module;
using Levelbuild.Core.FrontendDtos.Page;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.FrontendDtos.Workflow;
using Levelbuild.Core.SharedDtos.Attributes;
#pragma warning disable CS8618, CS9264

namespace Levelbuild.Core.FrontendDtos.DataSource;

[PublicApiInclude]
public class DataSourceDto : EntityDto
{
	[PublicApiExclude]
	public Guid DataStoreId { get; set; }
	
	[PublicApiExclude]
	public DataStoreConfigDto? DataStore { get; set; }
	
	[HeaderValue]
	[PublicApiExclude]
	public string? Slug { get; set; }
	
	[HeaderValue]
	public string Name { get; set; }
	
	public DataSourceType? Type { get; set; }
	
	public Guid? AnnotationSourceId { get; set; }
		
	[PublicApiExclude]
	public DataSourceDto? AnnotationSource { get; set; }
	
	public Guid? AnnotationKeyFieldId { get; set; }
		
	[PublicApiExclude]
	public DataFieldDto? AnnotationKeyField { get; set; }
	
	public Guid? AnnotationGroupByFieldId { get; set; }
	
	[PublicApiExclude]
	public DataFieldDto? AnnotationGroupByField { get; set; }
	
	public string? AnnotationLabel { get; set; }
	
	public Guid? AnnotationCreatePageId { get; set; }
	
	[PublicApiExclude]
	public CreatePageDto? AnnotationCreatePage { get; set; }
	
	public Guid? AnnotationDetailPageId { get; set; }
	
	[PublicApiExclude]
	public SingleDataPageDto? AnnotationDetailPage { get; set; }
	
	[HeaderValue]
	public DateTime? Created { get; set; }
	
	public string? CreatedBy { get; set; }
	
	[HeaderValue]
	public DateTime? LastModified { get; set; }
	
	[HeaderValue]
	public string? LastModifiedBy { get; set; }
	
	public string? Responsible { get; set; }
	
	public string? Comment { get; set; }
	
	public string? Icon { get; set; }

	public bool? StoreRevision { get; set; }
	
	public bool? FulltextSearch { get; set; }
	
	public string? StoragePath { get; set; }
	
	public bool? Encryption { get; set; }
	
	public bool? StoreFieldContent { get; set; }
	
	public bool? StoreFileContent { get; set; }
	
	public bool? AllowFile { get; set; }
	
	public Guid? Revision { get; set; }
	
	public Guid? ModuleId { get; set; }
	
	[HeaderValue]
	public ModuleDto? Module { get; set; }
	
	[HeaderValue]
	public string? ModuleName { get; set; }
	
	[HeaderValue]
	public string? TypeName { get; set; }
	
	public bool? GenerateDeepZoomImage { get; set; }
	
	public bool? Favor { get; set; }
	
	public bool? Inactive { get; set; }
	
	public Guid? DefaultDetailPageId { get; set; }
	
	[PublicApiExclude]
	public SingleDataPageDto? DefaultDetailPage { get; set; }
	
	public ICollection<DataFieldDto>? Fields { get; set; } = new List<DataFieldDto>();
	
	public ICollection<WorkflowDto>? Workflows { get; set; } = new List<WorkflowDto>();
	
	[JsonConstructor]
	public DataSourceDto() {}
	
	public DataSourceDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects) {}
}