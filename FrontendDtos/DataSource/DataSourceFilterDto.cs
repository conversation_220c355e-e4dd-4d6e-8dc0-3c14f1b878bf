using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.SharedDtos.Attributes;
using Levelbuild.Core.SharedDtos.Enums;

namespace Levelbuild.Core.FrontendDtos.DataSource;

[PublicApiInclude]
public class DataSourceFilterDto : EntityDto
{
	[HeaderValue]
	public Guid? FilterFieldId { get; set; }
	
	[HeaderValue]
	public string? FilterFieldName { get; set; }
	
	[HeaderValue]
	public DataType? FilterFieldType { get; set; }
	
	[HeaderValue]
	public CompareOperator? Operator { get; set; }
	
	[HeaderValue]
	public string? CompareValue { get; set; }
	
	[JsonConstructor]
	public DataSourceFilterDto() {}
	
	public DataSourceFilterDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects) {}
}