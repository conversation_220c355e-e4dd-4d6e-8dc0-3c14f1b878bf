using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.SharedDtos.Attributes;

namespace Levelbuild.Core.FrontendDtos.DataSource;

[PublicApiInclude]
public class ElementActionDto : EntityDto
{
	public ElementActionType Type { get; set; }
	
	public object? Value { get; set; }

	public IList<string>? Elements { get; set; }
	
	[JsonConstructor]
	public ElementActionDto() {}
	
	public ElementActionDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects) {}
}