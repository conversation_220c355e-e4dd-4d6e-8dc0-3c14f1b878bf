using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.SharedDtos.Attributes;

#pragma warning disable CS8618, CS9264

namespace Levelbuild.Core.FrontendDtos.DataStoreConfig;

[PublicApiInclude]
public class DataStoreConfigDto : EntityDto
{
	public DataStoreType? Type { get; set; }
	
	[HeaderValue]
    public string TypeName { get; set; }
    
	[HeaderValue]
	public string? Name { get; set; }

	[HeaderValue]
	public string? Slug { get; set; }

	[HeaderValue]
	public bool? Enabled { get; set; } = true;
	
	public Dictionary<string, object>? Options { get; set; } 
	
	[JsonConstructor]
	public DataStoreConfigDto() {}
	
	public DataStoreConfigDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects) {}
}