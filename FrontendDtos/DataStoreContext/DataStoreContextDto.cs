using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.Customer;
using Levelbuild.Core.FrontendDtos.DataStoreConfig;
using Levelbuild.Core.FrontendDtos.Shared;
#pragma warning disable CS8618, CS9264

namespace Levelbuild.Core.FrontendDtos.DataStoreContext;

public class DataStoreContextDto : EntityDto
{
	[HeaderValue]
	public string? Name { get; set; }
	
	[HeaderValue]
	public string? Slug { get; set; }
	
	[HeaderValue]
	public bool? Enabled { get; set; } = true;
	
	[HeaderValue]
	public Guid? CustomerId { get; set; }
	
	[HeaderValue]
	public string CustomerName { get; set; }
	
	[HeaderValue]
	public Guid? DataStoreId { get; set; }
	
	[HeaderValue]
	public DataStoreConfigDto? DataStore { get; set; }
	
	[HeaderValue]
	public CustomerDto? Customer { get; set; }
	
	[HeaderValue]
	public Dictionary<string, object>? Options { get; set; }
	
	[JsonConstructor]
	public DataStoreContextDto() {}
	
	public DataStoreContextDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects) {}
}