using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.SharedDtos.Attributes;

namespace Levelbuild.Core.FrontendDtos.Device;

[PublicApiInclude]
public class DeviceDto : EntityDto
{
	[HeaderValue]
	public bool? Enabled { get; set; }
	
	[HeaderValue]
	[PublicApiExclude]
	public string? Slug { get; set; }
	
	[HeaderValue]
	public string? DisplayName { get; set; }
	
	public string? ApiKey { get; set; }
	
	[HeaderValue]
	public DeviceType? Type { get; set; }
	
	[HeaderValue]
	[PublicApiExclude]
	public string? Icon => Type?.GetIcon();
	
	[HeaderValue]
	[PublicApiExclude]
	public string? TypeName { get; set; }
	
	[HeaderValue]
	public DeviceFormat? Format { get; set; }
	
	[HeaderValue]
	[PublicApiExclude]
	public string? FormatName { get; set; }
	
	[PublicApiExclude]
	public Guid? UserId { get; set; }
	
	[PublicApiExclude]
	public DateTime? Created { get; set; }
	
	[PublicApiExclude]
	public string? CreatedBy { get; set; }
	
	[PublicApiExclude]
	public DateTime? LastModified { get; set; }
	
	[PublicApiExclude]
	public string? LastModifiedBy { get; set; }
	
	public string? PasskeyId { get; set; }
	
	[JsonConstructor]
	public DeviceDto() {}
	
	// TODO: Can probably be removed once the App doesn't use this anymore
	/// <summary>
	/// Public API only!
	/// </summary>
	public string? PasskeyCredentials { get; set; }
	
	/// <summary>
	/// Public API only!
	/// </summary>
	public string? PasskeyRegistrationUrl { get; set; }
	
	public DeviceDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects) {}
}