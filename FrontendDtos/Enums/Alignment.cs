using System.Text.Json.Serialization;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.SharedDtos.Attributes;
using Microsoft.OpenApi.Extensions;

namespace Levelbuild.Core.FrontendDtos.Enums
{
	[PublicApiInclude]
	[JsonConverter(typeof(JsonStringEnumConverter))]
	public enum Alignment
	{
		Left,
		Right,
		Center,
		Justify
	}
}

public static class AlignmentExtensions
{
	public static string GetAlignmentAsString(this Alignment enumValue)
	{
		return enumValue.GetDisplayName().ToLower();
	}
}