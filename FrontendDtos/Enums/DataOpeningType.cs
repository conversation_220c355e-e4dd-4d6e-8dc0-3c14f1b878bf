using System.Text.Json.Serialization;
using Levelbuild.Core.SharedUtilities;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Core.FrontendDtos.Enums
{
	/// <summary>
	/// Options for displaying a single data record
	/// </summary>
	[JsonConverter(typeof(JsonStringEnumConverter))]
	public enum DataOpeningType
	{
		[DataOpeningTypeDescriptor] InTable,
		[DataOpeningTypeDescriptor] Directly
	}
	
	[AttributeUsage(AttributeTargets.Field)]
	public class DataOpeningTypeDescriptorAttribute : Attribute;
	
	public static class DataOpeningTypeExtensions
	{
		public static string GetString(this DataOpeningType enumValue)
		{
			return EnumUtils<DataOpeningType>.GetTranslatableString(enumValue);
		}
	}
	
	
}