using System.Text.Json.Serialization;
using Levelbuild.Core.SharedDtos.Attributes;

namespace Levelbuild.Core.FrontendDtos.Enums;

[PublicApiInclude]
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum DataSourceType
{
	Default,	// a data source without any special functionality
	Blueprint,	// a data source containing blueprints (enables automatic blueprint conversion)
	Annotation,	// a data source containing blueprint annotations (creates default fields for annotation positioning)
	ElementType	// a data source where each element represents a type (those types are used by referencing the datasource via FKs / creates default icon field)
}