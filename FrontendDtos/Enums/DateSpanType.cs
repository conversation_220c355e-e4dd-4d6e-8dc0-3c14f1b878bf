using System.Text.Json.Serialization;
using Levelbuild.Core.SharedDtos.Attributes;
using Levelbuild.Core.SharedUtilities;
using Microsoft.OpenApi.Extensions;

namespace Levelbuild.Core.FrontendDtos.Enums;

[PublicApiInclude]
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum DateSpanType
{
	[DateSpanTypeDescriptor()]
	Today,
	
	[DateSpanTypeDescriptor()]
	Yesterday,
	
	[DateSpanTypeDescriptor()]
	CurrentWeek,
	
	[DateSpanTypeDescriptor()]
	LastWeek,
	
	[DateSpanTypeDescriptor()]
	CurrentMonth,
	
	[DateSpanTypeDescriptor()]
	LastMonth,
	
	[DateSpanTypeDescriptor()]
	CurrentYear,
	
	[DateSpanTypeDescriptor()]
	LastYear,
	
	[DateSpanTypeDescriptor()]
	Older,
}

[AttributeUsage(AttributeTargets.Field)]
public class DateSpanTypeDescriptorAttribute : Attribute
{
	
}

public static class DateSpanTypeExtensions
{
	public static string GetName(this DateSpanType enumValue)
	{
		return enumValue.GetDisplayName();
	}

	public static string GetString(this DateSpanType enumValue)
	{
		return EnumUtils<DateSpanType>.GetTranslatableString(enumValue);
	}
}