using System.Text.Json.Serialization;
using Levelbuild.Core.SharedDtos.Attributes;
using Levelbuild.Core.SharedUtilities;

namespace Levelbuild.Core.FrontendDtos.Enums;

[PublicApiInclude]
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum DeviceType
{
	[DeviceTypeDescriptor("android")]
	Android,
	[DeviceTypeDescriptor("apple")]
	Ios,
	[DeviceTypeDescriptor("robot")]
	ApiClient,
}

[AttributeUsage(AttributeTargets.Field)]
public class DeviceTypeDescriptorAttribute : Attribute
{
	public string Icon { get; }
	
	public DeviceTypeDescriptorAttribute(string icon)
	{
		Icon = icon;
	}
}

public static class DeviceTypeExtensions
{
	public static string GetIcon(this DeviceType value)
	{
		return GetAttributes(value).Icon;
	}
	
	private static DeviceTypeDescriptorAttribute GetAttributes(DeviceType enumValue)
	{
		return (EnumUtils<DeviceType>.GetAttributeFromEnum<DeviceTypeDescriptorAttribute>(enumValue) as DeviceTypeDescriptorAttribute)!;
	}
}