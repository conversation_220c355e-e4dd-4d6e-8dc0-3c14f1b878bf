using System.Text.Json.Serialization;
using Levelbuild.Core.SharedUtilities;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Core.FrontendDtos.Enums
{
	/// <summary>
	/// Options for displaying a single data record
	/// </summary>
	[JsonConverter(typeof(JsonStringEnumConverter))]
	public enum DimensionUnit
	{
		[DimensionUnitDescriptor("px")] Pixel,
		[DimensionUnitDescriptor("%")] Percent,
	}
	
	[AttributeUsage(AttributeTargets.Field)]
	public class DimensionUnitDescriptorAttribute : Attribute
	{
		public string Sign { get; set; }
		
		public DimensionUnitDescriptorAttribute(string sign)
		{
			Sign = sign;
		}
	};
	
	public static class DimensionUnitExtensions
	{
		public static string GetSign(this DimensionUnit enumValue)
		{
			var attr = GetAttributes(enumValue);
			return attr.Sign;
		}
		
		private static DimensionUnitDescriptorAttribute GetAttributes(DimensionUnit enumValue)
		{
			return (EnumUtils<DimensionUnit>.GetAttributeFromEnum<DimensionUnitDescriptorAttribute>(enumValue) as DimensionUnitDescriptorAttribute)!;
		}
	}
	
	
}