using System.Text.Json.Serialization;
using Levelbuild.Core.SharedDtos.Attributes;

namespace Levelbuild.Core.FrontendDtos.Enums;

[PublicApiInclude]
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum ElementActionType
{
	[ElementActionTypeDescriptor]
	Favorite,
	
	[ElementActionTypeDescriptor]
	Inactive
}

[AttributeUsage(AttributeTargets.Field)]
public class ElementActionTypeDescriptorAttribute : Attribute;

