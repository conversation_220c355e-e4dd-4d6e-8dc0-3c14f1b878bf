using System.Text.Json.Serialization;
using Levelbuild.Core.SharedDtos.Attributes;

namespace Levelbuild.Core.FrontendDtos.Enums;

/// <summary>
/// enum to describe how the GridViewPage is connected to the page/dataset it is embedded into
/// </summary>
[PublicApiInclude]
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum GridViewPageReferenceType
{
	Self = 0,          // GridViewPage is referencing the current page/dataset directly
	ForeignElement = 1 // GridViewPage is referencing a foreign dataset referenced by the current dataset via a foreign key
}