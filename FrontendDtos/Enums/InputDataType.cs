#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member
using System.Text.Json;
using System.Text.Json.Serialization;
using Levelbuild.Core.SharedDtos.Attributes;
using Levelbuild.Core.SharedDtos.Enums;
using Levelbuild.Core.SharedUtilities;
using Microsoft.OpenApi.Extensions;

namespace Levelbuild.Core.FrontendDtos.Enums;

/// <summary>
/// Enum containing all data types relevant for frontend inputs and tag helpers.
/// <br/><br/>
/// If you need to serialize this type with the null value "Default" use <see cref="InputDataTypeConverter"/> above the property definition to override the default JsonConverter
/// </summary>
[PublicApiInclude]
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum InputDataType
{
	[InputDataTypeDescriptor(DataType.String, false, false, true)]
	String,
	
	[InputDataTypeDescriptor(DataType.Integer, false, true, false)]
	Integer,
	
	[InputDataTypeDescriptor(DataType.Long, false, true, false)]
	Long,
	
	[InputDataTypeDescriptor(DataType.Double, false, true, false)]
	Double,
	
	[InputDataTypeDescriptor(DataType.Date, false, false, false, true)]
	Date,
	
	[InputDataTypeDescriptor(DataType.DateTime, false, false, false, true, true)]
	DateTime,
	
	[InputDataTypeDescriptor(DataType.Time, false, false, false, false, true)]
	Time,
	
	[InputDataTypeDescriptor(DataType.Text, false, false, true)]
	Text,
	
	[InputDataTypeDescriptor(DataType.Boolean, false, false, false)]
	Boolean,
	
	[InputDataTypeDescriptor(DataType.String, true, false, true)]
	Url,
	
	[InputDataTypeDescriptor(DataType.String, false, false, true)]
	Enum,
	
	[InputDataTypeDescriptor(DataType.String, false, false, true)]
	Search,
	
	[InputDataTypeDescriptor(DataType.Guid, true, false, false)]
	Guid,
	
	[InputDataTypeDescriptor(DataType.String, true, false, true)]
	Icon,
	
	[InputDataTypeDescriptor(DataType.String, true, false, true)]
	Translation,
	
	[InputDataTypeDescriptor(DataType.String, true, false, true)]
	Color,
	
	[InputDataTypeDescriptor(DataType.String, false, false, true)]
	Password,
	
	[InputDataTypeDescriptor(DataType.String, true, false, true)]
	Email,
	
	[InputDataTypeDescriptor(DataType.Guid, true, false, false)]
	Annotation,
}

[AttributeUsage(AttributeTargets.Field)]
public class InputDataTypeDescriptorAttribute(DataType dataType, bool isSubtype, bool isNumeric, bool isText, bool isDate = false, bool isTime = false)
	: Attribute
{
	public bool IsSubtype { get; set; } = isSubtype;
	
	public bool IsText { get; set; } = isText;

	public bool IsNumeric { get; set; } = isNumeric;

	public bool IsDate { get; set; } = isDate;

	public bool IsTime { get; set; } = isTime;

	public DataType DataType { get; set; } = dataType;
}

public static class InputDataTypeExtensions
{
	public static string GetTypeAsString(this InputDataType enumValue)
	{
		return enumValue.GetDisplayName().ToLower();
	}
	
	public static DataType DataType(this InputDataType enumValue)
	{
		var attr = GetAttributes(enumValue);
		return attr.DataType;
	}
	
	public static bool IsSubtype(this InputDataType enumValue)
	{
		var attr = GetAttributes(enumValue);
		return attr.IsSubtype;
	}
	
	public static bool IsNumeric(this InputDataType enumValue)
	{
		var attr = GetAttributes(enumValue);
		return attr.IsNumeric;
	}
	
	public static bool IsDate(this InputDataType enumValue)
	{
		var attr = GetAttributes(enumValue);
		return attr.IsDate;
	}
	
	public static bool IsTime(this InputDataType enumValue)
	{
		var attr = GetAttributes(enumValue);
		return attr.IsTime;
	}
	
	public static bool IsText(this InputDataType enumValue)
	{
		var attr = GetAttributes(enumValue);
		return attr.IsText;
	}
	
	public static string GetString(this InputDataType enumValue)
	{
		return EnumUtils<InputDataType>.GetTranslatableString(enumValue);
	}
	
	private static InputDataTypeDescriptorAttribute GetAttributes(InputDataType enumValue)
	{
		return (EnumUtils<InputDataType>.GetAttributeFromEnum<InputDataTypeDescriptorAttribute>(enumValue) as InputDataTypeDescriptorAttribute)!;
	}
}

/// <summary>
/// Converter to serialize the InputDataType in a string and deserialize a string or a number in an InputDataType
/// </summary>
public class InputDataTypeConverter : JsonConverter<InputDataType?>
{
	/// <summary>
	/// Deserialization method for string to enum
	/// </summary>
	/// <param name="reader"></param>
	/// <param name="typeToConvert"></param>
	/// <param name="options"></param>
	/// <returns></returns>
	public override InputDataType? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
	{
		if (reader.TokenType == JsonTokenType.String)
		{
			var stringValue = reader.GetString();

			// Return null if the value is "Default" or empty
			if (stringValue == "Default" || string.IsNullOrEmpty(stringValue))
				return null;
				
			// Try parsing the enum value
			if (Enum.TryParse<InputDataType>(stringValue, ignoreCase: true, out var result))
			{
				return result;
			}
		}
		else if (reader.TokenType == JsonTokenType.Number)
        {
            // Handle numeric representation
            var value = reader.GetInt32();
            if (Enum.IsDefined(typeof(InputDataType), value))
                return (InputDataType)value;
        }


		throw new JsonException($"Unable to convert \"{reader.GetString()}\" to {nameof(InputDataType)}.");
	}

	/// <summary>
	/// Serialization method for enum to string
	/// </summary>
	/// <param name="writer"></param>
	/// <param name="value"></param>
	/// <param name="options"></param>
	public override void Write(Utf8JsonWriter writer, InputDataType? value, JsonSerializerOptions options)
	{
		if (value.HasValue)
		{
			writer.WriteStringValue(value.GetDisplayName());
		}
		else
		{
			writer.WriteNullValue();
		}
	}
}