using System.Text.Json.Serialization;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.SharedDtos.Attributes;
using Microsoft.OpenApi.Extensions;

namespace Levelbuild.Core.FrontendDtos.Enums
{
	[PublicApiInclude]
	[JsonConverter(typeof(JsonStringEnumConverter))]
	public enum Size
	{
		Small,
		Medium,
		Large
	}
}

public static class SizeExtensions
{
	public static string GetSizeAsString(this Size enumValue)
	{
		return enumValue.GetDisplayName().ToLower();
	}
}