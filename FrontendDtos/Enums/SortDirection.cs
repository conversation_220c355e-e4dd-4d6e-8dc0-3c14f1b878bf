using System.Text.Json.Serialization;
using Levelbuild.Core.SharedDtos.Attributes;
using Microsoft.OpenApi.Extensions;

namespace Levelbuild.Core.FrontendDtos.Enums;

/// <summary>
/// Sort Direction
/// </summary>
[PublicApiInclude]
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum SortDirection
{
	/// <summary>
	/// Sort direction Asc
	/// </summary>
	[SortDirectionDescriptor]
	Asc,
	
	/// <summary>
	/// Sort direction Desc
	/// </summary>
	[SortDirectionDescriptor]
	Desc,
}

/// <inheritdoc />
[AttributeUsage(AttributeTargets.Field)]
public class SortDirectionDescriptorAttribute : Attribute
{
	/// <inheritdoc />
	public SortDirectionDescriptorAttribute()
	{
		
	}
}

/// <summary>
/// Extensions to call methods on the enum 
/// </summary>
public static class SortDirectionExtensions
{
	/// <summary>
	/// Enum value as string and to lower case
	/// </summary>
	/// <param name="enumValue"></param>
	/// <returns></returns>
	public static string GetSorting(this SortDirection enumValue)
	{
		return enumValue.GetDisplayName().ToLower();
	}
}

