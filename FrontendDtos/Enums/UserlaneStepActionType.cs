using System.Text.Json.Serialization;
using Levelbuild.Core.FrontendDtos.Exceptions;

namespace Levelbuild.Core.FrontendDtos.Enums;

[JsonConverter(typeof(JsonStringEnumConverter))]
public enum UserlaneStepActionType
{
	[UserlaneStepActionTypeDescriptor("click")] Click = 0,
	[UserlaneStepActionTypeDescriptor("Set Value")] SetValue = 1,
	[UserlaneStepActionTypeDescriptor("Listen for events")] Listen = 2,
	[UserlaneStepActionTypeDescriptor("Open listed item")] OpenListItem = 3,
}

[AttributeUsage(AttributeTargets.Field)]
public class UserlaneStepActionTypeDescriptorAttribute : Attribute
{
	public string Label { get; }

	public UserlaneStepActionTypeDescriptorAttribute(string label)
	{
		Label = label;
	}
}

public static class UserlaneStepActionTypesExtensions
{
	public static string GetString(this UserlaneStepActionType value)
	{
		return Enum.GetName(value)!;
	}
	
	public static string GetLabel(this UserlaneStepActionType label)
	{
		var storeTypeAttributes = GetAttributes(label);
		return storeTypeAttributes.Label;
	}
	
	private static UserlaneStepActionTypeDescriptorAttribute GetAttributes(UserlaneStepActionType value)
	{
		var type = value.GetType();
		var name = Enum.GetName(type, value);
		if (name == null) 
			throw new EnumNotFoundException();

		var field = type.GetField(name);
		if (field == null || Attribute.GetCustomAttribute(field, typeof(UserlaneStepActionTypeDescriptorAttribute)) is not UserlaneStepActionTypeDescriptorAttribute customAttribute) 
			throw new EnumNotFoundException();

		return customAttribute;
	}

}