using System.Text.Json.Serialization;
using Levelbuild.Core.FrontendDtos.Exceptions;

namespace Levelbuild.Core.FrontendDtos.Enums;

[JsonConverter(typeof(JsonStringEnumConverter))]
public enum UserlaneStepTestConditionType
{
	[UserlaneStepTestConditionTypeDescriptor("Field Comparison")] FieldComparison = 0,
	[UserlaneStepTestConditionTypeDescriptor("Element Exists")] ElementExists = 1,
	[UserlaneStepTestConditionTypeDescriptor("Element Visible")] ElementVisible = 2,
	[UserlaneStepTestConditionTypeDescriptor("Text Contains")] TextContains = 3,
	[UserlaneStepTestConditionTypeDescriptor("Value Equals")] ValueEquals = 4,
}

[AttributeUsage(AttributeTargets.Field)]
public class UserlaneStepTestConditionTypeDescriptorAttribute : Attribute
{
	public string Label { get; }

	public UserlaneStepTestConditionTypeDescriptorAttribute(string label)
	{
		Label = label;
	}
}

public static class UserlaneStepTestConditionTypeExtensions
{
	public static string GetString(this UserlaneStepTestConditionType value)
	{
		return Enum.GetName(value)!;
	}
	
	public static string GetLabel(this UserlaneStepTestConditionType label)
	{
		var storeTypeAttributes = GetAttributes(label);
		return storeTypeAttributes.Label;
	}
	
	private static UserlaneStepTestConditionTypeDescriptorAttribute GetAttributes(UserlaneStepTestConditionType value)
	{
		var type = value.GetType();
		var name = Enum.GetName(type, value);
		if (name == null) 
			throw new EnumNotFoundException();

		var field = type.GetField(name);
		if (field == null || Attribute.GetCustomAttribute(field, typeof(UserlaneStepTestConditionTypeDescriptorAttribute)) is not UserlaneStepTestConditionTypeDescriptorAttribute customAttribute) 
			throw new EnumNotFoundException();

		return customAttribute;
	}

}