using System.Text.Json.Serialization;
using Levelbuild.Core.FrontendDtos.Exceptions;

namespace Levelbuild.Core.FrontendDtos.Enums;

[JsonConverter(typeof(JsonStringEnumConverter))]
public enum UserlaneType
{
	[UserlanesTypeDescriptor("Tour")] Tour = 0,
	[UserlanesTypeDescriptor("Test")] Test = 1,
}

[AttributeUsage(AttributeTargets.Field)]
public class UserlanesTypeDescriptorAttribute(string label) : Attribute
{
	public string Label { get; } = label;
}

public static class UserlanesTypeExtensions
{
	public static string? GetString(this UserlaneType value)
	{
		return Enum.GetName(value);
	}
	
	public static string GetLabel(this UserlaneType value)
	{
		var storeTypeAttributes = GetAttributes(value);
		return storeTypeAttributes.Label;
	}
	
	private static UserlanesTypeDescriptorAttribute GetAttributes(UserlaneType value)
	{
		var type = value.GetType();
		var name = Enum.GetName(type, value);
		if (name == null) 
			throw new EnumNotFoundException();

		var field = type.GetField(name);

		if (field == null) throw new EnumNotFoundException();
		
		var customAttribute = Attribute.GetCustomAttribute(field, typeof(UserlanesTypeDescriptorAttribute)) as UserlanesTypeDescriptorAttribute;
		if (customAttribute == null)
			throw new EnumNotFoundException();
		
		return customAttribute;

	}

}