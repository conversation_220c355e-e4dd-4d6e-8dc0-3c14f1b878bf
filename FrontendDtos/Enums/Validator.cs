using System.Text.Json.Serialization;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.SharedDtos.Attributes;
using Levelbuild.Core.SharedUtilities;

namespace Levelbuild.Core.FrontendDtos.Enums
{
	[PublicApiInclude]
	[JsonConverter(typeof(JsonStringEnumConverter))]
	public enum Validator
	{
		[ValidatorDescriptor("^[a-zA-Z0-9]+$")]
		NoSpecialSigns,
		
		[ValidatorDescriptor("^(?!.*__)[a-zA-Z0-9_]+$")]
		StorageFieldReserved,
		
		[ValidatorDescriptor(@"^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[^A-Za-z\d])[A-Za-z\d\S]{8,}$")]
		ZitadelPassword
	}
}

[AttributeUsage(AttributeTargets.Field)]
public class ValidatorDescriptorAttribute : Attribute
{
	public string Pattern { get; }
	
	public ValidatorDescriptorAttribute(string pattern)
	{
		Pattern = pattern;
	}
}

public static class ValidatorExtensions
{
	public static string GetPattern(this Validator value)
	{
		return GetAttributes(value).Pattern;
	}
	
	private static ValidatorDescriptorAttribute GetAttributes(Validator enumValue)
	{
		return (EnumUtils<Validator>.GetAttributeFromEnum<ValidatorDescriptorAttribute>(enumValue) as ValidatorDescriptorAttribute)!;
	}
}