using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.SharedDtos.Enums;

namespace Levelbuild.Core.FrontendDtos.Extensions;

public static class DataTypeExtension
{
	/// <summary>
	/// Return the InputDataType equivalent to DataType
	/// </summary>
	/// <param name="typeToParse">Datatype which has to be parsed</param>
	/// <returns></returns>
	public static InputDataType ToInputDataType(this DataType typeToParse)
	{
		return typeToParse switch
		{
			DataType.TimeFixed => InputDataType.Time,
			DataType.DateTimeFixed => InputDataType.DateTime,
			_ => Enum.TryParse(typeof(InputDataType), typeToParse.GetTypeAsString(), true, out var parsedInputType)
					 ? (InputDataType)parsedInputType
					 : InputDataType.String
		};
	}
}