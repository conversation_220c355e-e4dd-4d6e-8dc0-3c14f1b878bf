<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>

        <RootNamespace>Levelbuild.Core.FrontendDtos</RootNamespace>
        <Company>levelbuild AG</Company>
        <PackageId>levelbuild.FrontendDtos</PackageId>
        
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
      <NoWarn>1701;1702;1591</NoWarn>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <NoWarn>1701;1702;1591</NoWarn>
    </PropertyGroup>

    
    <!-- local solution reference -->
    <ItemGroup>
      <ProjectReference Include="..\EntityInterface\EntityInterface.csproj" />
      <ProjectReference Include="..\SharedDtos\SharedDtos.csproj" />
    </ItemGroup>

    
    <ItemGroup>
      <PackageReference Include="ClosedXML" Version="0.104.2" />
      <PackageReference Include="ExcelDataReader" Version="3.7.0" />
      <PackageReference Include="ExcelDataReader.DataSet" Version="3.7.0" />
      <PackageReference Include="Serilog" Version="4.1.0" />
    </ItemGroup>

    
    <ItemGroup>
      <Reference Include="Newtonsoft.Json">
        <HintPath>..\..\..\..\.nuget\packages\newtonsoft.json\13.0.1\lib\netstandard2.0\Newtonsoft.Json.dll</HintPath>
      </Reference>
    </ItemGroup>

</Project>
