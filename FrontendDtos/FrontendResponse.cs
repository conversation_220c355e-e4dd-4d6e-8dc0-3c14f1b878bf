using System.Text.Json.Serialization;
using Levelbuild.Core.SharedDtos;

namespace Levelbuild.Core.FrontendDtos;

public class FrontendResponse : ServerResponse
{
	public IList<ValidationError> ValidationErrors { get; init; } = new List<ValidationError>();
	
	public PerformanceMetrics? Performance { get; init; }

	public string? ProgressId { get; init; }

	public FrontendResponse(string server, Version version) : base(server, version)
	{
		// Nothing...
	}
}

public class FrontendResponse<T> : FrontendResponse
{
	[JsonPropertyOrder(4)]
	public T Data { get; init; }
	
	public FrontendResponse(string server, Version version, T data) : base(server, version)
	{
		Data = data;
	}
}