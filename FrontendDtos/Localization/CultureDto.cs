using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.SharedDtos.Attributes;
#pragma warning disable CS8618, CS9264

namespace Levelbuild.Core.FrontendDtos.Localization;

[PublicApiInclude]
public class CultureDto : EntityDto
{
	[HeaderValue]
	public string Name { get; set; }
    
	[HeaderValue]
    public string DisplayName { get; set; }
	
	[HeaderValue]
	[PublicApiExclude]
	public string Slug { get; set; }
	
	[HeaderValue]
	[PublicApiExclude]
	public int TranslationCount { get; set; }
	
	[HeaderValue]
	[PublicApiExclude]
	public int UserCount { get; set; }
	
	[JsonConstructor]
	public CultureDto(){}
	
	public CultureDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects) {}
}