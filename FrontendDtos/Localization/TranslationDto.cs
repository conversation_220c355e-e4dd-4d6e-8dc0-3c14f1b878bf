using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.Customer;
using Levelbuild.Core.FrontendDtos.Shared;
#pragma warning disable CS8618, CS9264

namespace Levelbuild.Core.FrontendDtos.Localization;

public class TranslationDto : EntityDto
{
	[HeaderValue]
	public string Key { get; set; }
    
	[HeaderValue]
    public string Value { get; set; }
	
	[HeaderValue]
	public bool SystemTranslation { get; set; }
	
	[HeaderValue]
	public string? Responsible { get; set; }
	
	[HeaderValue]
	public DateTime? Created { get; set; }
	
	public string? CreatedBy { get; set; }
	
	[HeaderValue]
	public DateTime? LastModified { get; set; }
	
	public string? LastModifiedBy { get; set; }
	
	[HeaderValue]
	public Guid? CultureId { get; set; }
	
	[HeaderValue]
	public string? CultureName { get; set; }
	
	public CultureDto? Culture { get; set; }
	
	[HeaderValue]
	public Guid? CustomerId { get; set; }
	
	[HeaderValue]
	public string? CustomerName { get; set; }
	
	public CustomerDto? Customer { get; set; }
	
	[JsonConstructor]
	public TranslationDto(){}
	
	public TranslationDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects) {}
}