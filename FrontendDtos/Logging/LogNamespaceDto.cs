using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.Shared;
#pragma warning disable CS8618, CS9264

namespace Levelbuild.Core.FrontendDtos.Logging;

public class LogNamespaceDto: EntityDto
{
	[HeaderValue]
	public string K8SNamespace { get; set; }
	
	[HeaderValue]
	public string DisplayValue { get; set; }
	
	[HeaderValue]
	public string Slug { get; set; }
	
	[HeaderValue]
	public IList<string> Labels { get; set; }
	
	[JsonConstructor]
	public LogNamespaceDto() {}
}