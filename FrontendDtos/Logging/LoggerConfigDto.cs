using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.Shared;
using Serilog.Events;

namespace Levelbuild.Core.FrontendDtos.Logging;
#pragma warning disable CS8618, CS9264

public class LoggerConfigDto : EntityDto
{
	[HeaderValue]
	public string? LoggerSource { get; set; }
	
	[HeaderValue]
	[JsonConverter(typeof(JsonStringEnumConverter))]
	public LogEventLevel? Level { get; set; }
	
	[HeaderValue]
	public string LevelName { get; set; }
	
	[HeaderValue]
	public bool? LogToFile { get; set; }
	
	public string? LogFilePath { get; set; }
	
	[HeaderValue]
	public bool? IsActive { get; set; }
	
	[HeaderValue]
	public string? Slug { get; set; }
	
	[JsonConstructor]
	public LoggerConfigDto() {}
	
	public LoggerConfigDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects) {}
}