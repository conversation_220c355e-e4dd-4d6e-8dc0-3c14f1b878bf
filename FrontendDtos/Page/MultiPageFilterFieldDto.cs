using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.DataField;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.SharedDtos.Enums;

namespace Levelbuild.Core.FrontendDtos.Page;

public class MultiPageFilterFieldDto : EntityDto
{
	/// <summary>
	/// Parent entity
	/// </summary>
	public Guid PageId { get; set; }
	
	/// <summary>
	/// Is used for the position with the filter panel and dialog
	/// </summary>
	[HeaderValue]
	public int? Position { get; set; }
	
	/// <summary>
	/// Reference to field to filter by 
	/// </summary>
	public DataFieldDto? Field { get; set; }
	
	/// <summary>
	/// Reference ID to field to filter by 
	/// </summary>
	public Guid? FieldId { get; set; }
	
	/// <summary>
	/// Name of the data field
	/// </summary>
	[HeaderValue]
	public string? FieldName { get; set; }
	
	/// <summary>
	/// Datatype of the data field
	/// </summary>
	[HeaderValue]
	public DataType? FieldType { get; set; }
	
	/// <summary>
	/// Should the filter field be displayed in the filter panel?
	/// </summary>
	[HeaderValue]
	public bool? DisplayInPanel { get; set; }
	
	/// <summary>
	/// Are more than one values possible?
	/// </summary>
	public bool? MultiValue { get; set; }
	
	/// <summary>
	/// Should existing values be grouped and display for selection 
	/// </summary>
	public bool? ValuePreview { get; set; }
	
	/// <inheritdoc />
	[JsonConstructor]
	public MultiPageFilterFieldDto() {}
	
	/// <inheritdoc />
	public MultiPageFilterFieldDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects) {}
}