using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.SharedDtos.Attributes;

namespace Levelbuild.Core.FrontendDtos.Page;

[PublicApiInclude]
public class PageHeaderElementDto : EntityDto
{
	public Guid PageId { get; set; }
	
	[HeaderValue]
	public string? Icon { get; set; }
	
	[HeaderValue]
	public string? Label { get; set; }
	
	public String? LabelTranslated { get; set; }
	
	[HeaderValue]
	public string? Value { get; set; }
	
	[HeaderValue]
	public bool? Enabled { get; set; }
	
	public int? Position { get; set; }
	
	[JsonConstructor]
	public PageHeaderElementDto() {}
	
	public PageHeaderElementDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects) {}
}