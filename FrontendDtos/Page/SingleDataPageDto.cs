using System.Text.Json;
using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.SharedDtos.Attributes;

namespace Levelbuild.Core.FrontendDtos.Page;

[PublicApiInclude]
public class SingleDataPageDto : PageDto
{
	public string? BreadcrumbLabel { get; set; }
	
	public String? BreadcrumbLabelTranslated { get; set; }
	
	public ICollection<PageHeaderElementDto>? HeaderElements { get; set; }

	[JsonConstructor]
	public SingleDataPageDto() {}

	public SingleDataPageDto(PageDto pageDto)
	{
		CopyValues(pageDto);
	}

	public SingleDataPageDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects)
	{
		TypeIcon = "rectangle";
	}

	public override string ToJson()
	{
		return JsonSerializer.Serialize(this, new JsonSerializerOptions()
		{
			PropertyNamingPolicy = JsonNamingPolicy.KebabCaseLower
		});
	}
}