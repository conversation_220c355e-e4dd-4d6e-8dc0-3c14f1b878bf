using System.Text.Json;
using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos.DataField;
using Levelbuild.Core.SharedDtos.Attributes;

namespace Levelbuild.Core.FrontendDtos.PageView;

[PublicApiInclude]
public class GalleryViewDto : PageViewDto
{
	public Guid? TitleFieldId { get; set; }
	
	public DataFieldDto? TitleField { get; set; }
	
	public Guid? SubtitleFieldId { get; set; }
	
	public DataFieldDto? SubtitleField { get; set; }

	[JsonConstructor]
	public GalleryViewDto() {}

	public GalleryViewDto(PageViewDto pageViewDto)
	{
		CopyValues(pageViewDto);
	}

	public GalleryViewDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects)
	{
		
	}
	
	public override string ToJson()
	{
		return JsonSerializer.Serialize(this, new JsonSerializerOptions()
		{
			PropertyNamingPolicy = JsonNamingPolicy.KebabCaseLower
		});
	}
}