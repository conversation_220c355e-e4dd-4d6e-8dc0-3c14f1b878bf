using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos.DataField;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.SharedDtos.Attributes;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Core.FrontendDtos.PageView;

[PublicApiInclude]
public class GridViewFieldDto : GridViewSectionElementDto
{
	public String? DataFieldId { get; set; }
	
	[PublicApiExclude]
	public DataFieldDto? DataField { get; set; }
	
	public String? Label { get; set; }
	
	public String? LabelTranslated { get; set; }
	
	public InputDataType? DataType { get; set; }
	
	public InputDataType? InputDataType { get; set; }
	
	public String? DataTypeTranslated { get; set; }
	
	public Boolean? Required { get; set; }
	
	public String? DefaultValue { get; set; }
	
	public String? Placeholder { get; set; }
	
	public String? PlaceholderTranslated { get; set; }
	
	public String? HelpText { get; set; }
	
	public String? HelpTextTranslated { get; set; }
	
	public String? FontColor { get; set; }
	
	public Alignment? TextAlign { get; set; }
	
	public bool? IsRichText { get; set; }
	
	public Boolean? Readonly { get; set; }
	
	public Boolean? HideIfEmpty { get; set; }
	
	[JsonConstructor]
	public GridViewFieldDto() {}
	
	public GridViewFieldDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects) {}
}