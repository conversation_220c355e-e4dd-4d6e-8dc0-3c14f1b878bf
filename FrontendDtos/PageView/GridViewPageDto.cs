using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos.DataField;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Page;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.SharedDtos.Attributes;

namespace Levelbuild.Core.FrontendDtos.PageView;

public class GridViewPageDto : GridViewSectionElementDto, ISortableDto
{
	public int? Position { get; set; }
	
	#region embedded into GridView
	
	public Guid? GridViewId { get; set; }

	public int GridViewColumn { get; set; } = -1;
	
	#endregion embedded into GridView
	
	public GridViewPageType GridViewPageType { get; set; }
	
	public Guid? EmbeddedPageId { get; set; }
	
	[PublicApiExclude]
	public PageDto? EmbeddedPage { get; set; }
	
	public GridViewPageReferenceType? ReferenceType { get; set; }
	
	public Guid? ReferenceFieldId { get; set; }
    	
	public DataFieldDto? ReferenceField { get; set; }
	
	public Guid? KeyFieldId { get; set; }
    	
	[PublicApiExclude]
	public DataFieldDto? KeyField { get; set; }
	
	public Guid? EmbeddedViewId { get; set; }
	
	[PublicApiExclude]
	public PageViewDto? EmbeddedView { get; set; }
	
	public Guid? EmbeddedSectionId { get; set; }
	
	[PublicApiExclude]
	public GridViewSectionDto? EmbeddedSection { get; set; }
	
	public string? Title { get; set; }
	
	public string? TitleTranslated { get; set; }
	
	public new string? Flag { get; set; }
	
	public int? MaxHeight { get; set; }
	
	public bool? ShowTitle { get; set; }
	
	public bool? AllowMinimize { get; set; }
	
	public bool? StartMinimized { get; set; }
	
	public bool? AllowCreate { get; set; }
	
	public bool? AllowMaximize { get; set; }
	
	#region Tile
	
	public bool? AllowOpenNewTab { get; set; }
	
	public bool? WithThousandSeparators { get; set; }
	
	public int? Divider { get; set; }
	
	public string? Icon { get; set; }
	
	#endregion
	
	public ICollection<GridViewPageFilterDto> Filters { get; set; } = new List<GridViewPageFilterDto>();
	
	[JsonConstructor]
	public GridViewPageDto() {}
	
	public GridViewPageDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects) {}
}