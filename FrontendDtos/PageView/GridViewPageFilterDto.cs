using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.DataSource;
using Levelbuild.Core.SharedDtos.Attributes;

namespace Levelbuild.Core.FrontendDtos.PageView;

[PublicApiInclude]
public class GridViewPageFilterDto : DataSourceFilterDto
{
	[HeaderValue]
	public Guid? PageId { get; set; }
	
	public GridViewPageDto? Page { get; set; }
	
	[JsonConstructor]
	public GridViewPageFilterDto() {}
	
	public GridViewPageFilterDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects) {}
}