using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.SharedDtos.Attributes;

namespace Levelbuild.Core.FrontendDtos.PageView;

[PublicApiInclude]
public class GridViewSectionDto : EntityDto, ISortableDto
{
	public Guid? GridViewId { get; set; }
	
	public int? Position { get; set; }
	
	public int? GridViewColumn { get; set; }
	
	public string? Title { get; set; }
	
	public string? TitleTranslated { get; set; }
	
	public bool? ShowTitle { get; set; }
	
	public bool? AllowMinimize { get; set; }
	
	public bool? StartMinimized { get; set; }
	
	public ICollection<GridViewFieldDto> Fields { get; set; } = new List<GridViewFieldDto>();
	
	public ICollection<GridViewTextDto> Texts { get; set; } = new List<GridViewTextDto>();
	
	public ICollection<GridViewPageDto> Pages { get; set; } = new List<GridViewPageDto>();
	
	public ICollection<GridViewPageDto> Tiles { get; set; } = new List<GridViewPageDto>();
	
	public int? RowCount { get; set; }
	
	[JsonConstructor]
	public GridViewSectionDto() {}
	
	public GridViewSectionDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects) {}
	
	/// <summary>
	/// Process grid elements to make them easier to render in cshtml.
	/// </summary>
	/// <param name="usedRows">The max row end.</param>
	/// <returns></returns>
	public Dictionary<int, Dictionary<int, GridViewSectionElementDto>> GetGridSortedElements(out int usedRows)
	{
		// collect elements
		var elements = new Dictionary<int, Dictionary<int, GridViewSectionElementDto>>();
		usedRows = 0;
		foreach (var field in Fields)
		{
			if (!elements.ContainsKey(field.RowStart))
				elements[field.RowStart] = new Dictionary<int, GridViewSectionElementDto>();
			
			elements[field.RowStart][field.ColStart] = field;
			usedRows = Math.Max(field.RowEnd, usedRows);
		}
		
		foreach (var text in Texts)
		{
			if (!elements.ContainsKey(text.RowStart))
				elements[text.RowStart] = new Dictionary<int, GridViewSectionElementDto>();
			
			elements[text.RowStart][text.ColStart] = text;
			usedRows = Math.Max(text.RowEnd, usedRows);
		}
		
		foreach (var page in Pages)
		{
			if (!elements.ContainsKey(page.RowStart))
				elements[page.RowStart] = new Dictionary<int, GridViewSectionElementDto>();
			
			elements[page.RowStart][page.ColStart] = page;
			usedRows = Math.Max(page.RowEnd, usedRows);
		}
		
		return elements;
	}
}