using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.SharedDtos.Attributes;

namespace Levelbuild.Core.FrontendDtos.PageView;
#pragma warning disable CS8618, CS9264

[PublicApiInclude]
public class GridViewSectionElementDto : EntityDto
{
	[PublicApiExclude]
	public Guid? SectionId { get; set; }
	
	public GridViewSectionElementType? Type { get; set; }
	
	public int RowStart { get; set; } = -1;
	
	public int RowEnd { get; set; } = -1;
	
	public int ColStart { get; set; } = -1;
	
	public int ColEnd { get; set; } = -1;
	
	public string Flag { get; set; }

	
	[JsonConstructor]
	public GridViewSectionElementDto() {}
	
	public GridViewSectionElementDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects) {}
}