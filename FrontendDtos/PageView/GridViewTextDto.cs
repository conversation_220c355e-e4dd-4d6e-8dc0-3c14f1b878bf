using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.SharedDtos.Attributes;

namespace Levelbuild.Core.FrontendDtos.PageView;

[PublicApiInclude]
public class GridViewTextDto : GridViewSectionElementDto
{
	public string? Label { get; set; }
	
	public String? LabelTranslated { get; set; }
	
	public string? Text { get; set; }
	
	public string? TextTranslated { get; set; }
	
	public string? Color { get; set; }
	
	public Alignment? TextAlign { get; set; }
	
	public GridViewTextType? TextType { get; set; }
	
	public string? TextTypeTranslated { get; set; }
	
	[JsonConstructor]
	public GridViewTextDto() {}
	
	public GridViewTextDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects) {}
}