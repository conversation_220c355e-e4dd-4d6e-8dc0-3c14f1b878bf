using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.DataField;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.SharedDtos.Attributes;

namespace Levelbuild.Core.FrontendDtos.PageView;
#pragma warning disable CS8618, CS9264

public class ListViewColumnDto : EntityDto
{
	[HeaderValue]
	public string? Slug { get; set; }
	
	public Guid ListViewId { get; set; }
	
	[PublicApiExclude]
	public DataFieldDto? Field { get; set; }
	
	public Guid FieldId { get; set; }
	
	[HeaderValue]
	[PublicApiExclude]
	public string FieldName { get; set; }
	
	[HeaderValue]
	[PublicApiExclude]
	public string FieldTypeName { get; set; }
	
	[HeaderValue]
	public int? Position { get; set; }
	
	public string? Label { get; set; }
	
	public String? LabelTranslated { get; set; }
	
	[HeaderValue]
	public bool? Display { get; set; }
	
	[HeaderValue]
	public bool? AllowEdit { get; set; }
	
	[JsonConstructor]
	public ListViewColumnDto() {}
	
	public ListViewColumnDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects) {}
}