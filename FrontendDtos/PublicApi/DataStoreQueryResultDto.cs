using System.Collections;
using Levelbuild.Core.SharedDtos.Attributes;

namespace Levelbuild.Core.FrontendDtos.PublicApi;

/// <summary>
/// DTO for data store element query responses.
/// </summary>
[PublicApiInclude]
public class DataStoreQueryResultDto
{
	/// <summary>
	/// Optional total element count.
	/// </summary>
	public int? CountTotal { get; init; }
	
	/// <summary>
	/// The actual elements.
	/// </summary>
	public IList Elements { get; init; }
	
	/// <summary>
	/// Constructor
	/// </summary>
	/// <param name="elements"></param>
	public DataStoreQueryResultDto(IList elements)
	{
		Elements = elements;
		CountTotal = null;
	}
	
	/// <summary>
	/// Constructor
	/// </summary>
	/// <param name="countTotal"></param>
	/// <param name="elements"></param>
	public DataStoreQueryResultDto(IList elements, int countTotal)
	{
		Elements = elements;
		CountTotal = countTotal;
	}
}