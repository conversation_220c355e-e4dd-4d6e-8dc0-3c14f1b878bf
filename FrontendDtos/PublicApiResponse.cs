using System.Text.Json.Serialization;
using Levelbuild.Core.SharedDtos.Attributes;

namespace Levelbuild.Core.FrontendDtos;

[PublicApiInclude]
public class PublicApiResponse : ServerResponse
{
	public PublicApiResponse(string server, Version version) : base(server, version)
	{
		// Nothing...
	}
}

[PublicApiInclude]
public class PublicApiResponse<T> : PublicApiResponse
{
	[JsonPropertyOrder(4)]
	public T Data { get; init; }
	
	public PublicApiResponse(string server, Version version, T data) : base(server, version)
	{
		Data = data;
	}
}