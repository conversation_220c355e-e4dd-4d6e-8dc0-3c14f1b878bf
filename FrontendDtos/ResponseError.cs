using Levelbuild.Core.SharedDtos.Attributes;

namespace Levelbuild.Core.FrontendDtos;

[PublicApiInclude]
public struct ResponseError
{
	public int ErrorCode { get; init; }
	public string ErrorMessage { get; init; }
	public string? TraceId { get; init; }

	public ResponseError(int errorCode, string errorMessage, string? traceId = null)
	{
		ErrorCode = errorCode;
		ErrorMessage = errorMessage;
		TraceId = traceId;
	}
	
	public ResponseError(int errorCode, Exception exception, string? traceId = null)
	{
		ErrorCode = errorCode;
		ErrorMessage = exception.Message;
		TraceId = traceId;
	}
}