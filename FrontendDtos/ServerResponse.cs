using System.Text.Json.Serialization;

namespace Levelbuild.Core.FrontendDtos;

public abstract class ServerResponse
{
	[JsonPropertyOrder(0)]
	public string Server { get; init; }
	
	[JsonPropertyOrder(1)]
	public Version Version { get; init; }
	
	[JsonPropertyOrder(2)]
	public ResponseError? Error { get; init; }

	public ServerResponse(string server, Version version)
	{
		Server = server;
		Version = version;
	}
}