using System.Text.Json;

namespace Levelbuild.Core.FrontendDtos;

/// <summary>
/// Encapsules response payloads and serves the purpose of a type whitelist for those.
/// </summary>
/// <typeparam name="T">Type of the object within the response.</typeparam>
public class ServerResponsePayload<T>
{
	/// <summary>
	/// The payload of the response.
	/// </summary>
	public T Payload { get; set; }
	
	internal ServerResponsePayload(T payload)
	{
		Payload = payload;
	}
}

/// <summary>
/// Helper class for the construction of <see cref="ServerResponsePayload{T}"/> instances.
/// </summary>
public static class ServerResponsePayload
{
	/// <summary>
	/// Constructs a string payload.
	/// </summary>
	/// <param name="payload"></param>
	/// <returns></returns>
	public static ServerResponsePayload<string> FromString(string payload)
	{
		return new ServerResponsePayload<string>(payload);
	}
	
	/// <summary>
	/// Constructs a dictionary payload.
	/// </summary>
	/// <param name="payload"></param>
	/// <returns></returns>
	public static ServerResponsePayload<Dictionary<TKey, TValue>> FromDictionary<TKey,TValue>(Dictionary<TKey, TValue> payload) where TKey : notnull
	{
		return new ServerResponsePayload<Dictionary<TKey, TValue>>(payload);
	}
	
	/// <summary>
	/// Constructs a json payload.
	/// </summary>
	/// <param name="payload"></param>
	/// <returns></returns>
	public static ServerResponsePayload<JsonElement> FromJson(JsonElement payload)
	{
		return new ServerResponsePayload<JsonElement>(payload);
	}
}