using Levelbuild.Core.FrontendDtos.Annotations;
using Levelbuild.Core.FrontendDtos.CachedDeepZoom;

namespace Levelbuild.Core.FrontendDtos.Shared;

public class DataElementDto(string id, IDictionary<string, object?> values) : IResponseObject
{
	/// <summary>
	/// Element Id
	/// </summary>
	public string Id { get; init; } = id;

	/// <summary>
	/// Available values referenced by name
	/// </summary>
	public IDictionary<string, object?> Values { get; set; } = values;

	/// <summary>
	/// Available File info (if any)
	/// </summary>
	public FileInfoDto? FileInfo { get; set; }

	public CachedDeepZoomDto? DeepZoomInfo { get; set; }
	
	public AnnotationSourceDto? AnnotationSourceInfo { get; set; }
	
	/// <summary>
	/// Are there any active workflows?
	/// </summary>
	public IList<WorkflowInfoDto>? WorkflowInfos { get; set; }	

	/// <summary>
	/// Is it a preferred record for the current user? 
	/// </summary>
	public bool Favorite { get; set; }

	/// <summary>
	/// Was the record discarded?
	/// </summary>
	public bool Inactive { get; set; }
}