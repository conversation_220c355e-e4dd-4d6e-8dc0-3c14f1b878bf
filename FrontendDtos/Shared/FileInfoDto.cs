namespace Levelbuild.Core.FrontendDtos.Shared;

/// <summary>
/// Dto conferring the info for files
/// </summary>
public class FileInfoDto
{
	/// <summary>
	/// ID of the source of the element that has the file attached
	/// </summary>
	public Guid DataSourceId { get; set; }
	
	/// <summary>
	/// ID of the file
	/// </summary>
	public required string FileId { get; set; }
	
	/// <summary>
	/// Name of the file
	/// </summary>
	public required string FileName { get; set; }
	
	/// <summary>
	/// ID of the element of the file
	/// </summary>
	public required string ElementId { get; set; }
	
	/// <summary>
	/// Size of the file
	/// </summary>
	public long? FileSize { get; set; }
	
	/// <summary>
	/// Type of the file
	/// </summary>
	public string? FileType { get; set; }
	
	/// <summary>
	/// Upload date of the file
	/// </summary>
	public DateTime? FileDate { get; set; }
	
	/// <summary>
	/// User who last edited the file
	/// </summary>
	public string? FileChangedUser { get; set; }
	
	/// <summary>
	/// User who uploaded the file
	/// </summary>
	public string? FileCreateUser { get; set; }
}