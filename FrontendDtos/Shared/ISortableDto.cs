using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface.Attributes;

namespace Levelbuild.Core.FrontendDtos.Shared;

public interface ISortableDto
{
	/// <summary>
	/// primary key of every entity
	/// </summary>
	[HeaderValue, JsonPropertyOrder(-1)]
	public Guid? Id { get; set; }
	
	/// <summary>
	/// Position of the entity
	/// </summary>
	[HeaderValue]
	public int? Position { get; set; }
}