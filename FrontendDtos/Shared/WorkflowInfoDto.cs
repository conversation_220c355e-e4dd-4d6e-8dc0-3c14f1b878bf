using Levelbuild.Core.FrontendDtos.Enums;

namespace Levelbuild.Core.FrontendDtos.Shared;

/// <summary>
/// Dto conferring the info for active workflows
/// </summary>
public class WorkflowInfoDto
{
	public WorkflowSlot Slot { get; set; }
	
	public Guid WorkflowId { get; set; }
	
	public string? WorkflowName { get; set; }
	
	public Guid? NodeId { get; set; }
	
	public string? NodeName { get; set; }
	
	public string? NodeIcon { get; set; }
	
	public WorkflowNodeState? State { get; set; }

	public IList<WorkflowNodeInfoDto> Nodes { get; set; } = [];
}