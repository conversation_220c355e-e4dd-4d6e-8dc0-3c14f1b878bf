using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.Customer;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.SharedDtos.Attributes;

namespace Levelbuild.Core.FrontendDtos.User;

[PublicApiInclude]
public class UserDto : EntityDto
{
	[HeaderValue]
	public string? Username { get; set; }

	[HeaderValue]
	public string? Email { get; set; }

	public string? Comment { get; set; }

	[HeaderValue]
	public string? FirstName { get; set; }

	[HeaderValue]
	public string? LastName { get; set; }
	
	[PublicApiExclude]
	public string? Password { get; set; }

	[HeaderValue]
	public string? MainCustomerId { get; set; }

	[HeaderValue]
	public CustomerDto? MainCustomer { get; set; }

	[HeaderValue]
	public string? MainCustomerName { get; set; }

	[HeaderValue]
	public bool? IsMainCustomerAdmin { get; set; }

	[HeaderValue]
	public string? Slug { get; set; }
	
	[HeaderValue]
	public bool? IsMachineUser { get; set; }
	
	[PublicApiExclude]
	public string? PersonalAccessToken { get; set; }

	public DateTime? PersonalAccessTokenExpirationDate { get; set; }

	[HeaderValue]
	public bool? Enabled { get; set; }

	public UserPreferencesDto? Preferences { get; set; }

	[HeaderValue]
	public string? UserCompositeName
	{
		get
		{
			if (string.IsNullOrEmpty(FirstName) || string.IsNullOrEmpty(LastName))
			{
				return FirstName ?? LastName;
			}

			return $"{LastName}, {FirstName}";
		}
	}

	[JsonConstructor]
	public UserDto()
	{
	}

	public UserDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects)
	{
	}
}