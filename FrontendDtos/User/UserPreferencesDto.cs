using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Localization;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.SharedDtos.Attributes;

namespace Levelbuild.Core.FrontendDtos.User;

[PublicApiInclude]
public class UserPreferencesDto : EntityDto
{
	public CultureOptionType? CultureOptionType { get; set; }
	
	public string? CurrentCultureId { get; set; }
	
	public CultureDto? CurrentCulture { get; set; }
	
	[JsonConstructor]
	public UserPreferencesDto() {}
	
	public UserPreferencesDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects) {}
}