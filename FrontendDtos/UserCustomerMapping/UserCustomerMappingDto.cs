using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.Customer;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.FrontendDtos.User;

namespace Levelbuild.Core.FrontendDtos.UserCustomerMapping;

public class UserCustomerMappingDto : EntityDto
{
	[HeaderValue]
	public string? Slug { get; set; }
	
	public string? UserId { get; set; }
	
	public UserDto? User { get; set; }
	
	public string? CustomerId { get; set; }
	
	public CustomerDto? Customer { get; set; }
	
	[HeaderValue]
	public string? CustomerName { get; set; }
	
	[HeaderValue]
	public bool? IsAdmin { get; set; }
	
	[JsonConstructor]
	public UserCustomerMappingDto() {}
	
	public UserCustomerMappingDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects) {}
}