using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Shared;


namespace Levelbuild.Core.FrontendDtos.Userlane;

    public class UserlaneDto : EntityDto
    {
		[HeaderValue]
		public string Name { get; set; } = null!;

		[HeaderValue]
		public string PageId { get; set; } = null!;

		[HeaderValue]
		public string? StartPoint { get; set; }
		
		[HeaderValue]
		public int Speed { get; set; }
		
		[HeaderValue]
		public UserlaneType Type { get; set; }
		
		[HeaderValue]
		public string? TesterRole { get; set; }
		
		[HeaderValue]
		public string? ClientContext { get; set; }
		
		[HeaderValue]
		public Guid ModuleId { get; set; }
			
		[JsonConstructor]
        public UserlaneDto(){}
		
		public UserlaneDto(IPersistentEntity entity, params string[] excludedProperties) : base(entity, excludedProperties) {}
    }
