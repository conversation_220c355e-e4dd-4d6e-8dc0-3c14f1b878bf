using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.FrontendDtos.Shared;

#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Core.FrontendDtos.Userlane;

/// <summary>
/// DTO for Userlane Feed items
/// </summary>
[ExcludeFromCodeCoverage]
public class UserlaneFeedDto : EntityDto
{
    /// <summary>
    /// The ID of the Userlane this feed item belongs to
    /// </summary>
    public Guid UserlaneId { get; set; }

    /// <summary>
    /// The ID of the user who performed the activity
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// The type of activity (created, updated, deleted, run)
    /// </summary>
    public string ActivityType { get; set; } = string.Empty;

    /// <summary>
    /// The status of the activity (successful, failed, deleted, etc.)
    /// </summary>
    public string? Status { get; set; }

    /// <summary>
    /// Description of the activity
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Additional metadata as JSON string
    /// </summary>
    public string? Metadata { get; set; }

    /// <summary>
    /// Timestamp when the activity occurred
    /// </summary>
    public DateTime ActivityTimestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Display name of the user who performed the activity (populated from navigation property)
    /// </summary>
    public string? UserName { get; set; }

    /// <summary>
    /// Email of the user who performed the activity (populated from navigation property)
    /// </summary>
    public string? UserEmail { get; set; }

    /// <summary>
    /// Name of the Userlane (populated from navigation property)
    /// </summary>
    public string? UserlaneName { get; set; }

    /// <summary>
    /// Constructor for creating DTO from entity
    /// </summary>
    /// <param name="entity">The entity to create DTO from</param>
    /// <param name="excludedProperties">Properties to exclude</param>
    public UserlaneFeedDto(object entity, string[]? excludedProperties = null) : base(entity, excludedProperties)
    {
    }

    /// <summary>
    /// Default constructor
    /// </summary>
    public UserlaneFeedDto()
    {
    }

    /// <summary>
    /// Get formatted timestamp for display
    /// </summary>
    public string FormattedTimestamp => ActivityTimestamp.ToString("dd.MM.yyyy • HH:mm");

    /// <summary>
    /// Get relative time description (Today, Yesterday, etc.)
    /// </summary>
    public string RelativeTime
    {
        get
        {
            var now = DateTime.Now;
            var activityDate = ActivityTimestamp.Date;

            if (activityDate == now.Date)
                return "Today";
            else if (activityDate == now.AddDays(-1).Date)
                return "Yesterday";
            else if (activityDate >= now.AddDays(-7).Date)
                return $"{(now.Date - activityDate).Days} days ago";
            else
                return activityDate.ToString("dd.MM.yyyy");
        }
    }

    /// <summary>
    /// Get user initials for avatar display
    /// </summary>
    public string UserInitials
    {
        get
        {
            if (string.IsNullOrWhiteSpace(UserName))
                return "??";

            var parts = UserName.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length >= 2)
            {
                return $"{parts[0][0]}{parts[1][0]}".ToUpper();
            }
            else if (parts.Length == 1 && parts[0].Length >= 2)
            {
                return parts[0].Substring(0, 2).ToUpper();
            }
            else if (parts.Length == 1)
            {
                return parts[0][0].ToString().ToUpper();
            }

            return "??";
        }
    }

    /// <summary>
    /// Get CSS class for activity status
    /// </summary>
    public string StatusCssClass
    {
        get
        {
            return Status?.ToLower() switch
            {
                "successful" => "successful",
                "failed" => "failed",
                "deleted" => "deleted",
                "pending" => "pending",
                "running" => "running",
                _ => string.Empty
            };
        }
    }

    /// <summary>
    /// Get avatar color class based on activity type
    /// </summary>
    public string AvatarColorClass
    {
        get
        {
            return ActivityType.ToLower() switch
            {
                "created" => "blue",
                "updated" => "blue",
                "deleted" => "red",
                "run" => "green",
                _ => UserId.ToString().GetHashCode() % 3 switch
                {
                    0 => "blue",
                    1 => "green",
                    _ => "red"
                }
            };
        }
    }

    /// <summary>
    /// Check if this feed item has an associated report
    /// </summary>
    public bool HasReport => ActivityType.ToLower() == "run" && !string.IsNullOrEmpty(Metadata);

    /// <summary>
    /// Get formatted description with Userlane name substitution
    /// </summary>
    public string FormattedDescription
    {
        get
        {
            var description = Description;
            if (!string.IsNullOrEmpty(UserlaneName))
            {
                description = description.Replace("@Model.Userlane?.Name", $"\"{UserlaneName}\"");
            }
            return description;
        }
    }
}
