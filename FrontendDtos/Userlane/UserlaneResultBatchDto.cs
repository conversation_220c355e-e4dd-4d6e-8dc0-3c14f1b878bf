using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Shared;


namespace Levelbuild.Core.FrontendDtos.Userlane;

    public class UserlaneResultBatchDto : EntityDto
    {
		[HeaderValue]
		public Guid UserlaneId { get; set; }
		
		[HeaderValue]
		public Guid? UserId { get; set; }
		
		[HeaderValue]
		public string? UserName { get; set; }
		
		[HeaderValue]
		public int Runtime { get; set; }
		
		[HeaderValue]
		public DateTime StartTime { get; set; }
		
		[HeaderValue]
		public DateTime EndTime { get; set; }
		
		[HeaderValue]
		public string? Status { get; set; }
		
		[HeaderValue]
		public DateTime CreatedDateTime { get; set; }
			
		[JsonConstructor]
        public UserlaneResultBatchDto(){}
		
		public UserlaneResultBatchDto(IPersistentEntity entity, params string[] excludedProperties) : base(entity, excludedProperties) {}
    }
