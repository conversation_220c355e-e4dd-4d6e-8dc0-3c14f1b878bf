using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.Shared;


namespace Levelbuild.Core.FrontendDtos.Userlane;

    public class UserlaneResultDto : EntityDto
    {
		[HeaderValue]
		public Guid UserlaneId { get; set; }
		
		[HeaderValue]
		public Guid UserlaneStepId { get; set; }
		
		[HeaderValue]
		public Guid UserlaneStepActionId { get; set; }
		
		[HeaderValue]
		public Guid BatchId { get; set; }
		
		[HeaderValue]
		public string Title { get; set; } = null!;

		[HeaderValue]
		public DateTime StartTime { get; set; }
		
		[HeaderValue]
		public DateTime EndTime { get; set; }
		
		[HeaderValue]
		public int Duration { get; set; }
		
		[HeaderValue]
		public bool Complete { get; set; }
		
		[HeaderValue]
		public bool Found { get; set; }
		
		[HeaderValue]
		public string Result { get; set; } = null!;

		[JsonConstructor]
        public UserlaneResultDto(){}
		
		public UserlaneResultDto(IPersistentEntity entity, params string[] excludedProperties) : base(entity, excludedProperties) {}
    }
