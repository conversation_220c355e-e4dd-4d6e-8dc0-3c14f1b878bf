using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Shared;


namespace Levelbuild.Core.FrontendDtos.Userlane;

public class UserlaneStepActionDto : EntityDto
{
	[HeaderValue]
	public Guid UserlaneStepId { get; set; }
		
	[HeaderValue]
	public int Order { get; set; }
	
	[HeaderValue]
	public UserlaneStepActionType ActionType { get; set; }
	
	[HeaderValue]
	public string Trigger { get; set; } = null!;

	[HeaderValue]
	public string Target { get; set; } = null!;

	[HeaderValue]
	public string TargetValue { get; set; } = null!;

	[HeaderValue]
	public int Delay { get; set; }

	
	[JsonConstructor]
	public UserlaneStepActionDto(){}
		
	public UserlaneStepActionDto(IPersistentEntity entity, params string[] excludedProperties) : base(entity, excludedProperties) {}
}