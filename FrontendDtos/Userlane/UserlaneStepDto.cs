using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.Shared;


namespace Levelbuild.Core.FrontendDtos.Userlane;

public class UserlaneStepDto : EntityDto
{
	[HeaderValue]
	public Guid UserlaneId { get; set; }
	
	[HeaderValue]
	public int Order { get; set; }
	
	[HeaderValue]
	public string TargetElement { get; set; } = null!;

	[HeaderValue]
	public string Title { get; set; } = null!;

	[HeaderValue]
	public string Description { get; set; } = null!;

	[HeaderValue]
	public int Delay { get; set; }

	
	[JsonConstructor]
	public UserlaneStepDto(){}
		
	public UserlaneStepDto(IPersistentEntity entity, params string[] excludedProperties) : base(entity, excludedProperties) {}
}