using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.SharedDtos.Enums;


namespace Levelbuild.Core.FrontendDtos.Userlane;

public class UserlaneStepTestConditionDto : EntityDto
{
	[HeaderValue]
	public Guid UserlaneStepId { get; set; }
		
	[HeaderValue]
	public int Order { get; set; }
	
	[HeaderValue]
	public UserlaneStepTestConditionType ConditionType { get; set; }
	
	[HeaderValue]
	public string Field { get; set; } = null!;

	[HeaderValue]
	public CompareOperator? Operator { get; set; }

	[HeaderValue]
	public string Value { get; set; } = null!;

	[HeaderValue]
	public string LogicalOperator { get; set; } = null!;

	
	[JsonConstructor]
	public UserlaneStepTestConditionDto(){}
		
	public UserlaneStepTestConditionDto(IPersistentEntity entity, params string[] excludedProperties) : base(entity, excludedProperties) {}
}