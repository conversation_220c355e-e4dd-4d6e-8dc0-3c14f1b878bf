using System.Text.Json.Serialization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.Customer;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.SharedDtos.Attributes;

namespace Levelbuild.Core.FrontendDtos.Workflow;

[PublicApiInclude]
public class WorkflowNodeDto : EntityDto
{
	[HeaderValue]
	public string? Slug { get; set; }
	
	[HeaderValue]
	public string? Name { get; set; }
	
	public string? NameTranslated { get; set; }
	
	public Guid? WorkflowId { get; set; }
	
	[PublicApiExclude]
	public WorkflowDto? Workflow { get; set; }
	
	public Guid? CustomerId { get; set; }
	
	[PublicApiExclude]
	public CustomerDto? Customer { get; set; }
	
	public string? Icon { get; set; }
	
	public short? Sorting { get; set; }
	
	[HeaderValue]
	public WorkflowNodeState? State { get; set; }
	
	[JsonConstructor]
	public WorkflowNodeDto() {}
	
	public WorkflowNodeDto(IPersistentEntity entity, string[]? excludedProperties = null, List<string>? handledObjects = null) : base(entity, excludedProperties, handledObjects) {}
}