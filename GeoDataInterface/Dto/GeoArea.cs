namespace Levelbuild.Core.GeoDataInterface.Dto;

/// <summary>
/// describes the (rectangular) area returned by a geolocation service for a given address
/// </summary>
public class GeoArea
{
	/// <summary>
	/// Latitude starting point
	/// </summary>
	public double LatitudeFrom { get; init; }
	
	/// <summary>
	/// Latitude ending point
	/// </summary>
	public double LatitudeTo { get; init; }
	
	/// <summary>
	/// Longitude starting point
	/// </summary>
	public double LongitudeFrom { get; init; }
	
	/// <summary>
	/// Longitude ending point
	/// </summary>
	public double LongitudeTo { get; init; }

	/// <summary>
	/// creates a new GeoArea instance with all mandatory properties set
	/// </summary>
	/// <param name="latFrom">Latitude starting point</param>
	/// <param name="latTo">Latitude ending point</param>
	/// <param name="longFrom">Longitude starting point</param>
	/// <param name="longTo">Longitude ending point</param>
	public GeoArea(double latFrom, double latTo, double longFrom, double longTo)
	{
		LatitudeFrom = latFrom;
		LatitudeTo = latTo;
		LongitudeFrom = longFrom;
		LongitudeTo = longTo;
	}
}