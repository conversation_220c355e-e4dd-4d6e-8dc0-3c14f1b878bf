namespace Levelbuild.Core.GeoDataInterface.Dto;

/// <summary>
/// DTO containing basic geo data information
/// </summary>
public class GeoLocation
{
	/// <summary>
	/// latitude
	/// </summary>
	public double Latitude { get; init; }
	
	/// <summary>
	/// longitude
	/// </summary>
	public double Longitude { get; init; }
	
	/// <summary>
	/// location name
	/// </summary>
	public string DisplayName { get; init; }

	/// <summary>
	/// optional area of the location
	/// </summary>
	public GeoArea? Area { get; init; }

	/// <summary>
	/// creates a new GeoLocation instance with all non-nullable properties set
	/// </summary>
	/// <param name="latitude">Latitude</param>
	/// <param name="longitude">Longitude</param>
	/// <param name="displayName">Location name</param>
	public GeoLocation(double latitude, double longitude, string displayName)
	{
		Latitude = latitude;
		Longitude = longitude;
		DisplayName = displayName;
	}
}