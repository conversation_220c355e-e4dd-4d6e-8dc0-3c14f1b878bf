namespace Levelbuild.Core.GeoDataInterface.Exceptions;

/// <summary>
/// used to indicate, that a service request could not be completed because the quota of this service was exceeded
/// </summary>
public class QuotaExceededException : ApplicationException
{
	
	/// <summary>
	/// creates a new QuotaExceededException
	/// </summary>
	public QuotaExceededException()
	{
	}

	/// <summary>
	/// creates a new QuotaExceededException with its message string set to message
	/// </summary>
	public QuotaExceededException(string message) : base(message)
	{
	}

	/// <summary>
	/// creates a new QuotaExceededException with its message string set to message and a given inner exception
	/// </summary>
	public QuotaExceededException(string message, Exception inner) : base(message, inner)
	{
	}
}