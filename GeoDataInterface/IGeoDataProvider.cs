using Levelbuild.Core.GeoDataInterface.Dto;
using Levelbuild.Core.GeoDataInterface.Exceptions;

namespace Levelbuild.Core.GeoDataInterface;

/// <summary>
/// Interface designed to get a geo data (lat/long) for a given address string
/// </summary>
public interface IGeoDataProvider
{
	/// <summary>
	/// Gets an instance of the GeoDataProvider
	/// </summary>
	/// <param name="config">optional config needed for this specific provider</param>
	/// <returns><see cref="IGeoDataProvider"/> instance</returns>
	public static abstract IGeoDataProvider GetInstance(GeoDataConfig? config = null);

	/// <summary>
	/// get the location for a given address sting
	/// </summary>
	/// <param name="address">address string</param>
	/// <returns><see cref="GeoLocation"/> containing at least latitude and longitude</returns>
	/// <exception cref="UnauthorizedAccessException">thrown if accessing the service was forbidden (for example because of a wrong API key).</exception>
	/// <exception cref="QuotaExceededException">thrown if accessing the service is currently forbidden because of an exceeded quota.</exception>
	public Task<GeoLocation?> GetLocation(string address);
}