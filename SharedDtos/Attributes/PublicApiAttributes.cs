namespace Levelbuild.Core.SharedDtos.Attributes;

/// <summary>
/// Marks a DTO to be included in Public API docs.
/// </summary>
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Struct | AttributeTargets.Enum)]
public class PublicApiIncludeAttribute : Attribute
{
	// Nothing...
}

/// <summary>
/// Marks a DTO's property to be excluded from Public API docs.
/// </summary>
[AttributeUsage(AttributeTargets.Field | AttributeTargets.Property)]
public class PublicApiExcludeAttribute : Attribute
{
	// Nothing...
}