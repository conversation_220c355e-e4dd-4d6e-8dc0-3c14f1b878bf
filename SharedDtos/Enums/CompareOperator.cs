using System.Text.Json.Serialization;
using Levelbuild.Core.SharedDtos.Attributes;
using Levelbuild.Core.SharedUtilities;
using Microsoft.OpenApi.Extensions;

namespace Levelbuild.Core.SharedDtos.Enums;

[PublicApiInclude]
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum CompareOperator
{
	[CompareOperatorDescriptor(false, true, true)]
	Equals,

	[CompareOperatorDescriptor(true, true, true)]
	NotEquals,

	[CompareOperatorDescriptor(false, false, true)]
	Like,

	[CompareOperatorDescriptor(true, false, true)]
	NotLike,

	[CompareOperatorDescriptor(false, true, false)]
	GreaterThan,

	[CompareOperatorDescriptor(false, true, false)]
	GreaterThanEquals,
	
	[CompareOperatorDescriptor(false, true, false)]
	Less<PERSON>han,

	[CompareOperatorDescriptor(false, true, false)]
	LessThanEquals,
	
	[CompareOperatorDescriptor(false, true, true)]
	In,

	[CompareOperatorDescriptor(false, true, true)]
	NotIn,
	
	[CompareOperatorDescriptor(false, true, true)]
	IsNull,

	[CompareOperatorDescriptor(true, true, true)]
	IsNotNull
}

[AttributeUsage(AttributeTargets.Field)]
public class CompareOperatorDescriptorAttribute : Attribute
{
	public bool IsNumeric { get; }

	public bool IsText { get; }

	public bool IsNegative { get; }

	public CompareOperatorDescriptorAttribute(bool isNegative, bool isNumeric, bool isText)
	{
		IsNegative = isNegative;
		IsNumeric = isNumeric;
		IsText = isText;
	}
}

public static class CompareOperatorExtensions
{
	public static bool IsNumeric(this CompareOperator compareOperator)
	{
		var attr = GetAttributes(compareOperator);
		return attr.IsNumeric;
	}

	public static bool IsText(this CompareOperator enumValue)
	{
		var attr = GetAttributes(enumValue);
		return attr.IsText;
	}

	public static bool IsNegative(this CompareOperator enumValue)
	{
		var attr = GetAttributes(enumValue);
		return attr.IsNegative;
	}
	
	public static string GetTypeAsString(this CompareOperator enumValue)
	{
		return enumValue.GetDisplayName().ToLower();
	}

	public static string GetString(this CompareOperator enumValue)
	{
		return EnumUtils<CompareOperator>.GetTranslatableString(enumValue);
	}

	private static CompareOperatorDescriptorAttribute GetAttributes(CompareOperator enumValue)
	{
		return (EnumUtils<CompareOperator>.GetAttributeFromEnum<CompareOperatorDescriptorAttribute>(enumValue) as CompareOperatorDescriptorAttribute)!;
	}
}