#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member
using System.Text.Json.Serialization;
using Levelbuild.Core.SharedDtos.Attributes;
using Levelbuild.Core.SharedUtilities;
using Microsoft.OpenApi.Extensions;

namespace Levelbuild.Core.SharedDtos.Enums;

/// <summary>
/// Enum containing all data types relevant for DataStore config.
/// </summary>
[PublicApiInclude]
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum DataType
{
	[DataTypeDescriptor(false, true)]
	String,

	[DataTypeDescriptor(true, false)]
	Integer,

	[DataTypeDescriptor(true, false)]
	Long,

	[DataTypeDescriptor(true, false)]
	Double,

	[DataTypeDescriptor(false, false, true)]
	Date,

	[DataTypeDescriptor(false, false, true, true)]
	DateTime,

	[DataTypeDescriptor(false, false, false, true)]
	Time,

	[DataTypeDescriptor(false, true)]
	Text,

	[DataTypeDescriptor(false, false)]
	Boolean,
	
	[DataTypeDescriptor(false, false)]
	Guid,
	
	[DataTypeDescriptor(false, false, true, true)]
	DateTimeFixed,
	
	[DataTypeDescriptor(false, false, false, true)]
	TimeFixed,
}

[AttributeUsage(AttributeTargets.Field)]
public class DataTypeDescriptorAttribute(bool isNumeric, bool isText, bool isDate = false, bool isTime = false)
	: Attribute
{
	public bool IsText { get; set; } = isText;

	public bool IsNumeric { get; set; } = isNumeric;

	public bool IsDate { get; set; } = isDate;

	public bool IsTime { get; set; } = isTime;
}

public static class DataTypeExtensions
{
	public static string GetTypeAsString(this DataType enumValue)
	{
		return enumValue.GetDisplayName().ToLower();
	}

	public static bool IsNumeric(this DataType enumValue)
	{
		var attr = GetAttributes(enumValue);
		return attr.IsNumeric;
	}
	
	public static bool IsDate(this DataType enumValue)
	{
		var attr = GetAttributes(enumValue);
		return attr.IsDate;
	}
	
	public static bool IsTime(this DataType enumValue)
	{
		var attr = GetAttributes(enumValue);
		return attr.IsTime;
	}

	public static bool IsText(this DataType enumValue)
	{
		var attr = GetAttributes(enumValue);
		return attr.IsText;
	}

	public static string GetString(this DataType enumValue)
	{
		return EnumUtils<DataType>.GetTranslatableString(enumValue);
	}

	private static DataTypeDescriptorAttribute GetAttributes(DataType enumValue)
	{
		return (EnumUtils<DataType>.GetAttributeFromEnum<DataTypeDescriptorAttribute>(enumValue) as DataTypeDescriptorAttribute)!;
	}
}