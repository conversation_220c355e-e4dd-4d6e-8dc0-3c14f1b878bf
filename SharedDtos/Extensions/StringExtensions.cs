using System.Text.RegularExpressions;

namespace Levelbuild.Core.SharedDtos.Extensions;

/// <summary>
/// Extension methods for string data type.
/// </summary>
public static class StringExtensions
{
	/// <summary>
	/// Ensures that the given string results in a valid Storage field name.
	///
	/// Does not account for max length!
	/// </summary>
	/// <param name="fieldName"></param>
	/// <returns></returns>
	public static string ToStorageFieldName(this string fieldName)
	{
		// Replace whitespaces
		fieldName = fieldName.Replace(" ", "_");
		
		// Remove special characters
		var pattern = "[^a-zA-Z0-9_]+";
		fieldName = Regex.Replace(fieldName, pattern, "");
		
		return fieldName.Replace("__", "_");
	}
}