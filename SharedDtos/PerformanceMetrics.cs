namespace Levelbuild.Core.SharedDtos;

/// <summary>
/// DTO class for performance metrics in server responses.
/// </summary>
public class PerformanceMetrics
{
	/// <summary>
	/// Name of the metric.
	/// </summary>
	public string Name { get; init; }
	
	/// <summary>
	/// Timestamp.
	/// </summary>
	public long Time { get; init; }
	
	/// <summary>
	/// Child metrics belonging to the same parent.
	/// </summary>
	public List<PerformanceMetrics> ChildMetrics { get; } = new ();

	/// <summary>
	/// Constructor.
	/// </summary>
	/// <param name="name"></param>
	/// <param name="time"></param>
	public PerformanceMetrics(string name, long time)
	{
		Name = name;
		Time = time;
	}
}