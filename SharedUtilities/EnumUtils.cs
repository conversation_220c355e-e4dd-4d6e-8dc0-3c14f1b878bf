namespace Levelbuild.Core.SharedUtilities;

/// <summary>
/// Extension class for Enums, containing various functions
/// </summary>
/// <typeparam name="TEnum"></typeparam>
public static class EnumUtils<TEnum> where TEnum : Enum
{
	/// <summary>
	/// Gets the value of an attribute from the given enum member
	/// </summary>
	/// <param name="enumValue">specific member of the enum, from which the attribute should be gotten</param>
	/// <typeparam name="TAttribute">AttributeType</typeparam>
	/// <returns>The Attribute of the enumValue</returns>
	/// <exception cref="ArgumentException"></exception>
	public static Attribute? GetAttributeFromEnum<TAttribute>(TEnum enumValue) where TAttribute : Attribute
	{
		if (!typeof(TEnum).IsEnum)
		{
			throw new ArgumentException("Value must be an enumerated Type");
		}
		
		var type = typeof(TEnum);
		var name = Enum.GetName(type, enumValue);
		if (name == null)
			return null;
		var field = type.GetField(name);
		if (field == null)
			return null;
		var attr = Attribute.GetCustomAttribute(field, typeof(TAttribute));
		return attr;
	}

	/// <summary>
	/// Gets a translatable string from a member of an Enum
	/// </summary>
	/// <param name="enumValue">The member</param>
	/// <returns></returns>
	public static string GetTranslatableString(TEnum enumValue)
	{
		return typeof(TEnum).Name + "." + enumValue;
	}
}