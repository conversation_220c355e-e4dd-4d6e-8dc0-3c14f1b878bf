namespace Levelbuild.Core.StorageInterface.Dto.SearchSuggestions;

/// <summary>
/// Determines the substantial content type. Separates semantic text from names. 
/// </summary>
public enum FulltextIndexingType
{
	/// <summary>
	/// The value of this field will be suggested if the search value starts at the beginning of one of the words in this field<br/>
	/// The suggestion will contain the field name as well as the complete value of the field.<br/>
	/// e.g. field name: "Name", field value "<PERSON>", search value: "Do" will give "Name: <PERSON>" as response.<br/>
	/// Can be used to create additional filters like "Name=<PERSON>"<br/>
	/// The rules for <see cref="Content"/> also apply.
	/// </summary>
	Name,

	/// <summary>
	/// Will give single word type ahead functionality.<br/>
	/// e.g. field value: "pdfa and pdfx are different standards that can be supported within pdf files." with search value: "pd" will give [pdfa, pdfx, pdf] as response.<br/>
	/// The rules for <see cref="Content"/> also apply.
	/// The technical limitation for field length currently is 32766 bytes (elastic keyword field type).
	/// </summary>
	Summary,

	/// <summary>
	/// This should be used for file content and fields of type text. If a word within this field starts with the search word, the element id together with the parts of the content that match the query will be returned<br/>
	///  e.g. field value: "pdfa and pdfx are different standards that can be supported within pdf files." with search value: "pd" will give {id: {{elementId}}, values: ["&lt;em&gt;pdfa and pdfx&lt;/em&gt; are different", "supported within &lt;em&gt;pdf&lt;/em&gt; files"]}<br/>
	/// This should not be used for type ahead suggestions, but to navigate to this specific element directly.
	/// </summary>
	Content
}