namespace Levelbuild.Core.StorageInterface.Dto.SearchSuggestions;

/// <summary>
/// Suggested word or document
/// </summary>
public class StorageSuggestionElement
{
	/// <summary>
	/// type of suggestion
	/// </summary>
	public enum SuggestionType
	{
		/// <summary>
		/// type Word
		/// </summary>
		Word,
		/// <summary>
		/// type Field
		/// </summary>
		Field,
		/// <summary>
		/// type Document
		/// </summary>
		Document,
		/// <summary>
		/// type DidYouMean (suggestion)
		/// </summary>
		DidYouMean
	}

	/// <summary>
	/// Suggestion type
	/// </summary>
	// ReSharper disable once UnusedAutoPropertyAccessor.Global
	public SuggestionType Type { get; init; }

	/// <summary>
	/// suggested words
	/// </summary>
	// ReSharper disable once UnusedAutoPropertyAccessor.Global
	public string[] Words { get; init; }

	/// <summary>
	/// Storage field
	/// </summary>
	// ReSharper disable once UnusedAutoPropertyAccessor.Global
	public StorageField? Field { get; init; }

	/// <summary>
	/// Element Id in Storage
	/// </summary>
	// ReSharper disable once UnusedAutoPropertyAccessor.Global
	public string? ElementId { get; init; }

	/// <summary>
	/// 
	/// </summary>
	/// <param name="type">The suggestion type of this element</param>
	/// <param name="words">The search suggestion to be shown to the user. Especially for Document suggestions,
	/// there might be multiple phrases in a single document, which will be different elements in this array</param>
	/// <param name="field">The field this suggestion applies to. Can be used to add additional filters to this query with field=suggestion when selected.</param>
	/// <param name="elementId">The ElementId this suggestion has been drawn from. Can be used to directly navigate to this element.</param>
	public StorageSuggestionElement(SuggestionType type, string[] words, StorageField? field = null, string? elementId = null)
	{
		Type = type;
		Words = words;
		Field = field;
		ElementId = elementId;
	}
}