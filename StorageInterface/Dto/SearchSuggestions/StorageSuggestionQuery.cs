using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Filter;
using Levelbuild.Core.DataStoreInterface.Filter.FilterTypes;

namespace Levelbuild.Core.StorageInterface.Dto.SearchSuggestions;

/// <summary>
/// Query to search for word suggestions
/// </summary>
public class StorageSuggestionQuery
{
	/// <summary>
	/// Data source name
	/// </summary>
	// ReSharper disable once UnusedAutoPropertyAccessor.Global
	public string DataSourceName { get; init; }

	/// <summary>
	/// The free form input
	/// </summary>
	// ReSharper disable once UnusedAutoPropertyAccessor.Global
	public FulltextSearchFilter FulltextSearchFilter { get; private set; }

	/// <summary>
	/// Fields which need to be selected
	/// </summary>
	// ReSharper disable once UnusedAutoPropertyAccessor.Global
	public IList<DataStoreQueryField>? Fields { get; init; }

	/// <summary>
	/// Filters which need to be applied
	/// </summary>
	// ReSharper disable once UnusedAutoPropertyAccessor.Global
	public QueryFilterGroup? Filter { get; private set; }

	/// <summary>
	/// </summary>
	/// <param name="dataSourceName">The name of the datasource to get the suggestions from</param>
	/// <param name="fulltextSearchFilter">The fulltext search value for which the suggestions should fit.</param>
	/// <param name="fields">This parameter is used to limit fields to show "From: <EMAIL>" suggestions</param>
	/// <param name="filter">The filters to apply for the suggestions. Some filters will not be evaluated.</param>
	public StorageSuggestionQuery(string dataSourceName, FulltextSearchFilter fulltextSearchFilter, IList<DataStoreQueryField>? fields = null,
								  QueryFilterGroup? filter = null)
	{
		DataSourceName = dataSourceName;
		FulltextSearchFilter = fulltextSearchFilter;
		Fields = fields;
		Filter = filter;
	}
}