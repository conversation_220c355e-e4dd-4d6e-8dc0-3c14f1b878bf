using Levelbuild.Core.DataStoreInterface.Dto.Interfaces;

namespace Levelbuild.Core.StorageInterface.Dto;

/// <summary>
/// Represents a Storage-specific context info which may contain additional information provided by the storage implementation
/// </summary>
public class StorageContext : IDataStoreContext
{
	/// <summary>
	/// The id for this context
	/// </summary>
	public string Identifier { get; init; }
	
	/// <summary>
	/// Contains all the backend context configuration.
	/// </summary>
	public IDictionary<string, object> Config;
	
	/// <summary>
	/// 
	/// </summary>
    public StorageContext(string identifier, IDictionary<string, object> config)
	{
		Identifier = identifier;
		Config = config;
	}
}