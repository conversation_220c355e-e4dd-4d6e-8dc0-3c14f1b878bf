using Levelbuild.Core.DataStoreInterface.Dto.Interfaces;

namespace Levelbuild.Core.StorageInterface.Dto;

/// <summary>
/// Implements the basic <see cref="IDataStoreDataSource" /> and in addition includes all properties of the <see cref="StorageDataSourceConfig"/>
/// </summary>
public class StorageDataSource : StorageDataSourceConfig, IDataStoreDataSource
{
    /// <summary>
    /// List of data fields available inside this data source
    /// </summary>
    public IList<IDataStoreField> Fields { get; init; }

	/// <summary>
	/// List of filter groups which can be used for advanced filter functions
	/// </summary>
	// ReSharper disable once UnusedAutoPropertyAccessor.Global
	public IList<StorageFilterGroup> FilterGroups { get; init; } = new List<StorageFilterGroup>();
    
    /// <param name="name">Data source name</param>
    /// <param name="fields">Data source fields</param>
	/// <param name="filterGroups">optional list of available filter groups</param>
    public StorageDataSource(string name, IList<IDataStoreField> fields, IList<StorageFilterGroup>? filterGroups = null) : base(name)
    {
        Fields = fields;
		if (filterGroups != null)
			FilterGroups = filterGroups;
	}
}