namespace Levelbuild.Core.StorageInterface.Dto;

/// <summary>
/// DTO used to configure a storage definition
/// </summary>
public class StorageDataSourceConfig
{
	/// <summary>
	/// Data source name (can't be changed afterwards!)
	/// </summary>
	public string Name { get; init; }

	/// <summary>
	/// Should there be a revision history for data changes?
	/// </summary>
	public bool StoreRevisions { get; init; } = true;

	/// <summary>
	/// Should there be a fulltext column containing the content of all user columns (which don't have StorageFieldConfig.ExcludeFromFulltext set to true)? 
	/// </summary>
	public bool StoreFieldContent { get; init; } = true;

	/// <summary>
	/// Should there be a fulltext column containing the file content?
	/// </summary>
	public bool StoreFileContent { get; init; } = true;

	/// <summary>
	/// Should the file- and revision data be stored in a specific file path? 
	/// </summary>
	public string? StoragePath { get; init; }

	/// <summary>
	/// Disable/Enable fulltext search feature on data source
	/// </summary>
	// Re<PERSON>harper disable once RedundantDefaultMemberInitializer
	public bool FulltextSearch { get; set; } = false;

	/// <param name="name">Data source name</param>
	public StorageDataSourceConfig(string name)
	{
		Name = name;
	}
	
	/// <summary>
	/// Flag indicating the datasource is created in customer context and should not be changed by updates
	/// </summary>
	public bool CustomerSpecific { get; set; } = false;
	
	/// <summary>
	/// Flag indicating the datasource is a view
	/// </summary>
	public bool IsView { get; set; } = false;
	
	/// <summary>
	/// SQL definition, if datasource is a view
	/// </summary>
	public string? ViewDefinition { get; set; }
}