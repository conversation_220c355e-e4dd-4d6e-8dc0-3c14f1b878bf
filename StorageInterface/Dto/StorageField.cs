using Levelbuild.Core.DataStoreInterface.Dto.Interfaces;

namespace Levelbuild.Core.StorageInterface.Dto;

/// <summary>
/// Implements the basic <see cref="IDataStoreField" /> and in addition includes all properties of the <see cref="StorageFieldConfig"/>
/// </summary>
public class StorageField : StorageFieldConfig
{
	
	/// <summary>
	/// is a specific system field?
	/// </summary>
	// ReSharper disable once UnusedAutoPropertyAccessor.Global
	public bool SystemField { get; init; }

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="name"></param>
    public StorageField(string name) : base(name)
    {
        Name = name;
    }
}