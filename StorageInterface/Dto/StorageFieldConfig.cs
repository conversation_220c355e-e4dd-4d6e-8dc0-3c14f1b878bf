using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.StorageInterface.Dto.SearchSuggestions;

namespace Levelbuild.Core.StorageInterface.Dto;

/// <summary>
/// Extends the <see cref="DataStoreField"/> to provide additional properties needed to manage our storage solution.
/// </summary>
public class StorageFieldConfig : DataStoreField
{
	/// <summary>
	/// Name of the linked lookup source (if any)
	/// </summary>
	// ReSharper disable once UnusedAutoPropertyAccessor.Global
	public string? LookupSource { get; init; }

	/// <summary>
	/// Name of the connected lookup source field (if any)
	/// </summary>
	public string? LookupSourceField { get; init; } // TODO: should we always link to pk?!

	/// <summary>
	/// If there is a connected lookup definition, should the storage system ensure reference integrity during create/update?
	/// </summary>
	public bool LookupCheck { get; init; } // TODO: if lookup check .. do we also need lookup behaviour to setNull / deleteCascade?

	/// <summary>
	/// Flag indicating the field is created in customer context and should not be changed by updates
	/// </summary>
	public bool CustomerSpecific { get; set; } = false;
	
	/// <summary>
	/// Should the content of this field be stored encrypted inside the database?
	/// </summary>
	// ReSharper disable once UnusedAutoPropertyAccessor.Global
	public bool Encrypted { get; init; }

	/// <summary>
	/// Should the fulltext data exclude this specific field? 
	/// </summary>
	public bool ExcludeFromFulltext { get; init; } = false;

	/// <summary>
	/// How should this field be indexed?
	/// </summary>
	// ReSharper disable once UnusedAutoPropertyAccessor.Global
	public FulltextIndexingType FulltextIndexingType { get; init; } = FulltextIndexingType.Content;

	// TODO: translation features

	// TODO: indexing features

	/// <summary>
	/// Create a new field definition instance. Use object initializers to init the remaining properties.
	/// <example>new StorageFieldDefinition("addressfield") { SystemField = true }</example>
	/// </summary>
	/// <param name="name">Field name</param>
	public StorageFieldConfig(string name) : base(name)
	{
	}
}