namespace Levelbuild.Core.StorageInterface.Dto;

/// <summary>
/// represents an additional grouping of fields which can be used for advanced filter functions
/// </summary>
public class StorageFilterGroup
{
	/// <summary>
	/// Name of the filter group
	/// </summary>
	// ReSharper disable once UnusedAutoPropertyAccessor.Global
	public string Name { get; init; }
	
	/// <summary>
	/// List of field names contained inside this group
	/// </summary>
	// ReSharper disable once UnusedAutoPropertyAccessor.Global
	public IList<string> Fields { get; init; }

	/// <summary>
	/// Constructor
	/// </summary>
	/// <param name="name">Name of the filter group</param>
	/// <param name="fields">List of field names contained inside this group</param>
	public StorageFilterGroup(string name, IList<string> fields)
	{
		Name = name;
		Fields = fields;
	}
}