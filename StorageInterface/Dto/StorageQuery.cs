using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Filter;

namespace Levelbuild.Core.StorageInterface.Dto;

/// <summary>
/// Represents a storage specific query which may contain group filters in addition to the default <see cref="DataStoreQuery"/>
/// </summary>
public class StorageQuery : DataStoreQuery
{
	/// <summary>
	/// Group filters contain filter conditions which only apply to a certain group of fields. They are used to apply advanced rights management on field level
	/// </summary>
	// ReSharper disable once UnusedAutoPropertyAccessor.Local
	private IDictionary<string, QueryFilterGroup> GroupFilters { get; init; } = new Dictionary<string, QueryFilterGroup>();
	
	/// <summary>
	/// Constructor which makes it possible to init the group filters
	/// </summary>
	/// <param name="dataSourceName">Data Source Name</param>
	/// <param name="fields">Fields which should be selected</param>
	/// <param name="groupFilters">Additional filters to access certain fields</param>
	public StorageQuery(string dataSourceName, List<DataStoreQueryField>? fields, IDictionary<string, QueryFilterGroup>? groupFilters) : base(dataSourceName, fields)
	{
		if (groupFilters != null)
			GroupFilters = groupFilters;
	}
}