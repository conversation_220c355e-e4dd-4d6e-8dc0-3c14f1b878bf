using Levelbuild.Core.DataStoreInterface;
using Levelbuild.Core.DataStoreInterface.Dto;

namespace Levelbuild.Core.StorageInterface;

/// <summary>
/// Currently a placeholder for future extensions which go further than the more general <see cref="IDataStore" />.
/// </summary>
public interface IStorage : IDataStore
{
	/// <summary>
	/// Get the Configuration Object for contexts of this backend.
	/// (contains meta information about the implementation as well as the available configuration options)
	/// </summary>
	public static abstract DataStoreInfo GetContextInfo();
}