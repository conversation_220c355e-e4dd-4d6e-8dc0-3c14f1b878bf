using System.Globalization;
using Levelbuild.Core.DataStoreInterface;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Dto.Interfaces;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.DataStoreInterface.Filter;
using Levelbuild.Core.DataStoreInterface.Filter.FilterTypes;
using Levelbuild.Core.StorageInterface;
using Levelbuild.Core.StorageInterface.Dto;
using Newtonsoft.Json;
using Xunit.Abstractions;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Core.WebAppCoreTests.DataStoreInterface;

/// <summary>
/// Column names of the columns where the filter tests can be run against.
/// Every column name should be filled with a column of the corresponding datatype.
/// The IdCol can be of type string as well as integer.
/// </summary>
public class TestDataSourceCols
{
	/// <summary>
	/// The primary key column - should be filled by the storage solution
	/// </summary>
	public string IdCol { get; set; }
	
	/// <summary>
	/// A varchar column with at least 255 characters
	/// </summary>
	public string StringCol { get; set; }
	
	/// <summary>
	/// A varchar column with at least 255 characters, nullable
	/// </summary>
	public string StringCol1 { get; set; }
	
	/// <summary>
	/// An int 32 column, nullable
	/// </summary>
	public string IntCol { get; set; }
	
	/// <summary>
	/// An int 32 column, nullable
	/// </summary>
	public string IntCol2 { get; set; }
	
	/// <summary>
	/// double column, nullable
	/// </summary>
	public string DoubleCol { get; set; }
	
	/// <summary>
	/// A DateTime column, nullable
	/// </summary>
	public string DateTimeCol { get; set; }
	
	/// <summary>
	/// A boolean/ bit column
	/// </summary>
	public string BoolCol { get; set; }
	
	/// <summary>
	/// If multi value fields are supported, one with base type string should be supplied here, otherwise null
	/// </summary>
	public string? MultiValueStringCol { get; set; }
	
	/// <summary>
	/// If field translations are supported, one should be supplied here. For these tests, the following languages will be used: ["en", "de", "fr", "it"]
	/// </summary>
	public string? TranslatableCol { get; set; }
	
	/// <summary>
	/// When using this constructor, most of the tests will fail.
	/// It is only suitable for calling some special functions on this instance.
	/// </summary>
	internal TestDataSourceCols()
	{
		IdCol = "";
		StringCol = "";
		StringCol1 = "";
		IntCol = "";
		IntCol2 = "";
		DoubleCol = "";
		DateTimeCol = "";
		BoolCol = "";
	}

	/// <summary>
	/// <inheritdoc cref="TestDataSourceCols"/>
	/// </summary>
	/// <param name="idCol"></param>
	/// <param name="stringCol"></param>
	/// <param name="stringCol1"></param>
	/// <param name="intCol"></param>
	/// <param name="intCol2"></param>
	/// <param name="doubleCol"></param>
	/// <param name="dateTimeCol"></param>
	/// <param name="boolCol"></param>
	/// <param name="multiValueStringCol"></param>
	/// <param name="translatableCol">translatable field with languages ["en", "de", "fr", "it"]</param>
	public TestDataSourceCols(string idCol, string stringCol, string stringCol1, string intCol, string intCol2, string doubleCol, string dateTimeCol,
							  string boolCol,
							  string? multiValueStringCol, string? translatableCol)
	{
		IdCol = idCol;
		StringCol = stringCol;
		StringCol1 = stringCol1;
		IntCol = intCol;
		IntCol2 = intCol2;
		DoubleCol = doubleCol;
		DateTimeCol = dateTimeCol;
		BoolCol = boolCol;
		MultiValueStringCol = multiValueStringCol;
		TranslatableCol = translatableCol;
	}
}

/// <summary>
/// This class attempts to test the functions of the <see cref="DataStoreInterface"/> - especially CRUD for data of a predefined <seealso cref="DataStoreDataSource"/>; inclusive extensive filter tests
/// </summary>
[Trait("Category", "Default Interface Tests")]
public abstract class DataStoreInterfaceTest
{
	/// <summary>
	/// The tests will be executed against this <see cref="IDataStore"/>
	/// </summary>
	protected readonly IDataStore DataStore;
	
	/// <summary>
	/// This output helper is being used for logging
	/// </summary>
	protected readonly ITestOutputHelper TestOutputHelper;
	
	/// <summary>
	/// The tests will be executed against this <see cref="DataStoreDataSource"/>.
	/// The real DataSource will be fetched through the <seealso cref="DataStore"/>.
	/// </summary>
	public string TestDataSourceName { get; set; }
	
	/// <summary>
	/// Contains the names of all the columns that are necessary for the tests
	/// </summary>
	public TestDataSourceCols TestTableCols { get; set; }
	
	protected DataStoreAuthentication TestAuth = new DataStoreAuthentication("testuser", Guid.NewGuid(), new List<string>()
	{
		"test"
	});

	/// <summary>
	/// This constructor needs to be overwritten by the derived class
	/// </summary>
	/// <param name="dataStore"></param>
	/// <param name="testDataSourceName"></param>
	/// <param name="testOutputHelper"></param>
	/// <param name="testTableCols"></param>
	public DataStoreInterfaceTest(IDataStore dataStore, string testDataSourceName, ITestOutputHelper testOutputHelper,
								  TestDataSourceCols testTableCols)
	{
		TestOutputHelper = testOutputHelper;
		DataStore = dataStore;
		TestDataSourceName = testDataSourceName;
		TestTableCols = testTableCols;
	}
	
	/// <summary>
	/// This method will test the basic create, read, update and delete of a data entry
	/// </summary>
	[Fact(DisplayName = "Create read update delete for data working")]
	public virtual void CrudTest()
	{
		var testuserId = Guid.NewGuid();
		// use 2 different connections to check that changes are persisted 
		var connection = DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, new DataStoreAuthentication("testuser", testuserId, new List<string>(){ "test"}));
		var compareConnection = DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, new DataStoreAuthentication("testuser", testuserId, new List<string>(){ "test"}));

		IDataStoreDataSource? dataSource = connection.GetDataSource(TestDataSourceName);
		Assert.NotNull(dataSource);
		Assert.Contains(dataSource.Fields, it => it is { Readonly: false, PrimaryKey: false });
		
		// create
		Dictionary<string, object?> dict = RandomDataHelper.GetDataSourceData(dataSource);
		var info = connection.CreateElement(TestDataSourceName, new DataStoreElementData(dict, new List<string> { "test" }),
											new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 0, "me"));
		
		connection.IsGetElementAllowed(TestDataSourceName, info.ElementId);
		
		// set other values null
		dict.TryAdd(TestTableCols.StringCol, null);
		dict.TryAdd(TestTableCols.StringCol1, null);
		dict.TryAdd(TestTableCols.IntCol, null);
		dict.TryAdd(TestTableCols.IntCol2, null);
		dict.TryAdd(TestTableCols.DoubleCol, null);
		dict.TryAdd(TestTableCols.BoolCol, null);
		dict.TryAdd(TestTableCols.DateTimeCol, null);
		if (TestTableCols.MultiValueStringCol != null)
		{
			if (dict.TryGetValue(TestTableCols.MultiValueStringCol, out var v))
				dict[TestTableCols.MultiValueStringCol] = v ?? Array.Empty<object>();
			else
				dict[TestTableCols.MultiValueStringCol] = Array.Empty<object>();
		}
		
		if (dict.TryGetValue(TestTableCols.DateTimeCol, out object? dt) && dt != null)
		{
			dict[TestTableCols.DateTimeCol] = ((DateTime)dt).ToUniversalTime();
		}
		
		foreach (var entry in dict)
		{
			if (entry.Key.Contains("__") && string.IsNullOrEmpty(entry.Value?.ToString()))
				dict[entry.Key] = null;
		}
		
		Assert.NotNull(info.ElementId);
		Assert.NotNull(info.ElementData);
		
		var readonlyFields = dataSource.Fields.Where(it => it.Readonly).Select(it => it.Name);
		Assert.Equivalent(dict, info.ElementData.Values);
		
		// read
		var doc = connection.GetElement(TestDataSourceName, info.ElementId);
		var doc1 = compareConnection.GetElement(TestDataSourceName, info.ElementId);
		
		Assert.Equivalent(info.ElementData.Values.Where(v => !readonlyFields.Contains(v.Key)),
						  doc.Values.Where(v => !readonlyFields.Contains(v.Key)), true);
		Assert.Equivalent(info.ElementData.Values.Where(v => !readonlyFields.Contains(v.Key)),
						  doc1.Values.Where(v => !readonlyFields.Contains(v.Key)), true);
		
		// update
		var dict1 = RandomDataHelper.GetDataSourceData(dataSource, dict, 0.25);
		
		DataStoreElementData dataStoreElementData = new DataStoreElementData(info.ElementId, dict1);
		var info1 = connection.UpdateElement(TestDataSourceName, dataStoreElementData,
											 new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 0, "me"));
		
		connection.IsGetElementAllowed(TestDataSourceName, info1.ElementId);
		
		Assert.Equal(info.ElementId, info1.ElementId);
		dict1.TryAdd(TestTableCols.StringCol, dict[TestTableCols.StringCol]);
		dict1.TryAdd(TestTableCols.StringCol1, dict[TestTableCols.StringCol1]);
		dict1.TryAdd(TestTableCols.IntCol, dict[TestTableCols.IntCol]);
		dict1.TryAdd(TestTableCols.IntCol2, dict[TestTableCols.IntCol2]);
		dict1.TryAdd(TestTableCols.DoubleCol, dict[TestTableCols.DoubleCol]);
		dict1.TryAdd(TestTableCols.BoolCol, dict[TestTableCols.BoolCol]);
		dict1.TryAdd(TestTableCols.DateTimeCol, dict[TestTableCols.DateTimeCol]);
		if (TestTableCols.MultiValueStringCol != null)
		{
			var old = dict[TestTableCols.MultiValueStringCol];
			if (dict1.TryGetValue(TestTableCols.MultiValueStringCol, out var v))
			{
				if (v == null)
					dict1[TestTableCols.MultiValueStringCol] = Array.Empty<object>();
			}
			else
			{
				dict1[TestTableCols.MultiValueStringCol] = old;
			}
		}
		
		if (dict1.TryGetValue(TestTableCols.DateTimeCol, out object? dt1) && dt1 != null)
		{
			dict1[TestTableCols.DateTimeCol] = ((DateTime)dt1).ToUniversalTime();
		}
		
		foreach (var entry in dict.Where(it => it.Key.Contains("__")))
		{
			dict1.TryAdd(entry.Key, entry.Value);
		}
		
		foreach (var entry in dict1.Where(it => it.Key.Contains("__")))
		{
			if (string.IsNullOrEmpty(entry.Value?.ToString()))
			{
				dict1[entry.Key] = null;
			}
		}
		
		if (TestTableCols.TranslatableCol != null)
		{
			var translatableField = dataSource.Fields.First(it => it.Name == TestTableCols.TranslatableCol);
			foreach (var language in translatableField.Languages)
			{
				dict1.TryAdd(translatableField.Name + "__" + language, null);
			}
		}

		if (info1.ElementData != null)
			Assert.Equivalent(dict1, info1.ElementData.Values.Where(v => !readonlyFields.Contains(v.Key)).ToDictionary(), true);
		doc = connection.GetElement(TestDataSourceName, info1.ElementId);
		doc1 = compareConnection.GetElement(TestDataSourceName, info1.ElementId);
		Assert.Equal(info1.ElementId, doc.Id);
		Assert.Equal(info1.ElementId, doc1.Id);
		if (info1.ElementData != null)
		{
			Assert.Equivalent(info1.ElementData.FileInfo, doc.FileInfo, true);
			Assert.Equivalent(info1.ElementData.FileInfo, doc1.FileInfo, true);
			Assert.Equivalent(info1.ElementData.Values.Where(v => !readonlyFields.Contains(v.Key)).ToDictionary(),
							  doc.Values.Where(v => !readonlyFields.Contains(v.Key)).ToDictionary(), true);
			Assert.Equivalent(info1.ElementData.Values.Where(v => !readonlyFields.Contains(v.Key)).ToDictionary(),
							  doc1.Values.Where(v => !readonlyFields.Contains(v.Key)).ToDictionary(), true);
		}
		
		// delete
		var info2 = connection.DeleteElement(TestDataSourceName, info1.ElementId, new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 0, "me"));
		Assert.Equal(false, connection.IsGetElementAllowed(TestDataSourceName, info1.ElementId));
		Assert.Equal(info.ElementId, info2.ElementId);
		Assert.Null(info2.ElementData);
		
		Assert.Null(connection.GetElement(TestDataSourceName, info2.ElementId));
		Assert.Null(compareConnection.GetElement(TestDataSourceName, info2.ElementId));
	}
	
	/// <summary>
	/// This method will test the basic create, read, update and delete of a data entry
	/// </summary>
	[Fact(DisplayName = "Create read update delete for data working - ASYNC")]
	public virtual async Task CrudTestAsync()
	{
		// use 2 different connections to check that changes are persisted 
		var connection = DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, new DataStoreAuthentication("testuser", Guid.NewGuid(), new List<string>(){ "test"}));
		
		IDataStoreDataSource? dataSource = connection.GetDataSource(TestDataSourceName);
		Assert.NotNull(dataSource);
		int count = dataSource.Fields.Where(it => !it.Readonly && !it.PrimaryKey).Count();
		Assert.True(count > 0);
		
		// create element
		Dictionary<string, object?> dict = RandomDataHelper.GetDataSourceData(dataSource);
		var infoCreate = await connection.CreateElementAsync(TestDataSourceName, new DataStoreElementData(dict, new List<string>() {"test"}),
													   new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 0, "me"));

		// read
		var infoRead = await connection.GetElementAsync(TestDataSourceName, infoCreate.ElementId);
		
		// update
		var dict1 = RandomDataHelper.GetDataSourceData(dataSource, dict, 0.25);
		DataStoreElementData dataStoreElementData = new DataStoreElementData(infoCreate.ElementId, dict1, new List<string>() {"test"});
		var infoUpdate = await connection.UpdateElementAsync(TestDataSourceName, dataStoreElementData,
															 new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 0, "me"));
		
		// delete
		var infoDelete = await connection.DeleteElementAsync(TestDataSourceName, infoUpdate.ElementId,
															 new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 0, "me"));

		Assert.NotNull(infoCreate.ElementId);
		Assert.NotNull(infoCreate.ElementData);
		Assert.Equal(infoCreate.ElementId, infoRead!.Id);
		Assert.Equal(infoCreate.ElementId, infoUpdate.ElementId);
		Assert.Null(infoDelete.ElementData);
		Assert.Equal(infoCreate.ElementId, infoDelete.ElementId);
		Assert.Null(await connection.GetElementAsync(TestDataSourceName, infoDelete.ElementId));
	}
	
	/// <summary>
	/// This test attempts to check if exceptions are being thrown on erroneous input 
	/// </summary>
	[Fact(DisplayName = "Exceptions on invalid operations are being thrown")]
	public virtual void ExceptionsTest()
	{
		var connection = DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, new DataStoreAuthentication("testuser", Guid.NewGuid(), new List<string>() {"test"}));
		var notExistingDataSource = RandomDataHelper.Random.NextString(21, false);
		
		// create
		Assert.Throws<DataSourceNotFoundException>(
			() => connection.CreateElement(notExistingDataSource, new DataStoreElementData(new Dictionary<string, object?>()
			{
				["stuff"] = ""
			}, new List<string> { "test" }), new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 0, "me")));
		Assert.Throws<DataStoreOperationException>(
			() => connection.CreateElement(TestDataSourceName, new DataStoreElementData(new Dictionary<string, object?>(), new List<string> { "test" }),
										   new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 0, "me")));
		Assert.Throws<DataStoreOperationException>(() => connection.CreateElement(TestDataSourceName, new DataStoreElementData(new Dictionary<string, object?>()
		{
			["notExistingColumnStuff"] = 123
		}, new List<string> { "test" }), new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 0, "me")));

		// read
		Assert.Throws<DataSourceNotFoundException>(() => connection.GetElement(notExistingDataSource, "elementid"));
		Assert.Null(connection.GetElement(TestDataSourceName, Guid.NewGuid().ToString()));
		
		// update
		Assert.Throws<DataSourceNotFoundException>(
			() => connection.UpdateElement(notExistingDataSource, new DataStoreElementData(new Dictionary<string, object?>(), new List<string> { "test" }),
										   new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 0, "me")));
		Assert.Throws<ElementNotFoundException>(() => connection.UpdateElement(TestDataSourceName, new DataStoreElementData(
																				   "unavailable_elementid", new Dictionary<string, object?>()
																				   {
																					   ["stuff"] = ""
																				   }),
																			   new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 0, "me")));
		
		IDataStoreDataSource dataSource = connection.GetDataSource(TestDataSourceName)!;
		Dictionary<string, object?> dict = RandomDataHelper.GetDataSourceData(dataSource);
		var info = connection.CreateElement(TestDataSourceName, new DataStoreElementData(dict, new List<string> { "test" }),
											new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 0, "me"));
		var dict1 = RandomDataHelper.GetDataSourceData(dataSource, dict, 0.25);
		dict1["notExistingColumnStuff"] = "blah";
		Assert.Throws<DataStoreOperationException>(() => connection.UpdateElement(TestDataSourceName, new DataStoreElementData(info.ElementId, dict1),
																				  new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 0, "me")));
		
		// delete
		Assert.Throws<DataSourceNotFoundException>(
			() => connection.DeleteElement(notExistingDataSource, "elementid", new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 0, "me")));
		Assert.Throws<ElementNotFoundException>(() => connection.DeleteElement(TestDataSourceName, Guid.NewGuid().ToString(),
																			   new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 0, "me")));
	}
	
	#region basic navigation tests
	
	/// <summary>
	/// This method will test the basic limit offset navigation features
	/// </summary>
	[Fact(DisplayName = "Limit offset test")]
	public virtual void LimitOffsetTest()
	{
		// use 2 different connections to check that changes are persisted 
		var connection = DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		var compareConnection = DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		IDataStoreDataSource dataSource = connection.GetDataSource(TestDataSourceName)!;
		int elementsToCreateCount = 100;
		
		CreateRandomElements(connection, dataSource, elementsToCreateCount, false);
		List<string> selectedElements = new List<string>();
		
		int lastCircle;
		// test limit and paging
		for (lastCircle = 0; lastCircle < 4; lastCircle++)
		{
			DataStoreQuery query = new DataStoreQuery(dataSource.Name, null).WithPaging(25, (lastCircle * 25));
			var elements = compareConnection.GetElements(query);
			
			Assert.Equal(25, elements.Count);
			
			// check, if every element is selected once
			foreach (var element in elements)
			{
				Assert.True(!selectedElements.Contains(element.Id));
				selectedElements.Add(element.Id);
			}
		}
		
		lastCircle++;
		DataStoreQuery query2 = new DataStoreQuery(dataSource.Name, null).WithPaging(25, (lastCircle * 25));
		var elements2 = compareConnection.GetElements(query2);
		Assert.Empty(elements2);
	}
	
	
	/// <summary>
	/// This method will test the basic count all feature
	/// </summary>
	[Fact(DisplayName = "count all test")]
	public virtual void CountAllTest()
	{
		// use 2 different connections to check that changes are persisted 
		var connection = DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		var compareConnection = DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		IDataStoreDataSource dataSource = connection.GetDataSource(TestDataSourceName)!;
		int elementsToCreateCount = 100;
		
		CreateRandomElements(connection, dataSource, elementsToCreateCount, false);
		
		DataStoreQuery query = new DataStoreQuery(dataSource.Name, null).WithCountAll();
		var elementsNoPaging = compareConnection.GetElements(query);
		
		DataStoreQuery queryWithPaging = query.WithPaging(25, 25);
		var elementsWithPaging = compareConnection.GetElements(queryWithPaging);
		
		Assert.Equal(elementsNoPaging.CountTotal, elementsNoPaging.Count);
		Assert.Equal(100, elementsWithPaging.CountTotal);
		Assert.Equal(25, elementsWithPaging.Count);
	}
	
	/// <summary>
	/// This method will test the basic count all feature
	/// </summary>
	[Fact(DisplayName = "extended count all test")]
	public virtual void ExtendedCountAllTest()
	{
		// use 2 different connections to check that changes are persisted 
		var connection = DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		var compareConnection = DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		IDataStoreDataSource dataSource = connection.GetDataSource(TestDataSourceName)!;
		int elementsToCreateCount = 100;
		
		var createdElements = CreateRandomElements(connection, dataSource, elementsToCreateCount, false);
		
		var pkField = dataSource.Fields.First(it => it.PrimaryKey);
		
		DataStoreQuery query = new DataStoreQuery(dataSource.Name, dataSource.Fields.Select(it => new DataStoreQueryField(it.Name)).ToList())
			.WithFilter(new QueryFilterGroup().AddFilter(new NotEqualsFilter(new QueryFilterField(pkField.Name), createdElements.First().ElementId)))
			.WithOrderBy(new List<DataStoreElementSort>()
			{
				new (dataSource.Fields.First(it => !it.PrimaryKey).Name)
			})
			.WithCountAll();
		var elementsNoPaging = compareConnection.GetElements(query);
		
		DataStoreQuery queryWithPaging = query.WithPaging(25, 25);
		var elementsWithPaging = compareConnection.GetElements(queryWithPaging);
		
		DataStoreQuery query1 = new DataStoreQuery(dataSource.Name, dataSource.Fields.Select(it => new DataStoreQueryField(it.Name)).ToList())
			.WithFilter(new QueryFilterGroup().AddFilter(new NotEqualsFilter(new QueryFilterField(pkField.Name), createdElements.First().ElementId)))
			.WithOrderBy(new List<DataStoreElementSort>()
			{
				new (dataSource.Fields.First(it => !it.PrimaryKey).Name)
			})
			.WithGroupBy(new List<string>()
			{
				pkField.Name
			})
			.WithCountAll();
		var elementsNoPaging1 = compareConnection.GetElements(query1);
		var elementsWithPaging1 = compareConnection.GetElements(queryWithPaging);
		
		Assert.Equal(elementsNoPaging.CountTotal, elementsNoPaging.Count);
		Assert.Equal(99, elementsWithPaging.CountTotal);
		Assert.Equal(25, elementsWithPaging.Count);
		
		Assert.Equal(elementsNoPaging1.CountTotal, elementsNoPaging1.Count);
		Assert.Equal(99, elementsWithPaging1.CountTotal);
		Assert.Equal(25, elementsWithPaging1.Count);
	}
	
	
	/// <summary>
	/// This method will insert 100 elements async and test the basic count all feature
	/// </summary>
	[Fact(DisplayName = "Count all test with ASYNC create multiple elements")]
	public virtual async Task CountAllTestAsync()
	{
		var testuserId = Guid.NewGuid();
		// use 2 different connections to check that changes are persisted 
		var connection = DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, new DataStoreAuthentication("testuser", testuserId, new List<string>() {"test"}));
		var compareConnection = DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, new DataStoreAuthentication("testuser", testuserId, new List<string>() {"test"}));
		IDataStoreDataSource dataSource = connection.GetDataSource(TestDataSourceName)!;
		int elementsToCreateCount = 100;
		
		IList<DataStoreElementData> elementDatas = new List<DataStoreElementData>();
		for (int i = 0; i < elementsToCreateCount; i++)
		{
			Dictionary<string, object?> dict = RandomDataHelper.GetDataSourceData(dataSource);
			DataStoreElementData elementData = new DataStoreElementData(dict, new List<string>() {"test"});
			elementDatas.Add(elementData);
		}
		
		await connection.CreateElementsAsync(dataSource.Name, elementDatas,
											 new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 0, "ARI"));
		
		DataStoreQuery query = new DataStoreQuery(dataSource.Name, null).WithCountAll();
		
		var elementsNoPagingAsync = await compareConnection.GetElementsAsync(query);
		var elementsNoPaging = compareConnection.GetElements(query);
		
		DataStoreQuery queryWithPaging = query.WithPaging(25, 25);
		var elementsWithPagingAsync = await compareConnection.GetElementsAsync(queryWithPaging);
		var elementsWithPaging = compareConnection.GetElements(queryWithPaging);
		
		Assert.Equal(elementsNoPaging.CountTotal, elementsNoPaging.Count);
		Assert.Equal(100, elementsWithPaging.CountTotal);
		Assert.Equal(25, elementsWithPaging.Count);
		
		Assert.Equal(elementsNoPagingAsync.CountTotal, elementsNoPagingAsync.Count);
		Assert.Equal(100, elementsWithPagingAsync.CountTotal);
		Assert.Equal(25, elementsWithPagingAsync.Count);
	}
	
	
	/// <summary>
	/// This method will test the basic sorting features
	/// </summary>
	[Fact(DisplayName = "Sorting single test")]
	public virtual void SortingTest()
	{
		// use 2 different connections to check that changes are persisted 
		var connection = DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		var compareConnection = DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		IDataStoreDataSource dataSource = connection.GetDataSource(TestDataSourceName)!;
		int elementsToCreateCount = 50;
		
		CreateRandomElements(connection, dataSource, elementsToCreateCount, false, true);
		
		IList<DataStoreElementSort> orderBy = new List<DataStoreElementSort>();
		DataStoreQuery query = new DataStoreQuery(dataSource.Name, null).WithOrderBy(orderBy); // should test empty order by working
		DataStoreResultSet<DataStoreElement> elements = connection.GetElements(query);
		
		
		// get data for varchar
		var elementsAsc = GetElementsOrderBySingle(dataSource, compareConnection, "test1", DataStoreElementSortDirection.Asc);
		var elementAsc = elementsAsc.First();
		
		var elementsDesc = GetElementsOrderBySingle(dataSource, compareConnection, "test1", DataStoreElementSortDirection.Desc);
		elementsDesc.Reverse();
		var elementDesc = elementsDesc.First();
		
		//PrintSortingSets(elementsAsc, elementsDesc, "test1", "varchar");
		// get data for varchar
		
		
		// get data for int
		var elementsIntAsc = GetElementsOrderBySingle(dataSource, compareConnection, "test2", DataStoreElementSortDirection.Asc);
		var elementIntAsc = elementsIntAsc.First();
		
		var elementsIntDesc = GetElementsOrderBySingle(dataSource, compareConnection, "test2", DataStoreElementSortDirection.Desc);
		elementsIntDesc.Reverse();
		var elementIntDesc = elementsIntDesc.First();
		
		//PrintSortingSets(elementsIntAsc, elementsIntDesc, "test2", "int");
		// get data for int
		
		
		// get data for double
		var elementsDoubleAsc = GetElementsOrderBySingle(dataSource, compareConnection, TestTableCols.DoubleCol, DataStoreElementSortDirection.Asc);
		var elementDoubleAsc = elementsDoubleAsc.First();
		
		var elementsDoubleDesc = GetElementsOrderBySingle(dataSource, compareConnection, TestTableCols.DoubleCol, DataStoreElementSortDirection.Desc);
		elementsDoubleDesc.Reverse();
		var elementDoubleDesc = elementsDoubleDesc.First();
		
		//PrintSortingSets(elementsDoubleAsc, elementsDoubleDesc, TestTableCols.DoubleCol, "double");
		// get data for double
		
		
		// get data for date
		var elementsDateAsc = GetElementsOrderBySingle(dataSource, compareConnection, "test3", DataStoreElementSortDirection.Asc);
		var elementDateAsc = elementsDateAsc.First();
		
		var elementsDateDesc = GetElementsOrderBySingle(dataSource, compareConnection, "test3", DataStoreElementSortDirection.Desc);
		elementsDateDesc.Reverse();
		var elementDateDesc = elementsDateDesc.First();
		
		//PrintSortingSets(elementsDateAsc, elementsDateDesc, "test3", "date");
		// get data for date
		
		
		Assert.Equal(elementsToCreateCount, elements.Count);
		Assert.Equal(elementAsc.Id, elementDesc.Id);             // test varchar
		Assert.Equal(elementIntAsc.Id, elementIntDesc.Id);       // test int
		Assert.Equal(elementDoubleAsc.Id, elementDoubleDesc.Id); // test double
		Assert.Equal(elementDateAsc.Id, elementDateDesc.Id);     // test date
	}
	
	/*
	private void PrintSortingSets(DataStoreResultSet<DataStoreElement> elementsAsc, DataStoreResultSet<DataStoreElement> elementsDesc, string columnName,
								  string typeName)
	{
		Console.Out.WriteLine($"elementsAsc.Count: {elementsAsc.Count} - elementsDesc.Count: {elementsDesc.Count} - type: {typeName}");
		Console.Out.WriteLine("------------------------------------------------");
		Console.Out.WriteLine("ASC:");
		foreach (var dataStoreElement in elementsAsc)
		{
			string columnValue = (dataStoreElement.Values.ContainsKey(columnName)) ? dataStoreElement.Values[columnName].ToString() : "";
			Console.Out.WriteLine("ID: " + dataStoreElement.Id + " - Value: " + columnValue);
		}
		Console.Out.WriteLine("------------------------------------------------");
		
		Console.Out.WriteLine("------------------------------------------------");
		Console.Out.WriteLine("DESC:");
		foreach (var dataStoreElement in elementsDesc)
		{
			string columnValue = (dataStoreElement.Values.ContainsKey(columnName)) ? dataStoreElement.Values[columnName].ToString() : "";
			Console.Out.WriteLine("ID: " + dataStoreElement.Id + " - Value: " + columnValue);
		}
		Console.Out.WriteLine("------------------------------------------------");
	}
	*/
	
	/// <summary>
	/// This method will test several column sort features
	/// </summary>
	[Fact(DisplayName = "Several column sort test")]
	public virtual void SeveralColumnSortTest()
	{
		// use 2 different connections to check that changes are persisted 
		var connection = DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		var compareConnection = DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		IDataStoreDataSource dataSource = connection.GetDataSource(TestDataSourceName)!;
		int elementsToCreateCount = 200;
		
		CreateRandomElements(connection, dataSource, elementsToCreateCount, true);
		
		IList<DataStoreElementSort> orderBy = new List<DataStoreElementSort>();
		orderBy.Add(new DataStoreElementSort("test2"));
		orderBy.Add(new DataStoreElementSort("test3", DataStoreElementSortDirection.Desc));
		orderBy.Add(new DataStoreElementSort(TestTableCols.DoubleCol));
		orderBy.Add(new DataStoreElementSort("Id"));
		
		QueryFilterGroup filters = new QueryFilterGroup();
		filters.AddFilter(new NotNullFilter(new QueryFilterField("test2")));
		filters.AddFilter(new NotNullFilter(new QueryFilterField("test3")));
		filters.AddFilter(new NotNullFilter(new QueryFilterField(TestTableCols.DoubleCol)));
		
		DataStoreQuery query = new DataStoreQuery(dataSource.Name, null).WithFilter(filters).WithOrderBy(orderBy);
		var elements = compareConnection.GetElements(query);
		
		query = new DataStoreQuery(dataSource.Name, null);
		var testElements = compareConnection.GetElements(query);
		var elementsToSort = testElements
			.Where(it => ((it.Values["test2"] != null && it.Values["test3"] != null && it.Values[TestTableCols.DoubleCol] != null)))
			.Select(it => new Dictionary<string, object?>
			{
				{ "test2", it.Values["test2"] },
				{ "test3", it.Values["test3"] },
				{ TestTableCols.DoubleCol, it.Values[TestTableCols.DoubleCol] },
				{ "Id", it.Values["Id"] }
			})
			.ToList()
			.OrderBy(it => it["test2"])
			.ThenByDescending(it => it["test3"])
			.ThenBy(it => it[TestTableCols.DoubleCol])
			.ThenBy(it => it["Id"])
			.ToList();
		
		
		orderBy = new List<DataStoreElementSort>();
		orderBy.Add(new DataStoreElementSort("test2", DataStoreElementSortDirection.Desc));
		orderBy.Add(new DataStoreElementSort("test3"));
		orderBy.Add(new DataStoreElementSort(TestTableCols.DoubleCol, DataStoreElementSortDirection.Desc));
		orderBy.Add(new DataStoreElementSort("Id", DataStoreElementSortDirection.Desc));
		
		query = new DataStoreQuery(dataSource.Name, null).WithFilter(filters).WithOrderBy(orderBy);
		var reverseElements = compareConnection.GetElements(query);
		
		query = new DataStoreQuery(dataSource.Name, null);
		var testElementsReverse = compareConnection.GetElements(query);
		var elementsReverseToSort = testElementsReverse
			.Where(it => ((it.Values["test2"] != null && it.Values["test3"] != null && it.Values[TestTableCols.DoubleCol] != null)))
			.Select(it => new Dictionary<string, object?>
			{
				{ "test2", it.Values["test2"] },
				{ "test3", it.Values["test3"] },
				{ TestTableCols.DoubleCol, it.Values[TestTableCols.DoubleCol]!},
				{ "Id", it.Values["Id"] }
			})
			.ToList()
			.OrderByDescending(it => it["test2"])
			.ThenBy(it => it["test3"])
			.ThenByDescending(it => it[TestTableCols.DoubleCol])
			.ThenByDescending(it => it["Id"])
			.ToList();
		
		var elementsList = elements.ToList();
		for (var i = 0; i < elementsList.Count; i++)
		{
			Assert.Equal(elementsList[i].Id, elementsToSort[i]["Id"]);
		}
		
		var elementsReverseList = reverseElements.ToList();
		for (var i = 0; i < elementsReverseList.Count; i++)
		{
			Assert.Equal(elementsReverseList[i].Id, elementsReverseToSort[i]["Id"]);
		}
	}
	
	
	/// <summary>
	/// This method will test aggregates (sum, min, max, avg, count)
	/// </summary>
	[Fact(DisplayName = "Aggregates test")]
	public virtual void AggregatesTest()
	{
		// use 2 different connections to check that changes are persisted 
		var connection = DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		var compareConnection = DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		IDataStoreDataSource dataSource = connection.GetDataSource(TestDataSourceName)!;
		int elementsToCreateCount = 200;
		
		CreateRandomElements(connection, dataSource, elementsToCreateCount, true);
		
		DataStoreQuery query = new DataStoreQuery(dataSource.Name, null);
		var selectedElements = compareConnection.GetElements(query);
		
		double sumDoubleTest = 0;
		double avgIntTest = 0;
		double minDoubleTest = 0;
		double maxDoubleTest = 0;
		int countBoolTest = 0;
		int countAvgNotNull = 0;
		
		foreach (var selectedElement in selectedElements)
		{
			if (selectedElement.Values[TestTableCols.DoubleCol] != null)
			{
				sumDoubleTest += Double.Parse(selectedElement.Values[TestTableCols.DoubleCol]!.ToString()!);
				
				if (Double.Parse(selectedElement.Values[TestTableCols.DoubleCol]!.ToString()!) < minDoubleTest)
					minDoubleTest = Double.Parse(selectedElement.Values[TestTableCols.DoubleCol]!.ToString()!);
				
				if (Double.Parse(selectedElement.Values[TestTableCols.DoubleCol]!.ToString()!) > maxDoubleTest)
					maxDoubleTest = Double.Parse(selectedElement.Values[TestTableCols.DoubleCol]!.ToString()!);
			}
			
			if (selectedElement.Values["intTest2"] != null)
			{
				avgIntTest += int.Parse(selectedElement.Values["intTest2"]!.ToString()!);
				countAvgNotNull++;
			}
			
			if (selectedElement.Values["test4"] != null)
				countBoolTest++;
		}
		
		avgIntTest = avgIntTest / countAvgNotNull;
		
		List<DataStoreQueryField> fields = new List<DataStoreQueryField>();
		DataStoreQueryField sumDoubleField = new DataStoreQueryField($"sum({TestTableCols.DoubleCol})")
		{
			Alias = "SumDouble"
		};
		DataStoreQueryField avgIntField = new DataStoreQueryField("avg(intTest2)")
		{
			Alias = "AvgInt"
		};
		DataStoreQueryField minDoubleField = new DataStoreQueryField($"min({TestTableCols.DoubleCol})")
		{
			Alias = "MinDouble"
		};
		DataStoreQueryField maxDoubleField = new DataStoreQueryField($"max({TestTableCols.DoubleCol})")
		{
			Alias = "MaxDouble"
		};
		DataStoreQueryField countBoolField = new DataStoreQueryField("count(test4)")
		{
			Alias = "CountBool"
		};
		
		fields.Add(sumDoubleField);
		fields.Add(avgIntField);
		fields.Add(minDoubleField);
		fields.Add(maxDoubleField);
		fields.Add(countBoolField);
		
		query = new DataStoreQuery(dataSource.Name, fields);
		var aggregatedElements = compareConnection.GetElements(query);
		
		Assert.True(aggregatedElements.Count == 1);
		
		var element = aggregatedElements.First();
		
		Assert.Equal(sumDoubleTest.ToString(CultureInfo.CurrentCulture), element.Values["SumDouble"]!.ToString());
		Assert.Equal(minDoubleTest.ToString(CultureInfo.CurrentCulture), element.Values["MinDouble"]!.ToString());
		Assert.Equal(maxDoubleTest.ToString(CultureInfo.CurrentCulture), element.Values["MaxDouble"]!.ToString());
		Assert.Equal(countBoolTest.ToString(), element.Values["CountBool"]!.ToString());
		
		Assert.True((avgIntTest - 0.1) <= Double.Parse(element.Values["AvgInt"]!.ToString()!));
		Assert.True(Double.Parse(element.Values["AvgInt"]!.ToString()!) <= (avgIntTest + 0.1));
	}
	
	private IList<DataStoreSuccessInfo> CreateRandomElements(IDataStoreConnection connection, IDataStoreDataSource dataSource, int elementsToCreateCount,
															 bool fixedValues, bool lowerCase = false)
	{
		IList<DataStoreElementData> elementDatas = new List<DataStoreElementData>();
		for (int i = 0; i < elementsToCreateCount; i++)
		{
			Dictionary<string, object?> dict = RandomDataHelper.GetDataSourceData(dataSource, null, 1, fixedValues, lowerCase);
			DataStoreElementData elementData = new DataStoreElementData(dict, new List<string> { "test" });
			elementDatas.Add(elementData);
		}
		
		return connection.CreateElements(dataSource.Name, elementDatas,
										 new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 0, "ARI"));
	}
	
	
	/// <summary>
	/// This method will test group by feature
	/// </summary>
	[Fact(DisplayName = "Group by test")]
	public virtual void GroupByTest()
	{
		// use 2 different connections to check that changes are persisted 
		var connection = DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		var compareConnection = DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		IDataStoreDataSource dataSource = connection.GetDataSource(TestDataSourceName)!;
		int elementsToCreateCount = 200;
		
		CreateRandomElements(connection, dataSource, elementsToCreateCount, true);
		
		List<DataStoreQueryField> fields = new List<DataStoreQueryField>();
		DataStoreQueryField intField = new DataStoreQueryField("SysCreateUser");
		DataStoreQueryField intField2 = new DataStoreQueryField("test2");
		fields.Add(intField);
		fields.Add(intField2);
		
		IList<string> groupByList = new List<string>();
		groupByList.Add("SysCreateUser");
		groupByList.Add("test2"); // int
		DataStoreQuery query = new DataStoreQuery(dataSource.Name, fields).WithGroupBy(groupByList);
		var elementsGroupedByInteger = compareConnection.GetElements(query);
		
		
		fields = new List<DataStoreQueryField>();
		DataStoreQueryField dateField = new DataStoreQueryField("SysCreateUser");
		DataStoreQueryField dateField2 = new DataStoreQueryField("test3");
		fields.Add(dateField);
		fields.Add(dateField2);
		
		groupByList = new List<string>();
		groupByList.Add("SysCreateUser");
		groupByList.Add("test3"); // datetime
		query = new DataStoreQuery(dataSource.Name, fields).WithGroupBy(groupByList);
		var elementsGroupedByDate = compareConnection.GetElements(query);
		
		
		fields = new List<DataStoreQueryField>();
		DataStoreQueryField doubleField = new DataStoreQueryField("SysCreateUser");
		DataStoreQueryField doubleField2 = new DataStoreQueryField(TestTableCols.DoubleCol);
		fields.Add(doubleField);
		fields.Add(doubleField2);
		
		groupByList = new List<string>();
		groupByList.Add("SysCreateUser");
		groupByList.Add(TestTableCols.DoubleCol); // double
		query = new DataStoreQuery(dataSource.Name, fields).WithGroupBy(groupByList);
		var elementsGroupedByDouble = compareConnection.GetElements(query);
		
		Assert.Equal(5, elementsGroupedByInteger.Count);
		Assert.Equal(6, elementsGroupedByDate.Count);
		Assert.Equal(5, elementsGroupedByDouble.Count);
	}
	
	private DataStoreResultSet<DataStoreElement> GetElementsOrderBySingle(IDataStoreDataSource dataSource, IDataStoreConnection connection,
																		  string columnName, DataStoreElementSortDirection sortDirection,
																		  bool checkNotNull = true)
	{
		IList<DataStoreElementSort> orderBy = new List<DataStoreElementSort>();
		orderBy.Add(new DataStoreElementSort(columnName, sortDirection));
		
		// not null filter for field
		QueryFilterGroup filters = new QueryFilterGroup();
		if (checkNotNull)
			filters.AddFilter(new NotNullFilter(new QueryFilterField(columnName)));
		
		DataStoreQuery query = new DataStoreQuery(dataSource.Name, null).WithFilter(filters).WithOrderBy(orderBy);
		var elements = connection.GetElements(query);
		
		return elements;
	}
	
	#endregion
	
	#region filter tests
	
	/// <summary>
	/// Mapping of <see cref="ColumnToValueFilters"/>
	/// </summary>
	/// <returns>the mapped filters in the TestObject</returns>
	public virtual List<TestObject> MappedColumnToValueFilters()
	{
		return MapFilters(ColumnToValueFilters());
	}
	
	/// <summary>
	/// Mapping of <see cref="MvfColumnToValueFilters"/>
	/// </summary>
	/// <returns>the mapped filters in the TestObject</returns>
	public virtual List<TestObject> MappedMvfColumnToValueFilters()
	{
		return MapFilters(MvfColumnToValueFilters(), true);
	}
	
	/// <summary>
	/// Mapping of <see cref="FunnyFieldCombinationFilters"/>
	/// </summary>
	/// <returns>the mapped filters in the TestObject</returns>
	public virtual List<TestObject> MappedFunnyFieldCombinationFilters()
	{
		return MapFilters(FunnyFieldCombinationFilters());
	}
	
	/// <summary>
	/// Mapping of <see cref="ColumnToSubSelectFilters"/>
	/// </summary>
	/// <returns>the mapped filters in the TestObject</returns>
	public virtual List<TestObject> MappedColumnToSubSelectFilters()
	{
		return MapFilters(ColumnToSubSelectFilters());
	}
	
	/// <summary>
	/// Mapping of <see cref="MvfColumnToSubSelectFilters"/>
	/// </summary>
	/// <returns>the mapped filters in the TestObject</returns>
	public virtual List<TestObject> MappedMvfColumnToSubSelectFilters()
	{
		return MapFilters(MvfColumnToSubSelectFilters(), true);
	}
	
	/// <summary>
	/// Mapping of <see cref="ColumnToColumnFilters"/>
	/// </summary>
	/// <returns>the mapped filters in the TestObject</returns>
	public virtual List<TestObject> MappedColumnToColumnFilters()
	{
		return MapFilters(ColumnToColumnFilters());
	}
	
	/// <summary>
	/// Mapping of <see cref="ColumnToMvfColumnFilter"/>
	/// </summary>
	/// <returns>the mapped filters in the TestObject</returns>
	public virtual List<TestObject> MappedColumnToMvfColumnFilter()
	{
		return MapFilters(ColumnToMvfColumnFilter(), true);
	}
	
	/// <summary>
	/// Mapping of <see cref="ValueToMvfColumnFilters"/>
	/// </summary>
	/// <returns>the mapped filters in the TestObject</returns>
	public virtual List<TestObject> MappedValueToMvfColumnFilters()
	{
		return MapFilters(ValueToMvfColumnFilters(), true);
	}
	
	/// <summary>
	/// Mapping of <see cref="ColumnToValueFilters"/>
	/// </summary>
	/// <returns>the mapped filters in the TestObject</returns>
	public virtual List<TestObject> MappedValueToSubSelectFilters()
	{
		return MapFilters(ValueToSubSelectFilters());
	}
	
	/// <summary>
	/// Mapping of <see cref="ColumnToValueFilters"/>
	/// </summary>
	/// <returns>the mapped filters in the TestObject</returns>
	public virtual List<TestObject> MappedValueToColumnFilters()
	{
		return MapFilters(ValueToColumnFilters());
	}
	
	/// <summary>
	/// Map the input parameters into <see cref="TestObject"/>s
	/// </summary>
	/// <param name="dict">A Dictionary consisting of a QueryFilter and an (int[] or type). The value should contain a list of expected result values for this query or an exception type</param>
	/// <param name="containsMultiValueFieldFilters">whether those tests depend on the existence of multi value fields</param>
	/// <returns></returns>
	public static List<TestObject> MapFilters(Dictionary<QueryFilter, object> dict, bool containsMultiValueFieldFilters = false)
	{
		return dict.Select(it =>
		{
			var filterGroup = new QueryFilterGroup();
			filterGroup.AddFilter(it.Key);
			return new TestObject(filterGroup, it.Value, containsMultiValueFieldFilters);
		}).ToList();
	}
	
	/// <summary>
	/// Randomly combines the filters in the methods <see cref="ColumnToValueFilters"/>, <see cref="FunnyFieldCombinationFilters"/> and <see cref="ColumnToColumnFilters"/> to see if they also work as expected
	/// </summary>
	/// <returns></returns>
	public virtual List<TestObject> RandomlyJoinedFilters()
	{
		Random rand = new Random(42);
		var dict = ColumnToValueFilters().ToList();
		dict.AddRange(FunnyFieldCombinationFilters());
		dict.AddRange(ColumnToColumnFilters());
		//dict.AddRange(ValueToColumnFilters());
		
		List<TestObject> queryFilterGroupsDict = new();
		for (int i = 0; i < 60; i++)
		{
			var keyValuePair = GetFilterGroup(rand, dict.Where(it => it.Value.GetType() == typeof(int[]))
												  .Select(it => new KeyValuePair<QueryFilter, int[]>(it.Key, (int[])it.Value))
												  .ToDictionary(), 0);
			queryFilterGroupsDict.Add(new TestObject(keyValuePair.Key, keyValuePair.Value));
		}
		
		return queryFilterGroupsDict;
	}
	
	private static KeyValuePair<QueryFilterGroup, int[]> GetFilterGroup(Random rand, Dictionary<QueryFilter, int[]> dict, int depth)
	{
		var combineLimit = 5;
		
		bool linkAnd = rand.Next(2) == 0;
		
		var filterGroup = new QueryFilterGroup(linkAnd ? QueryFilterLinkType.And : QueryFilterLinkType.Or);
		var res = new List<int>();
		
		var ele = dict.ElementAt(rand.Next(dict.Count));
		filterGroup.AddFilter(ele.Key);
		res.AddRange(ele.Value);
		
		var i = 0;
		while (rand.NextDouble() < 0.6 && i < combineLimit)
		{
			ele = dict.ElementAt(rand.Next(dict.Count));
			filterGroup.AddFilter(ele.Key);
			if (!linkAnd)
			{
				res.AddRange(ele.Value);
			}
			else
			{
				res = res.Where(it => ele.Value.Contains(it)).ToList();
			}
			
			i++;
		}
		
		var j = 0;
		while (rand.Next(2) != 0 && j < combineLimit)
		{
			if (depth > combineLimit)
				break;
			var group = GetFilterGroup(rand, dict, depth + 1);
			filterGroup.AddFilterGroup(group.Key);
			var r = (IEnumerable<int>)group.Value;
			if (!linkAnd)
			{
				res.AddRange(r);
			}
			else
			{
				res = res.Where(it => r.Contains(it)).ToList();
			}
			
			j++;
		}
		
		return new KeyValuePair<QueryFilterGroup, int[]>(filterGroup, res.ToHashSet().ToArray());
	}
	
	/// <summary>
	/// Contains filters like column = 'test'
	/// </summary>
	/// <returns></returns>
	public virtual Dictionary<QueryFilter, object> ColumnToValueFilters()
	{
		return new Dictionary<QueryFilter, object>
		{
			{ new EqualsFilter(new QueryFilterField(TestTableCols.StringCol1), "lets is a string"), new[] { 2 } },
			{ new GreaterThanEqualsFilter(new QueryFilterField(TestTableCols.IntCol), 123), new[] { 0, 1 } },
			{ new GreaterThanFilter(new QueryFilterField(TestTableCols.IntCol), 0), new[] { 0, 1 } },
			{ new InFilter(new QueryFilterField(TestTableCols.IntCol), new[] { -125, 0, 3, 456 }), new[] { 2, 3, 4, 5 } },
			{ new LessThanEqualsFilter(new QueryFilterField(TestTableCols.IntCol), 0), new[] { 2, 3, 4, 5 } },
			{ new LessThanFilter(new QueryFilterField(TestTableCols.IntCol), 0), new[] { 2, 5 } },
			{ new LikeFilter(new QueryFilterField(TestTableCols.StringCol1), "%is%"), new[] { 0, 1, 2, 3 } },
			{ new NotEqualsFilter(new QueryFilterField(TestTableCols.StringCol1), "lets is a string"), new[] { 0, 1, 3, 4, 5 } },
			{ new NotInFilter(new QueryFilterField(TestTableCols.IntCol), new[] { 0, 2, 345, -125 }), new[] { 0 } },
			{ new NotLikeFilter(new QueryFilterField(TestTableCols.StringCol1), "%this%"), new[] { 2, 4, 5 } },
			{ new NotNullFilter(new QueryFilterField(TestTableCols.StringCol1)), new[] { 0, 1, 2, 3, 5 } },
			{ new IsNullFilter(new QueryFilterField(TestTableCols.StringCol1)), new[] { 4 } },
			{ new GreaterThanEqualsFilter(new QueryFilterField(TestTableCols.DateTimeCol), "2000-01-01"), new[] { 0, 2, 3, 5 } }
		};
	}
	
	/// <summary>
	/// Contains filters like multi value field column = ['0', '1', '2']
	/// </summary>
	/// <returns></returns>
	public virtual Dictionary<QueryFilter, object> MvfColumnToValueFilters()
	{
		return new Dictionary<QueryFilter, object>()
		{
			{ new EqualsFilter(new QueryFilterField(TestTableCols.MultiValueStringCol!), new[] { "is", "1", "a" }), new[] { 1 } },
			{ new EqualsFilter(new QueryFilterField(TestTableCols.MultiValueStringCol!), new string[0]), new[] { 5 } },
			{ new NotEqualsFilter(new QueryFilterField(TestTableCols.MultiValueStringCol!), new[] { "0", "2", "1", "4" }), new[] { 0, 1, 2, 3, 5 } },
			{ new LikeFilter(new QueryFilterField(TestTableCols.MultiValueStringCol!), "thi%"), new[] { 0, 2 } },
			{ new NotLikeFilter(new QueryFilterField(TestTableCols.MultiValueStringCol!), "%s"), new[] { 3, 4, 5 } },
			{ new InFilter(new QueryFilterField(TestTableCols.MultiValueStringCol!), "%s"), typeof(DataStoreQueryException) },
			{ new NotInFilter(new QueryFilterField(TestTableCols.MultiValueStringCol!), "%s"), typeof(DataStoreQueryException) },
			{ new LessThanFilter(new QueryFilterField(TestTableCols.MultiValueStringCol!), "%s"), typeof(DataStoreQueryException) },
		};
	}
	
	/// <summary>
	/// Contains filters like multi value field integer column like '%12%'
	/// </summary>
	/// <returns></returns>
	public virtual Dictionary<QueryFilter, object> FunnyFieldCombinationFilters()
	{
		return new Dictionary<QueryFilter, object>
		{
			{ new GreaterThanEqualsFilter(new QueryFilterField(TestTableCols.IntCol), "123"), new[] { 0, 1 } },
			{ new LikeFilter(new QueryFilterField(TestTableCols.IntCol), "%12%"), new[] { 0, 2, 5 } },
			{ new NotEqualsFilter(new QueryFilterField(TestTableCols.BoolCol), "fALse"), new[] { 0, 2, 5 } },
			{ new EqualsFilter(new QueryFilterField(TestTableCols.BoolCol), "1"), new[] { 0, 2, 5 } },
			{ new NotLikeFilter(new QueryFilterField(TestTableCols.IntCol), "%12%"), new[] { 1, 3, 4 } }
		};
	}
	
	/// <summary>
	/// Contains filters like integer column >= (select AVG(*) from table)
	/// </summary>
	/// <returns></returns>
	public virtual Dictionary<QueryFilter, object> ColumnToSubSelectFilters()
	{
		QueryFilterGroup additionalQueryFilterGroup = new(new List<QueryFilter>()
		{
			new EqualsFilter(new QueryFilterField(TestTableCols.StringCol), 1)
		});
		return new Dictionary<QueryFilter, object>
		{
			{
				new GreaterThanEqualsFilter(new QueryFilterField(TestTableCols.IntCol),
											new QueryFilterField($"AVG({TestTableCols.IntCol})", TestDataSourceName)),
				new[] { 0, 1 }
			},
			{
				new LessThanFilter(new QueryFilterField(TestTableCols.IntCol), new QueryFilterField($"SUM({TestTableCols.IntCol})", TestDataSourceName)),
				new[] { 0, 2, 3, 4, 5 }
			},
			{
				new EqualsFilter(new QueryFilterField(TestTableCols.StringCol), new QueryFilterField($"COUNT({TestTableCols.DateTimeCol})", TestDataSourceName)),
				new[] { 5 }
			},
			
			// IntCol >= (MAX(IntCol) where BoolCol = true)
			{
				new GreaterThanEqualsFilter(new QueryFilterField(TestTableCols.IntCol),
											new QueryFilterField($"MAX({TestTableCols.IntCol})", TestDataSourceName,
																 new QueryFilterGroup(new List<QueryFilter>()
																 {
																	 new EqualsFilter(new QueryFilterField(TestTableCols.BoolCol), true)
																 })
											)),
				new[] { 0, 1 }
			},
			// IntCol < (MIN(IntCol) where BoolCol = false)
			{
				new LessThanFilter(new QueryFilterField(TestTableCols.IntCol),
								   new QueryFilterField($"MIN({TestTableCols.IntCol})", TestDataSourceName,
														new QueryFilterGroup(new List<QueryFilter>()
																				 { new EqualsFilter(new QueryFilterField(TestTableCols.BoolCol), false) })
								   )),
				new[] { 2, 5 }
			},
			{ new ExistsFilter(new QueryFilterField(TestTableCols.BoolCol, TestDataSourceName, additionalQueryFilterGroup)), new[] { 0, 1, 2, 3, 4, 5 } },
			{ new NotExistsFilter(new QueryFilterField(TestTableCols.BoolCol, TestDataSourceName, additionalQueryFilterGroup)), new int[] { } },
			{
				new LessThanFilter(new QueryFilterField(TestTableCols.IntCol), new QueryFilterField(
									   TestTableCols.StringCol, TestDataSourceName, new QueryFilterGroup(new List<QueryFilter>()
									   {
										   new EqualsFilter(new QueryFilterField(TestTableCols.BoolCol), true)
									   }))),
				typeof(DataStoreQueryException)
			},
			{
				new LessThanFilter(new QueryFilterField(TestTableCols.IntCol), new QueryFilterField(
									   TestTableCols.StringCol, TestDataSourceName, new QueryFilterGroup(new List<QueryFilter>()
									   {
										   new EqualsFilter(new QueryFilterField(TestTableCols.StringCol), 1)
									   }))),
				new[] { 2, 3, 4, 5 }
			},
			{
				new NotInFilter(new QueryFilterField(TestTableCols.StringCol), new QueryFilterField(TestTableCols.IntCol, TestDataSourceName)),
				new[] { 1, 2, 3, 4, 5 }
			},
			{
				new InFilter(new QueryFilterField(TestTableCols.StringCol), new QueryFilterField(TestTableCols.IntCol, TestDataSourceName)),
				new[] { 0 }
			}
		};
	}
	
	/// <summary>
	/// Contains filters like multi value field column like (select Count(*) from table where id='stuff')
	/// </summary>
	/// <returns></returns>
	public virtual Dictionary<QueryFilter, object> MvfColumnToSubSelectFilters()
	{
		return new Dictionary<QueryFilter, object>()
		{
			{
				new LikeFilter(new QueryFilterField(TestTableCols.MultiValueStringCol!), new QueryFilterField(
								   TestTableCols.StringCol, TestDataSourceName, new QueryFilterGroup(new List<QueryFilter>()
								   {
									   new EqualsFilter(new QueryFilterField(TestTableCols.StringCol), "0")
								   }))),
				new[] { 4 }
			},
			{
				new NotLikeFilter(new QueryFilterField(TestTableCols.MultiValueStringCol!), new QueryFilterField(
									  TestTableCols.StringCol, TestDataSourceName, new QueryFilterGroup(new List<QueryFilter>()
									  {
										  new EqualsFilter(new QueryFilterField(TestTableCols.StringCol), "1")
									  }))),
				new[] { 0, 2, 3, 5 }
			},
			{
				new GreaterThanFilter(new QueryFilterField($"COUNT({TestTableCols.MultiValueStringCol})"), new QueryFilterField(
										  TestTableCols.StringCol, TestDataSourceName, new QueryFilterGroup(new List<QueryFilter>()
										  {
											  new EqualsFilter(new QueryFilterField(TestTableCols.StringCol), "2")
										  }))),
				new[] { 0, 1, 2, 4 }
			},
		};
	}
	
	/// <summary>
	/// Contains filters like column1 = column2
	/// </summary>
	/// <returns></returns>
	public virtual Dictionary<QueryFilter, object> ColumnToColumnFilters()
	{
		return new Dictionary<QueryFilter, object>
		{
			{
				new EqualsFilter(new QueryFilterField(TestTableCols.StringCol1),
								 new QueryFilterField(TestTableCols.IntCol)),
				typeof(DataStoreQueryException)
			},
			{
				new NotEqualsFilter(new QueryFilterField(TestTableCols.IntCol),
									new QueryFilterField(TestTableCols.StringCol1)),
				typeof(DataStoreQueryException)
			},
			{
				new EqualsFilter(new QueryFilterField(TestTableCols.IntCol), new QueryFilterField(TestTableCols.BoolCol)),
				typeof(DataStoreQueryException)
			},
			{
				new GreaterThanFilter(new QueryFilterField(TestTableCols.BoolCol), new QueryFilterField(TestTableCols.IntCol)),
				typeof(DataStoreQueryException)
			},
			{
				new EqualsFilter(new QueryFilterField(TestTableCols.StringCol),
								 new QueryFilterField(TestTableCols.StringCol1)),
				new int[] { }
			},
			{
				new NotEqualsFilter(new QueryFilterField(TestTableCols.StringCol),
									new QueryFilterField(TestTableCols.StringCol1)),
				new[] { 0, 1, 2, 3, 4, 5 }
			},
			{
				new GreaterThanEqualsFilter(new QueryFilterField(TestTableCols.IntCol2), new QueryFilterField(TestTableCols.IntCol)), new[] { 0, 2, 3, 4 }
			},
			{
				new LessThanFilter(new QueryFilterField(TestTableCols.IntCol2), new QueryFilterField(TestTableCols.IntCol)), new[] { 1, 5 }
			}
		};
	}
	
	/// <summary>
	/// Contains filters like column in multi value field column
	/// </summary>
	/// <returns></returns>
	public virtual Dictionary<QueryFilter, object> ColumnToMvfColumnFilter()
	{
		return new Dictionary<QueryFilter, object>
		{
			{
				new InFilter(new QueryFilterField(TestTableCols.StringCol), new QueryFilterField(TestTableCols.MultiValueStringCol!)), new[] { 1, 4 }
			},
			{
				new NotInFilter(new QueryFilterField(TestTableCols.StringCol), new QueryFilterField(TestTableCols.MultiValueStringCol!)), new[] { 0, 2, 3, 5 }
			},
			{
				new LikeFilter(new QueryFilterField(TestTableCols.StringCol), new QueryFilterField(TestTableCols.MultiValueStringCol!)), new[] { 1, 4 }
			},
			{
				new NotLikeFilter(new QueryFilterField(TestTableCols.StringCol), new QueryFilterField(TestTableCols.MultiValueStringCol!)), new[] { 0, 2, 3, 5 }
			},
			{
				new NotLikeFilter(new QueryFilterField(TestTableCols.IntCol), new QueryFilterField(TestTableCols.MultiValueStringCol!)),
				typeof(DataStoreQueryException)
			},
			{
				new LessThanFilter(new QueryFilterField(TestTableCols.IntCol2), new QueryFilterField($"COUNT({TestTableCols.MultiValueStringCol!})")),
				new[] { 3, 5 }
			},
			{
				new LessThanFilter(new QueryFilterField(TestTableCols.IntCol), new QueryFilterField($"COUNT({TestTableCols.MultiValueStringCol!})")),
				new[] { 2, 3, 4, 5 }
			},
			{
				new EqualsFilter(new QueryFilterField(TestTableCols.MultiValueStringCol!), new QueryFilterField(TestTableCols.MultiValueStringCol!)),
				new[] { 0, 1, 2, 3, 4, 5 }
			}
		};
	}
	
	/// <summary>
	/// Contains filters like 'is' in multi value column
	/// </summary>
	/// <returns></returns>
	public virtual Dictionary<QueryFilter, object> ValueToMvfColumnFilters()
	{
		return new Dictionary<QueryFilter, object>
		{
			{ new EqualsFilter(new[] { "1", "0", "2", "4", }, new QueryFilterField(TestTableCols.MultiValueStringCol!)), new[] { 4 } },
			{ new InFilter("is", new QueryFilterField(TestTableCols.MultiValueStringCol!)), new[] { 0, 1, 2 } },
			{ new NotInFilter("test", new QueryFilterField(TestTableCols.MultiValueStringCol!)), new[] { 1, 3, 4, 5 } },
			{ new NotLikeFilter("a", new QueryFilterField(TestTableCols.MultiValueStringCol!)), new[] { 3, 4, 5 } },
			{ new LikeFilter("a", new QueryFilterField(TestTableCols.MultiValueStringCol!)), new[] { 0, 1, 2 } },
			{ new EqualsFilter("lets is a string", new QueryFilterField(TestTableCols.MultiValueStringCol!)), typeof(DataStoreQueryException) },
			{ new LessThanFilter(5, new QueryFilterField(TestTableCols.MultiValueStringCol!)), typeof(DataStoreQueryException) },
			{ new LessThanEqualsFilter(3, new QueryFilterField($"COUNT({TestTableCols.MultiValueStringCol!})")), new[] { 0, 1, 2, 4 } },
		};
	}
	
	/// <summary>
	/// Contains filters like 5 in (select column from table)
	/// </summary>
	/// <returns></returns>
	public virtual Dictionary<QueryFilter, object> ValueToSubSelectFilters()
	{
		return new Dictionary<QueryFilter, object>()
		{
			{ new InFilter(5, new QueryFilterField(TestTableCols.StringCol, TestDataSourceName)), new[] { 0, 1, 2, 3, 4, 5 } },
			{ new InFilter(-1, new QueryFilterField(TestTableCols.StringCol, TestDataSourceName)), new int[0] },
			{ new NotInFilter(-1, new QueryFilterField(TestTableCols.StringCol, TestDataSourceName)), new[] { 0, 1, 2, 3, 4, 5 } },
			{ new NotInFilter(4, new QueryFilterField(TestTableCols.StringCol, TestDataSourceName)), new int[0] },
			{
				new EqualsFilter("2", new QueryFilterField(TestTableCols.StringCol, TestDataSourceName, new QueryFilterGroup(new List<QueryFilter>()
				{
					new EqualsFilter(new QueryFilterField(TestTableCols.StringCol), "2")
				}))),
				new[] { 0, 1, 2, 3, 4, 5 }
			},
			{
				new EqualsFilter(2, new QueryFilterField($"Count({TestTableCols.IdCol})", TestDataSourceName, new QueryFilterGroup(new List<QueryFilter>()
				{
					new EqualsFilter(new QueryFilterField(TestTableCols.StringCol), "2")
				}))),
				new int[0]
			},
			{
				new EqualsFilter(1, new QueryFilterField($"Count({TestTableCols.IdCol})", TestDataSourceName, new QueryFilterGroup(new List<QueryFilter>()
				{
					new EqualsFilter(new QueryFilterField(TestTableCols.StringCol), "2")
				}))),
				new[] { 0, 1, 2, 3, 4, 5 }
			},
			{
				new LessThanFilter(123, new QueryFilterField(TestTableCols.IntCol, TestDataSourceName, new QueryFilterGroup(new List<QueryFilter>()
				{
					new EqualsFilter(new QueryFilterField(TestTableCols.IntCol), 123)
				}))),
				new int[0]
			},
			{
				new LessThanFilter(123, new QueryFilterField(TestTableCols.IntCol, TestDataSourceName, new QueryFilterGroup(new List<QueryFilter>()
				{
					new EqualsFilter(new QueryFilterField(TestTableCols.StringCol), "1")
				}))),
				new[] { 0, 1, 2, 3, 4, 5 }
			},
			{
				new LessThanFilter(123, new QueryFilterField(TestTableCols.IntCol, TestDataSourceName, new QueryFilterGroup(new List<QueryFilter>()
				{
					new EqualsFilter(new QueryFilterField(TestTableCols.StringCol), 1)
				}))),
				new[] { 0, 1, 2, 3, 4, 5 }
			},
			{
				new EqualsFilter(new[] { "0", "1", "3", "2", "4", "5" }, new QueryFilterField(TestTableCols.StringCol, TestDataSourceName)),
				new[] { 0, 1, 2, 3, 4, 5 }
			},
			{
				new NotEqualsFilter(new[] { "0", "1", "3", "2", "4", "5" }, new QueryFilterField(TestTableCols.StringCol, TestDataSourceName)),
				new int[0]
			},
			{
				new LikeFilter(new[] { "0", "1", "3", "2", "4", "5" }, new QueryFilterField(TestTableCols.StringCol, TestDataSourceName, new QueryFilterGroup(
																								new List<QueryFilter>()
																								{
																									new EqualsFilter(
																										new QueryFilterField(TestTableCols.StringCol), "2")
																								}))),
				new[] { 0, 1, 2, 3, 4, 5 }
			},
			{
				new NotLikeFilter(new[] { "0", "1", "3", "2", "4", "5" }, new QueryFilterField(TestTableCols.StringCol, TestDataSourceName,
																							   new QueryFilterGroup(new List<QueryFilter>()
																							   {
																								   new EqualsFilter(
																									   new QueryFilterField(TestTableCols.StringCol), "2")
																							   }))),
				new int[0]
			}
		};
	}
	
	/// <summary>
	/// Contains filters like 5 = column, similar to <see cref="ColumnToValueFilters"/>
	/// </summary>
	/// <returns></returns>
	public virtual Dictionary<QueryFilter, object> ValueToColumnFilters()
	{
		return new Dictionary<QueryFilter, object>
		{
			{ new LikeFilter("lets is a string", new QueryFilterField(TestTableCols.StringCol1)), new[] { 2 } },
			{ new NotLikeFilter("lets is a string", new QueryFilterField(TestTableCols.StringCol1)), new[] { 0, 1, 3, 4, 5 } },
			{ new InFilter("is", new QueryFilterField(TestTableCols.StringCol)), typeof(DataStoreQueryException) },
			
			{ new EqualsFilter("lets is a string", new QueryFilterField(TestTableCols.StringCol1)), new[] { 2 } },
			{ new LessThanEqualsFilter(123, new QueryFilterField(TestTableCols.IntCol)), new[] { 0, 1 } },
			{ new LessThanFilter(0, new QueryFilterField(TestTableCols.IntCol)), new[] { 0, 1 } },
			{ new GreaterThanFilter(0, new QueryFilterField(TestTableCols.IntCol)), new[] { 2, 5 } },
			{ new GreaterThanEqualsFilter(0, new QueryFilterField(TestTableCols.IntCol)), new[] { 2, 3, 4, 5 } },
			{ new NotEqualsFilter("lets is a string", new QueryFilterField(TestTableCols.StringCol1)), new[] { 0, 1, 3, 4, 5 } },
			{ new LessThanFilter("2000-01-01", new QueryFilterField(TestTableCols.DateTimeCol)), new[] { 0, 2, 3, 5 } }
		};
	}
	
	/// <summary>
	/// this method returns the descriptions and a counter for the tests of <seealso cref="TestFilters"/>
	/// </summary>
	/// <returns>IEnumerable&lt;object[]&gt;{ string description, int counter}</returns>
	public static IEnumerable<object[]> FilterCounter()
	{
		var dataSourceCols = new TestDataSourceCols()
		{
			IdCol = "IdCol", 
			BoolCol = "BoolCol", 
			IntCol = "IntCol", 
			StringCol = "StringCol", 
			StringCol1 = "StringCol1", 
			IntCol2 = "Int_Col2", 
			MultiValueStringCol = "MultiValueStringCol", 
			TranslatableCol = "TranslatableCol", 
			DateTimeCol = "DateTimeCol", 
			DoubleCol = "DoubleCol"
		};
		DataStoreInterfaceTestImpl impl = new(dataSourceCols);
		var overallCount = 0;
		var groupCount = 0;
		foreach (var queryGroupFilterFunc in impl.AllQueryGroupFilters())
		{
			var inGroupCount = 0;
			var groupDescription = $"{groupCount:D2}-{queryGroupFilterFunc.Method.Name}";
			
			foreach (var unused in queryGroupFilterFunc.Invoke())
			{
				yield return new object[] { $"{groupDescription} {inGroupCount:D3}", overallCount };
				inGroupCount++;
				overallCount++;
			}
			
			groupCount++;
		}
	}
	
	private List<TestObject> _filterGroups = new();
	
	/// <summary>
	/// This method compiles a list of all QueryFilterGroups to be used with <see cref="TestFilters"/> and the counter of <see cref="FilterCounter"/>
	/// </summary>
	/// <returns>A list of QueryFilterGroups and the ids of their expected result set.</returns>
	public virtual List<TestObject> GetTestFilterGroups()
	{
		if (_filterGroups.Count == 0)
		{
			foreach (var queryGroupFilterFunc in AllQueryGroupFilters())
			{
				_filterGroups.AddRange(queryGroupFilterFunc.Invoke());
			}
		}
		
		return _filterGroups;
	}
	
	/// <summary>
	/// This method contains a list of references of all functions, that will provide <see cref="QueryFilterGroup"/>s to be used with the <see cref="TestFilters"/> method.
	/// </summary>
	/// <returns>All the functions that return a Dictionary&lt;QueryFilterGroup, int[]&gt; for <see cref="TestFilters"/> method</returns>
	public virtual Func<List<TestObject>>[] AllQueryGroupFilters()
	{
		return new[]
		{
			MappedColumnToValueFilters,
			MappedMvfColumnToValueFilters,
			MappedFunnyFieldCombinationFilters,
			MappedColumnToSubSelectFilters,
			MappedMvfColumnToSubSelectFilters,
			MappedColumnToColumnFilters,
			MappedColumnToMvfColumnFilter,
			MappedValueToColumnFilters,
			MappedValueToMvfColumnFilters,
			MappedValueToSubSelectFilters,
			RandomlyJoinedFilters
		};
	}

	/// <summary>
	/// Tests all the defined Filters in <see cref="AllQueryGroupFilters"/>. The real query is being build within the test
	/// </summary>
	/// <param name="description"></param>
	/// <param name="filterCounter">The index of the filter</param>
	[Theory(DisplayName = "Test different kinds of query filters (selects) ")]
	[MemberData(nameof(FilterCounter))]
	#pragma warning disable xUnit1026
	public virtual void TestFilters(string description, int filterCounter)
		#pragma warning restore xUnit1026
	{
		GetTestFilterGroups();
		
		QueryFilterGroup filterGroup = _filterGroups[filterCounter].QueryFilterGroup;
		object indexes = _filterGroups[filterCounter].ResultSet;
		
		// allow the backend to not support multi value fields
		if (_filterGroups[filterCounter].ContainsMvfFilter && TestTableCols.MultiValueStringCol == null)
		{
			TestOutputHelper.WriteLine($"{nameof(TestTableCols.MultiValueStringCol)} is null, but test should be running against this column. NOT TESTING.");
			return;
		}
		
		TestOutputHelper.WriteLine(filterCounter + " - " + JsonConvert.SerializeObject(filterGroup) + " - " +
								   JsonConvert.SerializeObject(indexes));

		var connection = DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		
		var data = new List<Dictionary<string, object?>>()
		{
			new()
			{
				[TestTableCols.StringCol] = 0,
				[TestTableCols.StringCol1] = "this is a string",
				[TestTableCols.IntCol] = 123,
				[TestTableCols.IntCol2] = 123,
				[TestTableCols.BoolCol] = true,
				[TestTableCols.DateTimeCol] = "2020-01-01T00:00:00.123"
			},
			new()
			{
				[TestTableCols.StringCol] = 1,
				[TestTableCols.StringCol1] = "this is not string",
				[TestTableCols.IntCol] = 345,
				[TestTableCols.IntCol2] = 5,
				[TestTableCols.BoolCol] = false,
				[TestTableCols.DateTimeCol] = "1900-01-01T00:00:00"
			},
			new()
			{
				[TestTableCols.StringCol] = 2,
				[TestTableCols.StringCol1] = "lets is a string",
				[TestTableCols.IntCol] = -125,
				[TestTableCols.IntCol2] = 125,
				[TestTableCols.BoolCol] = true,
				[TestTableCols.DateTimeCol] = "2100-01-01T00:00:00"
			},
			new()
			{
				[TestTableCols.StringCol] = 3,
				[TestTableCols.StringCol1] = "this is a test",
				[TestTableCols.IntCol] = 0,
				[TestTableCols.BoolCol] = false,
				[TestTableCols.DateTimeCol] = "2020-01-05T00:00:00"
			},
			new()
			{
				[TestTableCols.StringCol] = 4,
				[TestTableCols.IntCol2] = 9,
				[TestTableCols.BoolCol] = false
			},
			new()
			{
				[TestTableCols.StringCol] = 5,
				[TestTableCols.StringCol1] = "-125",
				[TestTableCols.IntCol] = -125,
				[TestTableCols.IntCol2] = -126,
				[TestTableCols.BoolCol] = true,
				[TestTableCols.DateTimeCol] = "2100-01-01T00:00:00"
			}
		};

		data[4][TestTableCols.DateTimeCol] = null;
			
		if (TestTableCols.MultiValueStringCol != null)
		{
			data[0][TestTableCols.MultiValueStringCol] = new[] { "this", "is", "a", "test" };
			data[1][TestTableCols.MultiValueStringCol] = new[] { "is", "1", "a" };
			data[2][TestTableCols.MultiValueStringCol] = new[] { "is", "this", "test", "a" };
			data[3][TestTableCols.MultiValueStringCol] = new[] { "false", "true" };
			data[4][TestTableCols.MultiValueStringCol] = new[] { "0", "1", "2", "4" };
			data[5][TestTableCols.MultiValueStringCol] = Array.Empty<string>();
		}
		
		foreach (var dict in data)
		{
			DataStoreElementData dataStoreElementData = new DataStoreElementData(dict, new List<string> { "test" });
			connection.CreateElement(TestDataSourceName, dataStoreElementData, new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 0, "me"));
		}
		
		DataStoreQuery query = new(TestDataSourceName, null);
		
		
		query.WithFilter(filterGroup);
		if (indexes.GetType() == typeof(int[]))
		{
			var res1 = connection.GetElements(query);
			Assert.Equal(((int[])indexes).Order(), res1.Select(it => int.Parse(it.Values[TestTableCols.StringCol] + "")).Order());
		}
		else
		{
			Assert.Throws((Type)indexes, () => connection.GetElements(query));
		}
	}
	
	#endregion
	
	/// <summary>
	/// test filters, sortings joins for translatable fields 
	/// </summary>
	[Fact(DisplayName = "test filters & sortings for translatable fields")]
	public virtual void TranslationsTest()
	{
		var testuserId = Guid.NewGuid();
		if (TestTableCols.TranslatableCol == null)
			return;

		var dataStoreConnectionEn = DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, new DataStoreAuthentication("testuser", testuserId, new List<string>{"testgroup"})
		{
			Language = "en"
		});

		var dataStoreConnectionDe = DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, new DataStoreAuthentication("testuser", testuserId, new List<string>{"testgroup"})
		{
			Language = "de"
		});

		var dataStoreConnectionFr = DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, new DataStoreAuthentication("testuser", testuserId, new List<string>{"testgroup"})
		{
			Language = "fr"
		});

		var dataStoreConnectionIt = DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, new DataStoreAuthentication("testuser", testuserId, new List<string>{"testgroup"})
		{
			Language = "it"
		});

		var dataStoreConnectionNope = DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, new DataStoreAuthentication("testuser", testuserId, new List<string>{"testgroup"})
		{
			Language = "nonexistinglanguage"
		});
		
		// field is readonly and no other fields are set...
		Assert.Throws<DataStoreOperationException>(() => dataStoreConnectionEn.CreateElement(TestDataSourceName, new DataStoreElementData(
																								 new Dictionary<string, object?>()
																								 {
																									 {
																										 TestTableCols.TranslatableCol, "its a trap!!!"
																									 }
																								 }, new List<string>{"testgroup"}),
																							 new DataStoreOperationOrigin(
																								 DataStoreOperationOriginType.User, 1, "testuser")));
		
		dataStoreConnectionEn.CreateElement(TestDataSourceName, new DataStoreElementData(new Dictionary<string, object?>()
		{
			{
				TestTableCols.StringCol, "1"
			},
			{
				$"{TestTableCols.TranslatableCol}__de", "es ist eine falle!!!"
			},
			{
				$"{TestTableCols.TranslatableCol}__en", "its a trap!!!"
			},
			{
				$"{TestTableCols.TranslatableCol}__it", ""
			},
		}, new List<string>{"testgroup"}), new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "testuser"));

		// test select columns
		var eles = dataStoreConnectionEn.GetElements(new StorageQuery(TestDataSourceName, null, null));
		Assert.Equal("its a trap!!!", eles.First().Values[TestTableCols.TranslatableCol]);
		Assert.Equal("es ist eine falle!!!", eles.First().Values[$"{TestTableCols.TranslatableCol}__de"]);
		
		eles = dataStoreConnectionIt.GetElements(new StorageQuery(TestDataSourceName, null, null));
		// fallback to default language as field is empty
		Assert.Equal("its a trap!!!", eles.First().Values[TestTableCols.TranslatableCol]);
		Assert.Equal("es ist eine falle!!!", eles.First().Values[$"{TestTableCols.TranslatableCol}__de"]);
		Assert.Equal("its a trap!!!", eles.First().Values[$"{TestTableCols.TranslatableCol}__en"]);
		Assert.Null(eles.First().Values[$"{TestTableCols.TranslatableCol}__it"]);
		Assert.Null(eles.First().Values[$"{TestTableCols.TranslatableCol}__fr"]);
		
		eles = dataStoreConnectionFr.GetElements(new StorageQuery(TestDataSourceName, null, null));
		// fallback to default language as field is empty
		Assert.Equal("its a trap!!!", eles.First().Values[TestTableCols.TranslatableCol]);
		Assert.Equal("es ist eine falle!!!", eles.First().Values[$"{TestTableCols.TranslatableCol}__de"]);
		Assert.Equal("its a trap!!!", eles.First().Values[$"{TestTableCols.TranslatableCol}__en"]);
		Assert.Null(eles.First().Values[$"{TestTableCols.TranslatableCol}__it"]);
		Assert.Null(eles.First().Values[$"{TestTableCols.TranslatableCol}__fr"]);
		
		eles = dataStoreConnectionNope.GetElements(new StorageQuery(TestDataSourceName, null, null));
		// fallback to default language as requested language is not supported for this field
		Assert.Equal("its a trap!!!", eles.First().Values[TestTableCols.TranslatableCol]);
		Assert.Equal("es ist eine falle!!!", eles.First().Values[$"{TestTableCols.TranslatableCol}__de"]);
		Assert.Equal("its a trap!!!", eles.First().Values[$"{TestTableCols.TranslatableCol}__en"]);
		Assert.Null(eles.First().Values[$"{TestTableCols.TranslatableCol}__it"]);
		Assert.Null(eles.First().Values[$"{TestTableCols.TranslatableCol}__fr"]);
		
		eles = dataStoreConnectionDe.GetElements(new StorageQuery(TestDataSourceName, null, null));
		Assert.Single(eles);
		Assert.Equal("es ist eine falle!!!", eles.First().Values[TestTableCols.TranslatableCol]);
		
		// test filters
		dataStoreConnectionEn.CreateElement(TestDataSourceName, new DataStoreElementData(new Dictionary<string, object?>()
		{
			{
				TestTableCols.StringCol, "2"
			},
			{
				$"{TestTableCols.TranslatableCol}__de",
				"Zwei Dinge sind unendlich: das Universum und die menschliche Dummheit, und bei dem Universum bin ich mir nicht sicher."
			},
			{
				$"{TestTableCols.TranslatableCol}__en", "Two things are infinite: the universe and human stupidity; and I'm not sure about the universe."
			},
			{
				$"{TestTableCols.TranslatableCol}__it", "Due cose sono infinite: l'universo e la stupidità umana; e non sono sicuro dell'universo."
			},
		}, new List<string>{"testgroup"}), new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "testuser"));

		dataStoreConnectionEn.CreateElement(TestDataSourceName, new DataStoreElementData(new Dictionary<string, object?>()
		{
			{
				TestTableCols.StringCol, "3"
			},
			{
				$"{TestTableCols.TranslatableCol}__de", "Wahnsinn ist, immer wieder das Gleiche zu tun, aber andere Ergebnisse zu erwarten."
			},
			{
				$"{TestTableCols.TranslatableCol}__en", "Insanity is doing the same thing, over and over again, but expecting different results."
			},
			{
				$"{TestTableCols.TranslatableCol}__it", ""
			},
		}, new List<string>{"testgroup"}), new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "testuser"));

		eles = dataStoreConnectionEn.GetElements(new DataStoreQuery(TestDataSourceName, null)
													 .WithFilter(new QueryFilterGroup().AddFilter(
																	 new LikeFilter(new QueryFilterField(TestTableCols.TranslatableCol), "%in%"))));
		Assert.Equal(2, eles.Count);
		var ids = eles.Select(it => (string)it.Values[TestTableCols.StringCol]!).ToArray();
		Assert.Contains("2", ids);
		Assert.Contains("3", ids);
		
		eles = dataStoreConnectionDe.GetElements(new DataStoreQuery(TestDataSourceName, null)
													 .WithFilter(new QueryFilterGroup().AddFilter(
																	 new LikeFilter(new QueryFilterField(TestTableCols.TranslatableCol), "%in%"))));
		Assert.Equal(3, eles.Count);
		ids = eles.Select(it => (string)it.Values[TestTableCols.StringCol]!).ToArray();
		Assert.Contains("1", ids);
		Assert.Contains("2", ids);
		Assert.Contains("3", ids);
		
		// everything else should be identical to english
		eles = dataStoreConnectionIt.GetElements(new DataStoreQuery(TestDataSourceName, null)
													 .WithFilter(new QueryFilterGroup().AddFilter(
																	 new LikeFilter(new QueryFilterField(TestTableCols.TranslatableCol), "%in%"))));
		Assert.Equal(2, eles.Count);
		ids = eles.Select(it => (string)it.Values[TestTableCols.StringCol]!).ToArray();
		Assert.Contains("2", ids);
		Assert.Contains("3", ids);
		
		eles = dataStoreConnectionFr.GetElements(new DataStoreQuery(TestDataSourceName, null)
													 .WithFilter(new QueryFilterGroup().AddFilter(
																	 new LikeFilter(new QueryFilterField(TestTableCols.TranslatableCol), "%in%"))));
		Assert.Equal(2, eles.Count);
		ids = eles.Select(it => (string)it.Values[TestTableCols.StringCol]!).ToArray();
		Assert.Contains("2", ids);
		Assert.Contains("3", ids);
		
		eles = dataStoreConnectionNope.GetElements(new DataStoreQuery(TestDataSourceName, null)
													   .WithFilter(new QueryFilterGroup().AddFilter(
																	   new LikeFilter(new QueryFilterField(TestTableCols.TranslatableCol), "%in%"))));
		Assert.Equal(2, eles.Count);
		ids = eles.Select(it => (string)it.Values[TestTableCols.StringCol]!).ToArray();
		Assert.Contains("2", ids);
		Assert.Contains("3", ids);
		
		// continue with equal
		eles = dataStoreConnectionEn.GetElements(new DataStoreQuery(TestDataSourceName, null)
													 .WithFilter(new QueryFilterGroup().AddFilter(
																	 new EqualsFilter(new QueryFilterField(TestTableCols.TranslatableCol),
																					  "es ist eine falle!!!"))));
		Assert.Empty(eles);
		
		eles = dataStoreConnectionDe.GetElements(new DataStoreQuery(TestDataSourceName, null)
													 .WithFilter(new QueryFilterGroup().AddFilter(
																	 new EqualsFilter(new QueryFilterField(TestTableCols.TranslatableCol),
																					  "es ist eine falle!!!"))));
		Assert.Single(eles);
		ids = eles.Select(it => (string)it.Values[TestTableCols.StringCol]!).ToArray();
		Assert.Contains("1", ids);
		
		// everything else should be identical to english
		eles = dataStoreConnectionIt.GetElements(new DataStoreQuery(TestDataSourceName, null)
													 .WithFilter(new QueryFilterGroup().AddFilter(
																	 new EqualsFilter(new QueryFilterField(TestTableCols.TranslatableCol),
																					  "Insanity is doing the same thing, over and over again, but expecting different results."))));
		ids = eles.Select(it => (string)it.Values[TestTableCols.StringCol]!).ToArray();
		Assert.Contains("3", ids);
		
		eles = dataStoreConnectionFr.GetElements(new DataStoreQuery(TestDataSourceName, null)
													 .WithFilter(new QueryFilterGroup().AddFilter(
																	 new EqualsFilter(new QueryFilterField(TestTableCols.TranslatableCol),
																					  "Insanity is doing the same thing, over and over again, but expecting different results."))));
		Assert.Single(eles);
		ids = eles.Select(it => (string)it.Values[TestTableCols.StringCol]!).ToArray();
		Assert.Contains("3", ids);
		
		eles = dataStoreConnectionNope.GetElements(new DataStoreQuery(TestDataSourceName, null)
													   .WithFilter(new QueryFilterGroup().AddFilter(
																	   new EqualsFilter(new QueryFilterField(TestTableCols.TranslatableCol),
																						"Insanity is doing the same thing, over and over again, but expecting different results."))));
		Assert.Single(eles);
		ids = eles.Select(it => (string)it.Values[TestTableCols.StringCol]!).ToArray();
		Assert.Contains("3", ids);
		
		// test sortings
		eles = dataStoreConnectionDe.GetElements(
			new DataStoreQuery(TestDataSourceName, null).WithOrderBy(new List<DataStoreElementSort>() { new(TestTableCols.TranslatableCol) }));
		Assert.Equal(3, eles.Count);
		ids = eles.Select(it => (string)it.Values[TestTableCols.StringCol]!).ToArray();
		Assert.Equal("1", ids[0]);
		Assert.Equal("3", ids[1]);
		Assert.Equal("2", ids[2]);
		
		eles = dataStoreConnectionIt.GetElements(
			new DataStoreQuery(TestDataSourceName, null).WithOrderBy(new List<DataStoreElementSort>() { new(TestTableCols.TranslatableCol) }));
		Assert.Equal(3, eles.Count);
		ids = eles.Select(it => (string)it.Values[TestTableCols.StringCol]!).ToArray();
		Assert.Equal("2", ids[0]);
		Assert.Equal("3", ids[1]);
		Assert.Equal("1", ids[2]);
		
		eles = dataStoreConnectionEn.GetElements(
			new DataStoreQuery(TestDataSourceName, null).WithOrderBy(new List<DataStoreElementSort>() { new(TestTableCols.TranslatableCol) }));
		Assert.Equal(3, eles.Count);
		ids = eles.Select(it => (string)it.Values[TestTableCols.StringCol]!).ToArray();
		Assert.Equal("3", ids[0]);
		Assert.Equal("1", ids[1]);
		Assert.Equal("2", ids[2]);
		
		// rest same as english
		eles = dataStoreConnectionFr.GetElements(
			new DataStoreQuery(TestDataSourceName, null).WithOrderBy(new List<DataStoreElementSort>() { new(TestTableCols.TranslatableCol) }));
		Assert.Equal(3, eles.Count);
		ids = eles.Select(it => (string)it.Values[TestTableCols.StringCol]!).ToArray();
		Assert.Equal("3", ids[0]);
		Assert.Equal("1", ids[1]);
		Assert.Equal("2", ids[2]);
		
		eles = dataStoreConnectionNope.GetElements(
			new DataStoreQuery(TestDataSourceName, null).WithOrderBy(new List<DataStoreElementSort>() { new(TestTableCols.TranslatableCol) }));
		Assert.Equal(3, eles.Count);
		ids = eles.Select(it => (string)it.Values[TestTableCols.StringCol]!).ToArray();
		Assert.Equal("3", ids[0]);
		Assert.Equal("1", ids[1]);
		Assert.Equal("2", ids[2]);
	}
}

/// <summary>
/// This object contains all the values needed for <see cref="DataStoreInterfaceTest.TestFilters"/>
/// </summary>
public class TestObject
{
	/// <summary>
	/// The filter group to test
	/// </summary>
	public QueryFilterGroup QueryFilterGroup { get; set; }
	
	/// <summary>
	/// The expected result, either in form of an int[] or Type (e.g. typeof(DataStoreQueryException))
	/// </summary>
	public object ResultSet { get; set; }
	
	/// <summary>
	/// whether the query filter group references a multi value field 
	/// </summary>
	public bool ContainsMvfFilter { get; set; }
	
	/// <summary>
	/// <inheritdoc cref="TestObject"/>
	/// </summary>
	/// <param name="queryFilterGroup"></param>
	/// <param name="resultSet">An array with the <see cref="TestDataSourceCols.StringCol"/> entries of the expected results for the query or an exception type e.g. <code>typeof(DataStoreQueryException)</code></param>
	/// <param name="containsMvfFilter"></param>
	public TestObject(QueryFilterGroup queryFilterGroup, object resultSet, bool containsMvfFilter = false)
	{
		QueryFilterGroup = queryFilterGroup;
		ResultSet = resultSet;
		ContainsMvfFilter = containsMvfFilter;
	}
}

internal class DataStoreInterfaceTestImpl : DataStoreInterfaceTest
{
	public DataStoreInterfaceTestImpl(TestDataSourceCols testcols) : base(null!, "", null!, testcols)
	{
	}
}