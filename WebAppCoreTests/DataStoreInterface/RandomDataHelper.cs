using Levelbuild.Core.DataStoreInterface.Dto.Interfaces;
using Levelbuild.Core.DataStoreInterface.Enum;

namespace Levelbuild.Core.WebAppCoreTests.DataStoreInterface;

/// <summary>
/// This class contains helper methods for creating randomized test data
/// </summary>
public static class RandomDataHelper
{
	/// <summary>
	/// random seed
	/// </summary>
	public static readonly Random Random = new (42);

	/// <summary>
	/// A method to create a random data entry for the supplied dataSource
	/// </summary>
	/// <param name="dataSource"></param>
	/// <param name="dictionary">An optional dictionary with already existing values, in combination with changePercentage only some of them will get changed</param>
	/// <param name="changePercentage">On average, this is the percentage (0 - 1) of existing values in the dictionary that will be changed</param>
	/// <param name="fixedValues">Delivers values from a fixed set</param>
	/// <param name="lowerCase">Should the method return only lowercase strings</param>
	/// <returns></returns>
	public static Dictionary<string, object?> GetDataSourceData(IDataStoreDataSource dataSource,
																Dictionary<string, object?>? dictionary = null, double changePercentage = 1,
																bool fixedValues = false, bool lowerCase = false)
	{
		var fieldNames = dataSource.Fields.Select(it => it.Name).ToList();
		Dictionary<string, object?> dict = dictionary != null ? dictionary.Where(it => !fieldNames.Contains(it.Key)).ToDictionary() : new();
		
		foreach (var field in dataSource.Fields)
		{
			if (dictionary != null && dictionary.ContainsKey(field.Name) && Random.NextDouble() < changePercentage)
			{
				dict[field.Name] = dictionary[field.Name];
				continue;
			}
			
			if (field.Nullable && Random.Next(3) == 0)
			{
				if (!field.Readonly)
					dict[field.Name] = null;
				continue;
			}

			if (field.PrimaryKey)
				continue;

			if (field.Readonly)
				continue;

			if (!field.MultiValue)
				dict[field.Name] = GetRand(field, fixedValues, lowerCase);
			else
			{
				List<object> list = new List<object>();
				for (int i = 0; i < Random.Next(20); i++)
				{
					list.Add(GetRand(field, fixedValues, lowerCase));
				}

				dict[field.Name] = list;
			}
		}

		return dict;
	}

	/// <summary>
	/// Fill type specific field with random data
	/// </summary>
	/// <param name="field"></param>
	/// <param name="fixedValues"></param>
	/// <param name="lowerCase"></param>
	/// <returns></returns>
	/// <exception cref="Exception"></exception>
	private static object GetRand(IDataStoreField field, bool fixedValues, bool lowerCase = false)
	{
		if (field.Type == DataStoreFieldType.String)
		{
			if (fixedValues)
			{
				string[] values = new[] { "asdfjklö", "!#+*~|%$§!", "äöüÄÖÜß", "1234567890", "áàâêéèûîÁÀÂ" };
				var position = Random.Next(0, 5);
				return values[position];
			}

			var len = Random.Next(5, field.Length);
			return (lowerCase) ? Random.NextString(len).ToLower() : Random.NextString(len);
		}
		else if (field.Type == DataStoreFieldType.Boolean)
		{
			return Random.Next(2) == 0;
		}
		else if (field.Type == DataStoreFieldType.Text)
		{
			if (fixedValues)
			{
				string[] values = new[] { "asdfjklö", "!#+*~|%$§!", "äöüÄÖÜß", "1234567890", "áàâêéèûîÁÀÂ" };
				var position = Random.Next(0, 5);
				return values[position];
			}

			var len = Random.Next(2000);
			return Random.NextString(len);
		}
		else if (field.Type == DataStoreFieldType.Integer)
		{
			if (fixedValues)
			{
				int[] values = new[] { -1000, 0, 27, 1000, 9999999 };
				var position = Random.Next(0, 5);
				return values[position];
			}

			return Random.Next(int.MinValue, int.MaxValue);
		}
		else if (field.Type == DataStoreFieldType.Double)
		{
			if (fixedValues)
			{
				double[] values = new[] { -1000.000000, 0.00, 27.98, 100000.0000, 9.999999999999 };
				var position = Random.Next(0, 5);
				return values[position];
			}

			return Random.NextDouble(-9999999, 9999999); // exception by infinity
		}
		else if (field.Type == DataStoreFieldType.Long)
		{
			if (fixedValues)
			{
				long[] values = new[] { -1000000000, 0, 27, 1000000000, 9999999999999 };
				var position = Random.Next(0, 5);
				return values[position];
			}

			return Random.NextInt64(long.MinValue, long.MaxValue);
		}
		else if (field.Type == DataStoreFieldType.Date)
		{
			if (fixedValues)
			{
				DateTime[] values =
				[
					DateTime.Parse("1970-01-01"),
					DateTime.Parse("1999-12-31"),
					DateTime.Parse("2000-01-01"),
					DateTime.UtcNow,
					DateTime.Parse("2432-06-21")
				];
				var position = Random.Next(0, 5);
				return values[position];
			}

			return Random.NextDay();
		}
		else if (field.Type == DataStoreFieldType.DateTime)
		{
			if (fixedValues)
			{
				DateTime[] values =
				[
					DateTime.Parse("2023-05-01 00:00:00+02"),
					DateTime.Parse("2023-05-01 09:30:01+02"),
					DateTime.Parse("2023-05-01 12:00:37+02"),
					DateTime.Parse("2023-05-01 20:15:01+02"),
					DateTime.Parse("2023-05-01 23:59:59+02")
				];
				var position = Random.Next(0, 5);
				return values[position];
			}

			return DateTime.SpecifyKind(Random.NextDay(), DateTimeKind.Utc);
		}

		throw new Exception($"unsupported field type {field.Type}");
	}

	/// <summary>
	/// Similar to NextInt or NextDouble, this method will create a randomized string of the specified length
	/// </summary>
	/// <param name="random"></param>
	/// <param name="length">the expected length of the string</param>
	/// <param name="extended">if a extended character set should be used. If false, the string will only consist of the letters A-Z</param>
	/// <returns></returns>
	public static string NextString(this Random random, int length, bool extended = true)
	{
		string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
		if (extended)
		{
			chars += "äü+;|<>!?45假60-_&%$§#`/\\\"'";
		}

		return new string(Enumerable.Repeat(chars, length)
							  .Select(s => s[random.Next(s.Length)]).ToArray());
	}

	/// <summary>
	/// <inheritdoc cref="Random.NextDouble"/>
	/// </summary>
	/// <param name="random"></param>
	/// <param name="min">the minimum expected double value, inclusive</param>
	/// <param name="max">the maximum expected double value, exclusive</param>
	/// <returns></returns>
	public static double NextDouble(this Random random, double min, double max)
	{
		return min + (random.NextDouble() * (max - min));
	}

	/// <summary>
	/// will create a random DateTime between 1995-01-01 and 2150-01-01
	/// </summary>
	/// <param name="random"></param>
	/// <returns></returns>
	public static DateTime NextDay(this Random random)
	{
		DateTime start = new DateTime(1995, 1, 1);
		DateTime end = new DateTime(2150, 1, 1);
		int range = (end - start).Days;
		return start.AddDays(random.Next(range));
	}
}