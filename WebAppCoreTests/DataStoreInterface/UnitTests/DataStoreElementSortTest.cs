using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Enum;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Core.WebAppCoreTests.DataStoreInterface.UnitTests;

public class DataStoreElementSortTest
{
	[Fact]
	public void EqualsIsTrue()
	{
		var left = new DataStoreElementSort(new("Foo"));
		var right = new DataStoreElementSort(new("Foo"));
		
		Assert.True(left == right);
		Assert.True(left.GetHashCode() == right.GetHashCode());
	}
	
	[Fact]
	public void NotEqualsIsTrue()
	{
		var left = new DataStoreElementSort(new("Foo"));
		var right = new DataStoreElementSort(new("Bar"), DataStoreElementSortDirection.Desc);
		
		Assert.True(left != right);
		Assert.True(left.GetHashCode() != right.GetHashCode());
	}
	
	[Fact]
	public void EqualsIsFalse()
	{
		var left = new DataStoreElementSort(new("Foo"));
		var right = new DataStoreElementSort(new("Bar"), DataStoreElementSortDirection.Desc);
		
		Assert.False(left == right);
		Assert.False(left.GetHashCode() == right.GetHashCode());
	}
	
	[Fact]
	public void NotEqualsIsFalse()
	{
		var left = new DataStoreElementSort(new("Foo"));
		var right = new DataStoreElementSort(new("Foo"));
		
		Assert.False(left != right);
		Assert.False(left.GetHashCode() != right.GetHashCode());
	}
}