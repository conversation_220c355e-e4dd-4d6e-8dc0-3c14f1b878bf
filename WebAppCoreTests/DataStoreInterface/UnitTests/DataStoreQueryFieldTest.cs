using Levelbuild.Core.DataStoreInterface.Dto;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Core.WebAppCoreTests.DataStoreInterface.UnitTests;

public class DataStoreQueryFieldTest
{
	[Fact]
	public void EqualsIsTrue()
	{
		var left = new DataStoreQueryField("Foo", "Bar");
		var right = new DataStoreQueryField("Foo", "Bar");
		
		Assert.True(left == right);
		Assert.True(left.GetHashCode() == right.GetHashCode());
	}
	
	[Fact]
	public void NotEqualsIsTrue()
	{
		var left = new DataStoreQueryField("Foo", "Bar");
		var right = new DataStoreQueryField("Foo");
		
		Assert.True(left != right);
		Assert.True(left.GetHashCode() != right.GetHashCode());
	}
	
	[Fact]
	public void EqualsIsFalse()
	{
		var left = new DataStoreQueryField("Foo", "Bar");
		var right = new DataStoreQueryField("Foo");
		
		Assert.False(left == right);
		Assert.False(left.GetHashCode() == right.GetHashCode());
	}
	
	[Fact]
	public void NotEqualsIsFalse()
	{
		var left = new DataStoreQueryField("Foo", "Bar");
		var right = new DataStoreQueryField("Foo", "Bar");
		
		Assert.False(left != right);
		Assert.False(left.GetHashCode() != right.GetHashCode());
	}
}