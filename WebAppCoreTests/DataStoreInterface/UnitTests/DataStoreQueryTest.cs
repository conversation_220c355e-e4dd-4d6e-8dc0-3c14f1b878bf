using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.DataStoreInterface.Filter;
using Levelbuild.Core.DataStoreInterface.Filter.FilterTypes;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Core.WebAppCoreTests.DataStoreInterface.UnitTests;

public class DataStoreQueryTest
{
	#region Init

	private DataStoreQuery InitFirstQuery()
	{
		var query = new DataStoreQuery("Foo", new List<DataStoreQueryField> {new ("Id"), new("FirstName"), new("LastName"), new("Age")});

		var sortings = new List<DataStoreElementSort>
		{
			new("Age")
		};

		var filter = new QueryFilterGroup()
			.AddFilter(
				new GreaterThanFilter(new("Age"), 5)
			);
		
		return query
			.WithOrderBy(sortings)
			.WithFilter(filter)
			.WithGroupBy(new List<string> {"Age"})
			.WithPaging(100);
	}

	private DataStoreQuery InitSecondQuery()
	{
		var query = new DataStoreQuery("Bar", new List<DataStoreQueryField> {new ("Id"), new("FirstName"), new("LastName"), new("Age")});

		var sortings = new List<DataStoreElementSort>
		{
			new("Age", DataStoreElementSortDirection.Desc)
		};

		var filter = new QueryFilterGroup();
		var firstChildGroup = new QueryFilterGroup();
		firstChildGroup.AddFilter(new LessThanFilter(new("Age"), 15));
		firstChildGroup.AddFilter(new NotEqualsFilter(new("FirstName"), "John"));
		
		var secondChildGroup = new QueryFilterGroup();
		secondChildGroup.AddFilter(new GreaterThanFilter(new("Age"), 5));
		secondChildGroup.AddFilter(new EqualsFilter(new("LastName"), "Doe"));

		filter.AddFilterGroup(firstChildGroup);
		filter.AddFilterGroup(secondChildGroup);
		
		return query
			.WithOrderBy(sortings)
			.WithFilter(filter)
			.WithGroupBy(new List<string> {"LastName"})
			.WithPaging(100, 100);
	}

	#endregion

	#region Tests

	[Fact]
	public void EqualsIsTrue()
	{
		var left = InitFirstQuery();
		var right = InitFirstQuery();

		Assert.True(left == right);
		Assert.True(left.GetHashCode() == right.GetHashCode());
	}

	[Fact]
	public void NotEqualsIsTrue()
	{
		var left = InitFirstQuery();
		var right = InitSecondQuery();

		Assert.True(left != right);
		Assert.True(left.GetHashCode() != right.GetHashCode());
	}

	[Fact]
	public void EqualsIsFalse()
	{
		var left = InitFirstQuery();
		var right = InitSecondQuery();

		Assert.False(left == right);
		Assert.False(left.GetHashCode() == right.GetHashCode());
	}

	[Fact]
	public void NotEqualsIsFalse()
	{
		var left = InitFirstQuery();
		var right = InitFirstQuery();

		Assert.False(left != right);
		Assert.False(left.GetHashCode() != right.GetHashCode());
	}
	
	#endregion
}