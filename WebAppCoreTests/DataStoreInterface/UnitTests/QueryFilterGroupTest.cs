using Levelbuild.Core.DataStoreInterface.Filter;
using Levelbuild.Core.DataStoreInterface.Filter.FilterTypes;

namespace Levelbuild.Core.WebAppCoreTests.DataStoreInterface.UnitTests;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public class QueryFilterGroupTest
{
	#region Tests

	#region ToString
	
	[Fact]
	public void SingleFilter()
	{
		var filterGroup = new QueryFilterGroup();
		filterGroup.AddFilter(new EqualsFilter(new("Foo"), "Bar"));

		var expected = @"Foo = ""Bar""";
		Assert.Equal(expected, filterGroup.ToString());
	}
	
	[Fact]
	public void MultiFilterAnd()
	{
		var filterGroup = new QueryFilterGroup();
		filterGroup.AddFilter(new EqualsFilter(new("Foo"), "Bar"));
		filterGroup.AddFilter(new EqualsFilter("Foo", new("Bar")));

		var expected = @"Foo = ""Bar"" AND ""Foo"" = Bar";
		Assert.Equal(expected, filterGroup.ToString());
	}
	
	[Fact]
	public void MultiFilterOr()
	{
		var filterGroup = new QueryFilterGroup
		{
			LinkType = QueryFilterLinkType.Or
		};
		filterGroup.AddFilter(new EqualsFilter(new("Foo"), "Bar"));
		filterGroup.AddFilter(new EqualsFilter("Foo", new("Bar")));

		var expected = @"Foo = ""Bar"" OR ""Foo"" = Bar";
		Assert.Equal(expected, filterGroup.ToString());
	}
	
	[Fact]
	public void SingleGroup()
	{
		var filterGroup = new QueryFilterGroup();
		
		var childGroup = new QueryFilterGroup();
		childGroup.AddFilter(new EqualsFilter(new("Foo"), "Bar"));
		childGroup.AddFilter(new EqualsFilter(13, new("Bar")));
		filterGroup.AddFilterGroup(childGroup);
		
		var expected = @"(Foo = ""Bar"" AND 13 = Bar)";
		Assert.Equal(expected, filterGroup.ToString());
	}
	
	[Fact]
	public void MultiGroupAnd()
	{
		var filterGroup = new QueryFilterGroup();
		
		var firstChildGroup = new QueryFilterGroup();
		firstChildGroup.AddFilter(new EqualsFilter(new("Foo"), "Bar"));
		firstChildGroup.AddFilter(new EqualsFilter(13, new("Bar")));
		filterGroup.AddFilterGroup(firstChildGroup);

		var secondChildGroup = new QueryFilterGroup()
		{
			LinkType = QueryFilterLinkType.Or
		};
		secondChildGroup.AddFilter(new EqualsFilter(new("Foo"), 5));
		secondChildGroup.AddFilter(new EqualsFilter("Foo", new("Bar")));
		filterGroup.AddFilterGroup(secondChildGroup);

		var expected = @"(Foo = ""Bar"" AND 13 = Bar) AND (Foo = 5 OR ""Foo"" = Bar)";
		Assert.Equal(expected, filterGroup.ToString());
	}
	
	[Fact]
	public void MultiGroupOr()
	{
		var filterGroup = new QueryFilterGroup
		{
			LinkType = QueryFilterLinkType.Or
		};
		var firstChildGroup = new QueryFilterGroup();
		firstChildGroup.AddFilter(new EqualsFilter(new("Foo"), "Bar"));
		firstChildGroup.AddFilter(new EqualsFilter(13, new("Bar")));
		filterGroup.AddFilterGroup(firstChildGroup);

		var secondChildGroup = new QueryFilterGroup()
		{
			LinkType = QueryFilterLinkType.Or
		};
		secondChildGroup.AddFilter(new EqualsFilter(new("Foo"), 5));
		secondChildGroup.AddFilter(new EqualsFilter("Foo", new("Bar")));
		filterGroup.AddFilterGroup(secondChildGroup);

		var expected = @"(Foo = ""Bar"" AND 13 = Bar) OR (Foo = 5 OR ""Foo"" = Bar)";
		Assert.Equal(expected, filterGroup.ToString());
	}
	
	[Fact]
	public void MultiGroupMixed()
	{
		var filterGroup = FillMixedGroup();

		var expected = @"(Foo = Bar) OR ((Foo = ""Bar"" AND 13 = Bar) AND (Foo = 5 OR ""Foo"" = Bar)) OR ((Foo IS NULL AND 13 > Bar) AND (Foo IS NOT NULL OR ""Foo"" != Bar))";
		Assert.Equal(expected, filterGroup.ToString());
	}
	
	#endregion

	#region Comparison

	[Fact]
	public void CompareEqualsIsTrue()
	{
		var left = FillMixedGroup();
		var right = FillMixedGroup();
		
		Assert.True(left == right);
		Assert.True(left.GetHashCode() == right.GetHashCode());
	}
	
	[Fact]
	public void CompareNotEqualsIsTrue()
	{
		var left = FillMixedGroup();
		var right = FillOtherMixedGroup();
		
		Assert.True(left != right);
		Assert.True(left.GetHashCode() != right.GetHashCode());
	}
	
	[Fact]
	public void CompareEqualsIsFalse()
	{
		var left = FillMixedGroup();
		var right = FillOtherMixedGroup();
		
		Assert.False(left == right);
		Assert.False(left.GetHashCode() == right.GetHashCode());
	}
	
	[Fact]
	public void CompareNotEqualsIsFalse()
	{
		var left = FillMixedGroup();
		var right = FillMixedGroup();
		
		Assert.False(left != right);
		Assert.False(left.GetHashCode() != right.GetHashCode());
	}

	#endregion
	
	#endregion

	private QueryFilterGroup FillMixedGroup()
	{
		var filterGroup = new QueryFilterGroup
		{
			LinkType = QueryFilterLinkType.Or
		};
		
		filterGroup.AddFilter(new EqualsFilter(new("Foo"), new("Bar")));
		
		var firstSubGroup = new QueryFilterGroup();
		var firstChildGroup = new QueryFilterGroup();
		firstChildGroup.AddFilter(new EqualsFilter(new("Foo"), "Bar"));
		firstChildGroup.AddFilter(new EqualsFilter(13, new("Bar")));
		firstSubGroup.AddFilterGroup(firstChildGroup);

		var secondChildGroup = new QueryFilterGroup()
		{
			LinkType = QueryFilterLinkType.Or
		};
		secondChildGroup.AddFilter(new EqualsFilter(new("Foo"), 5));
		secondChildGroup.AddFilter(new EqualsFilter("Foo", new("Bar")));
		firstSubGroup.AddFilterGroup(secondChildGroup);
		
		filterGroup.AddFilterGroup(firstSubGroup);
		
		var secondSubGroup = new QueryFilterGroup();
		var thirdChildGroup = new QueryFilterGroup();
		thirdChildGroup.AddFilter(new IsNullFilter(new("Foo")));
		thirdChildGroup.AddFilter(new GreaterThanFilter(13, new("Bar")));
		secondSubGroup.AddFilterGroup(thirdChildGroup);

		var fourthChildGroup = new QueryFilterGroup()
		{
			LinkType = QueryFilterLinkType.Or
		};
		fourthChildGroup.AddFilter(new NotNullFilter(new("Foo")));
		fourthChildGroup.AddFilter(new NotEqualsFilter("Foo", new("Bar")));
		secondSubGroup.AddFilterGroup(fourthChildGroup);
		
		filterGroup.AddFilterGroup(secondSubGroup);

		return filterGroup;
	}
	
	private QueryFilterGroup FillOtherMixedGroup()
	{
		var filterGroup = new QueryFilterGroup
		{
			LinkType = QueryFilterLinkType.Or
		};
		
		filterGroup.AddFilter(new EqualsFilter(new("Foo"), new("Bar")));
		
		var firstSubGroup = new QueryFilterGroup();
		var firstChildGroup = new QueryFilterGroup();
		firstChildGroup.AddFilter(new NotEqualsFilter(new("Foo"), "Bar"));
		firstChildGroup.AddFilter(new LessThanFilter(13, new("Bar")));
		firstSubGroup.AddFilterGroup(firstChildGroup);

		var secondChildGroup = new QueryFilterGroup()
		{
			LinkType = QueryFilterLinkType.Or
		};
		secondChildGroup.AddFilter(new GreaterThanFilter(new("Foo"), 5));
		secondChildGroup.AddFilter(new EqualsFilter("Foo", new("Bar")));
		firstSubGroup.AddFilterGroup(secondChildGroup);
		
		filterGroup.AddFilterGroup(firstSubGroup);
		
		var secondSubGroup = new QueryFilterGroup();
		var thirdChildGroup = new QueryFilterGroup();
		thirdChildGroup.AddFilter(new IsNullFilter(new("Foo")));
		thirdChildGroup.AddFilter(new GreaterThanEqualsFilter(13, new("Bar")));
		secondSubGroup.AddFilterGroup(thirdChildGroup);

		var fourthChildGroup = new QueryFilterGroup()
		{
			LinkType = QueryFilterLinkType.Or
		};
		fourthChildGroup.AddFilter(new NotNullFilter(new("Foo")));
		fourthChildGroup.AddFilter(new NotEqualsFilter("Foo", new("Bar")));
		secondSubGroup.AddFilterGroup(fourthChildGroup);
		
		filterGroup.AddFilterGroup(secondSubGroup);

		return filterGroup;
	}
}