using Levelbuild.Core.DataStoreInterface.Filter;
using Levelbuild.Core.DataStoreInterface.Filter.FilterTypes;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Core.WebAppCoreTests.DataStoreInterface.UnitTests;

public class QueryFilterTest
{
	#region Tests

	#region ToString

	#region Equals

	[Fact]
	public void FieldEqualsString()
	{
		var filter = new EqualsFilter(new("Foo"), "Bar");

		var expected = @"Foo = ""Bar""";
		Assert.Equal(expected, filter.ToString());
	}
	
	[Fact]
	public void FieldEqualsNumber()
	{
		var filter = new EqualsFilter(new("Foo"), 5);

		var expected = "Foo = 5";
		Assert.Equal(expected, filter.ToString());
	}
	
	[Fact]
	public void FieldEqualsField()
	{
		var filter = new EqualsFilter(new("Foo"), new("Bar"));

		var expected = "Foo = Bar";
		Assert.Equal(expected, filter.ToString());
	}
	
	[Fact]
	public void StringEqualsField()
	{
		var filter = new EqualsFilter("Foo", new("Bar"));

		var expected = @"""Foo"" = Bar";
		Assert.Equal(expected, filter.ToString());
	}
	
	[Fact]
	public void NumberEqualsField()
	{
		var filter = new EqualsFilter(5, new("Bar"));

		var expected = "5 = Bar";
		Assert.Equal(expected, filter.ToString());
	}

	#endregion

	#region NotEquals

	[Fact]
	public void FieldNotEqualsString()
	{
		var filter = new NotEqualsFilter(new("Foo"), "Bar");

		var expected = @"Foo != ""Bar""";
		Assert.Equal(expected, filter.ToString());
	}
	
	[Fact]
	public void FieldNotEqualsNumber()
	{
		var filter = new NotEqualsFilter(new("Foo"), 5);

		var expected = "Foo != 5";
		Assert.Equal(expected, filter.ToString());
	}
	
	[Fact]
	public void FieldNotEqualsField()
	{
		var filter = new NotEqualsFilter(new("Foo"), new("Bar"));

		var expected = "Foo != Bar";
		Assert.Equal(expected, filter.ToString());
	}
	
	[Fact]
	public void StringNotEqualsField()
	{
		var filter = new NotEqualsFilter("Foo", new("Bar"));

		var expected = @"""Foo"" != Bar";
		Assert.Equal(expected, filter.ToString());
	}
	
	[Fact]
	public void NumberNotEqualsField()
	{
		var filter = new NotEqualsFilter(5, new("Bar"));

		var expected = "5 != Bar";
		Assert.Equal(expected, filter.ToString());
	}

	#endregion

	#region GreaterThan
	
	[Fact]
	public void FieldGreaterThanNumber()
	{
		var filter = new GreaterThanFilter(new("Foo"), 5);

		var expected = "Foo > 5";
		Assert.Equal(expected, filter.ToString());
	}
	
	[Fact]
	public void FieldGreaterThanField()
	{
		var filter = new GreaterThanFilter(new("Foo"), new("Bar"));

		var expected = "Foo > Bar";
		Assert.Equal(expected, filter.ToString());
	}
	
	[Fact]
	public void NumberGreaterThanField()
	{
		var filter = new GreaterThanFilter(5, new("Bar"));

		var expected = "5 > Bar";
		Assert.Equal(expected, filter.ToString());
	}

	#endregion

	#region GreaterThanEquals

	[Fact]
	public void FieldGreaterThanEqualsNumber()
	{
		var filter = new GreaterThanEqualsFilter(new("Foo"), 5);

		var expected = "Foo >= 5";
		Assert.Equal(expected, filter.ToString());
	}
	
	[Fact]
	public void FieldGreaterThanEqualsField()
	{
		var filter = new GreaterThanEqualsFilter(new("Foo"), new("Bar"));

		var expected = "Foo >= Bar";
		Assert.Equal(expected, filter.ToString());
	}
	
	[Fact]
	public void NumberGreaterThanEqualsField()
	{
		var filter = new GreaterThanEqualsFilter(5, new("Bar"));

		var expected = "5 >= Bar";
		Assert.Equal(expected, filter.ToString());
	}

	#endregion

	#region LessThan

	[Fact]
	public void FieldLessThanNumber()
	{
		var filter = new LessThanFilter(new("Foo"), 5);

		var expected = "Foo < 5";
		Assert.Equal(expected, filter.ToString());
	}
	
	[Fact]
	public void FieldLessThanField()
	{
		var filter = new LessThanFilter(new("Foo"), new("Bar"));

		var expected = "Foo < Bar";
		Assert.Equal(expected, filter.ToString());
	}
	
	[Fact]
	public void NumberLessThanField()
	{
		var filter = new LessThanFilter(5, new("Bar"));

		var expected = "5 < Bar";
		Assert.Equal(expected, filter.ToString());
	}

	#endregion

	#region LessThanEquals

	[Fact]
	public void FieldLessThanEqualsNumber()
	{
		var filter = new LessThanEqualsFilter(new("Foo"), 5);

		var expected = "Foo <= 5";
		Assert.Equal(expected, filter.ToString());
	}
	
	[Fact]
	public void FieldLessThanEqualsField()
	{
		var filter = new LessThanEqualsFilter(new("Foo"), new("Bar"));

		var expected = "Foo <= Bar";
		Assert.Equal(expected, filter.ToString());
	}
	
	[Fact]
	public void NumberLessThanEqualsField()
	{
		var filter = new LessThanEqualsFilter(5, new("Bar"));

		var expected = "5 <= Bar";
		Assert.Equal(expected, filter.ToString());
	}

	#endregion

	#region In

	[Fact]
	public void FieldInList()
	{
		var list = new List<string>()
		{
			"Foo",
			"Bar"
		};
		var filter = new InFilter(new("Foo"), list);

		var expected = @"Foo IN [""Foo"", ""Bar""]";
		Assert.Equal(expected, filter.ToString());
	}
	
	[Fact]
	public void FieldInField()
	{
		var filter = new InFilter(new QueryFilterField("Foo"), new QueryFilterField("Bar"));

		var expected = "Foo IN Bar";
		Assert.Equal(expected, filter.ToString());
	}
	
	[Fact]
	public void ListInField()
	{
		var list = new List<string>()
		{
			"Foo",
			"Bar"
		};
		var filter = new InFilter(list, new("Bar"));

		var expected = @"[""Foo"", ""Bar""] IN Bar";
		Assert.Equal(expected, filter.ToString());
	}

	#endregion
	
	#region NotIn

	[Fact]
	public void FieldNotInList()
	{
		var list = new List<string>()
		{
			"Foo",
			"Bar"
		};
		var filter = new NotInFilter(new("Foo"), list);

		var expected = @"Foo NOT IN [""Foo"", ""Bar""]";
		Assert.Equal(expected, filter.ToString());
	}
	
	[Fact]
	public void FieldNotInField()
	{
		var filter = new NotInFilter(new QueryFilterField("Foo"), new QueryFilterField("Bar"));

		var expected = "Foo NOT IN Bar";
		Assert.Equal(expected, filter.ToString());
	}
	
	[Fact]
	public void ListNotInField()
	{
		var list = new List<string>()
		{
			"Foo",
			"Bar"
		};
		var filter = new NotInFilter(list, new("Bar"));

		var expected = @"[""Foo"", ""Bar""] NOT IN Bar";
		Assert.Equal(expected, filter.ToString());
	}

	#endregion

	#region IsNull

	[Fact]
	public void FieldIsNull()
	{
		var filter = new IsNullFilter(new("Foo"));

		var expected = "Foo IS NULL";
		Assert.Equal(expected, filter.ToString());
	}

	#endregion
	
	#region IsNotNull

	[Fact]
	public void FieldIsNotNull()
	{
		var filter = new NotNullFilter(new("Foo"));

		var expected = "Foo IS NOT NULL";
		Assert.Equal(expected, filter.ToString());
	}

	#endregion

	#region Like

	[Fact]
	public void FieldLikeString()
	{
		var filter = new LikeFilter(new("Foo"), "Bar");

		var expected = @"Foo LIKE ""Bar""";
		Assert.Equal(expected, filter.ToString());
	}
	
	[Fact]
	public void FieldLikeField()
	{
		var filter = new LikeFilter(new("Foo"), new("Bar"));

		var expected = "Foo LIKE Bar";
		Assert.Equal(expected, filter.ToString());
	}
	
	[Fact]
	public void StringLikeField()
	{
		var filter = new LikeFilter("Foo", new("Bar"));

		var expected = @"""Foo"" LIKE Bar";
		Assert.Equal(expected, filter.ToString());
	}

	#endregion
	
	#region NotLike

	[Fact]
	public void FieldNotLikeString()
	{
		var filter = new NotLikeFilter(new("Foo"), "Bar");

		var expected = @"Foo NOT LIKE ""Bar""";
		Assert.Equal(expected, filter.ToString());
	}
	
	[Fact]
	public void FieldNotLikeField()
	{
		var filter = new NotLikeFilter(new("Foo"), new("Bar"));

		var expected = "Foo NOT LIKE Bar";
		Assert.Equal(expected, filter.ToString());
	}
	
	[Fact]
	public void StringNotLikeField()
	{
		var filter = new NotLikeFilter("Foo", new("Bar"));

		var expected = @"""Foo"" NOT LIKE Bar";
		Assert.Equal(expected, filter.ToString());
	}

	#endregion

	#region Exists
	
	[Fact]
	public void Exists()
	{
		var filter = new ExistsFilter(new("Foo")
		{
			DataSourceName = "SubSource"
		});

		var expected = "SubSource.Foo EXISTS";
		Assert.Equal(expected, filter.ToString());
	}
	
	[Fact]
	public void ExistsWhere()
	{
		var subSelectFilters = new QueryFilterGroup();
		subSelectFilters.AddFilter(new EqualsFilter(new("Id"), 5));
		
		var filter = new ExistsFilter(new("Foo")
		{
			DataSourceName = "SubSource",
			DataSourceFilters = subSelectFilters
		});

		var expected = "SubSource.Foo EXISTS WHERE Id = 5";
		Assert.Equal(expected, filter.ToString());
	}

	#endregion

	#region NotExists

	[Fact]
	public void NotExists()
	{
		var filter = new NotExistsFilter(new("Foo")
		{
			DataSourceName = "SubSource"
		});

		var expected = "SubSource.Foo DOES NOT EXIST";
		Assert.Equal(expected, filter.ToString());
	}
	
	[Fact]
	public void NotExistsWhere()
	{
		var subSelectFilters = new QueryFilterGroup();
		subSelectFilters.AddFilter(new EqualsFilter(new("Id"), 5));
		
		var filter = new NotExistsFilter(new("Foo")
		{
			DataSourceName = "SubSource",
			DataSourceFilters = subSelectFilters
		});

		var expected = "SubSource.Foo DOES NOT EXIST WHERE Id = 5";
		Assert.Equal(expected, filter.ToString());
	}

	#endregion

	#region SubSelect

	[Fact]
	public void EqualsSubSelect()
	{
		var filter = new EqualsFilter(new("Foo"), new("Bar")
		{
			DataSourceName = "SubSource"
		});

		var expected = "Foo = SubSource.Bar";
		Assert.Equal(expected, filter.ToString());
	}
	
	[Fact]
	public void EqualsSubSelectWhere()
	{
		var subSelectFilters = new QueryFilterGroup();
		subSelectFilters.AddFilter(new EqualsFilter(new("Id"), 5));
		
		var filter = new NotEqualsFilter(new("Foo"), new("Bar")
		{
			DataSourceName = "SubSource",
			DataSourceFilters = subSelectFilters
		});

		var expected = "Foo != (SubSource.Bar WHERE Id = 5)";
		Assert.Equal(expected, filter.ToString());
	}

	#endregion
	
	#endregion
	
	#region Comparison

	[Fact]
	public void CompareEqualsIsTrue()
	{
		var left = new EqualsFilter(new("Foo"), new("Bar"));
		var right = new EqualsFilter(new("Foo"), new("Bar"));
		
		Assert.True(left == right);
		Assert.True(left.GetHashCode() == right.GetHashCode());
	}
	
	[Fact]
	public void CompareNotEqualsIsTrue()
	{
		var left = new EqualsFilter(new("Foo"), new("Bar"));
		var right = new NotEqualsFilter(new("Foo"), new("Bar"));
		
		Assert.True(left != right);
		Assert.True(left.GetHashCode() != right.GetHashCode());
	}
	
	[Fact]
	public void CompareEqualsIsFalse()
	{
		var left = new EqualsFilter(new("Foo"), new("Bar"));
		var right = new NotEqualsFilter(new("Foo"), new("Bar"));
		
		Assert.False(left == right);
		Assert.False(left.GetHashCode() == right.GetHashCode());
	}
	
	[Fact]
	public void CompareNotEqualsIsFalse()
	{
		var left = new EqualsFilter(new("Foo"), new("Bar"));
		var right = new EqualsFilter(new("Foo"), new("Bar"));
		
		Assert.False(left != right);
		Assert.False(left.GetHashCode() != right.GetHashCode());
	}

	#endregion

	#endregion
}