using Levelbuild.Core.FileInterface;
using DirectoryInfo = Levelbuild.Core.FileInterface.DirectoryInfo;
using FileInfo = Levelbuild.Core.FileInterface.FileInfo;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Core.WebAppCoreTests.FileInterface;

public abstract class FileInterfaceTests
{
	protected FileStore FileStore;
	
	protected FileInterfaceTests(FileStore fileStore)
	{
		this.FileStore = fileStore;
	}
	
	[Trait("Category", "Default Interface Tests")]
	[Fact(DisplayName = "Plain file test")]
	public virtual void BasicFileFunctionsInRootDir()
	{
		var file = FileStore.GetFile("test.txt");
		var file2 = FileStore.GetFile("test2.txt");
		
		CleanupFile(file);
		CleanupFile(file2);
		
		Assert.Equal(".txt", file.GetExtension());
		Assert.Equal("test.txt", file.GetName());
		Assert.EndsWith("test.txt", file.Path);
		Assert.DoesNotContain("test.txt", file.GetPathWithoutFilename());
		Assert.False(file.Exists());
		Assert.Throws<FileNotFoundException>(() => file.GetFileSize());
		Assert.Throws<FileNotFoundException>(() => file.ReadFile());
		Assert.Throws<FileNotFoundException>(() => file.ReadAllBytes());
		Assert.Throws<FileNotFoundException>(() => file.ReadAllLines());
		Assert.Throws<FileNotFoundException>(() => file.MoveTo(file2));
		Assert.Throws<FileNotFoundException>(() => file.CopyTo(file2));
		Assert.Throws<FileNotFoundException>(() => file.DeleteFile());
		
		CleanupFile(file);
		CleanupFile(file2);
	}
	
	[Trait("Category", "Default Interface Tests")]
	[Fact(DisplayName = "Bad file name characters")]
	public virtual void BadFileNameTests()
	{
		Assert.Throws<ArgumentException>(() => FileStore.GetFile("this/is/a<test/filetest.blue"));
		Assert.Throws<ArgumentException>(() => FileStore.GetFile("file\\test.blue"));
		Assert.Throws<ArgumentException>(() => FileStore.GetFile("file<test.blue"));
		Assert.Throws<ArgumentException>(() => FileStore.GetFile("file>test.blue"));
		Assert.Throws<ArgumentException>(() => FileStore.GetFile("file*test.blue"));
		Assert.Throws<ArgumentException>(() => FileStore.GetFile("file?test.blue"));
		Assert.Throws<ArgumentException>(() => FileStore.GetFile("file\"test.blue"));
		Assert.Throws<ArgumentException>(() => FileStore.GetFile("file:test.blue"));
		Assert.Throws<ArgumentException>(() => FileStore.GetFile("file|test.blue"));
		Assert.Throws<ArgumentException>(() => FileStore.GetFile("file\ud83d\ude00test.blue"));
	}
	
	[Trait("Category", "Default Interface Tests")]
	[Fact(DisplayName = "directory test")]
	public virtual void BasicDirectoryTests()
	{
		var dir = FileStore.GetDirectory("some/directory");
		var dir3 = FileStore.GetDirectory("some/dir/directory");
		
		CleanupDir(dir);
		CleanupDir(dir3);
		
		Assert.EndsWith("some/", dir.GetParent().ToString());
		
		var file1 = FileStore.GetFile("some/directory/file1");
		var file2 = FileStore.GetFile("some/directory/file1.exe");
		
		file1.WriteText("");
		file2.WriteText("");
		
		var dirs = dir.ListDirectories();
		Assert.Empty(dirs);
		
		var files = dir.ListFiles();
		Assert.Equal(2, files.Count);
		var fileNames = files.Select(it => it.ToString());
		var collection = fileNames as string[] ?? fileNames.ToArray();
		Assert.NotNull(collection.FirstOrDefault(it => it.EndsWith(file1.ToString())));
		Assert.NotNull(collection.FirstOrDefault(it => it.EndsWith(file2.ToString())));
		
		var file3 = FileStore.GetFile("some/directory/tree/file546.bin");
		var file3Text = "some text to file ßtuff";
		file3.WriteText(file3Text);
		
		var dirs3 = dir.ListDirectories();
		Assert.Single(dirs3);
		var files3 = dir.ListFiles();
		Assert.Equal(2, files3.Count);
		
		var file3Moved = FileStore.GetFile("some/dir/directory/tree/file546.bin");
		file3.MoveTo(file3Moved);
		Assert.Equal(file3Text, string.Join("\n", file3Moved.ReadAllLines()));
		
		CleanupDir(dir);
		CleanupDir(dir3);
	}
	
	[Trait("Category", "Default Interface Tests")]
	[Fact(DisplayName = "write file test")]
	public virtual void WriteFileTests()
	{
		var file = FileStore.GetFile("WriteFileTests.txt");
		var file2 = FileStore.GetFile("lastone.mov");
		var file3 = FileStore.GetFile("some/directory/lastone123.mov");
		var dir = FileStore.GetDirectory("some/directory");
		
		CleanupFile(file);
		CleanupFile(file2);
		CleanupFile(file3);
		CleanupDir(dir);
		
		file.WriteText("this is some text.Ä`");
		file.WriteText("\nother stuff");
		
		var file1 = FileStore.GetFile("WriteFileTests.txt");
		Assert.Equal("\nother stuff", string.Join("\n", file1.ReadAllLines()));
		
		file.WriteText("ne\ud83d\ude00xt ß?");
		Assert.Equal("ne\ud83d\ude00xt ß?", string.Join("\n", file1.ReadAllLines()));
		Assert.Equal("ne\ud83d\ude00xt ß?"u8.ToArray(), file1.ReadAllBytes());
		
		// lets just do some real stuff here
		byte[] bytes = new byte[32 * 1024 * 1024];
		new Random(42).NextBytes(bytes);
		
		file.WriteFile(bytes);
		Assert.Equal(bytes.Length, file.GetFileSize());
		Assert.Equal(bytes, file1.ReadAllBytes());
		using (var stream = file.ReadFile(12, 50))
		{
			Assert.Equal(bytes.Skip(12).Take(50).ToArray(), ReadFully(stream));
		}
		
		byte[] bytes1 = new byte[32 * 1024 * 1024 + 5];
		new Random(43).NextBytes(bytes1);
		file.WriteFile(bytes);
		Assert.Equal(bytes, file1.ReadAllBytes());

		byte[] bytes2;
		using (var fileStream = file1.ReadFile())
		{
			bytes2 = ReadFully(fileStream);
		}
		
		Assert.Equal(bytes, bytes2);
		
		// file2 is a zero bytes file...
		Assert.False(file2.Exists());
		file2.WriteText("");
		Assert.True(file2.Exists());
		file2.WriteText("");
		Assert.True(file2.Exists());
		
		file2.MoveTo(file3);
		Assert.True(file3.Exists());
		Assert.False(file2.Exists());
		
		file3.CopyTo(file2);
		Assert.True(file3.Exists());
		Assert.True(file2.Exists());
		
		CleanupFile(file2);
		CleanupFile(file3);
		CleanupDir(dir);
		
		// and the same with some data inside the file
		Assert.True(file.Exists());
		Assert.False(file2.Exists());
		
		file.MoveTo(file3);
		Assert.True(file3.Exists());
		Assert.False(file.Exists());
		
		file3.CopyTo(file);
		Assert.True(file3.Exists());
		Assert.True(file.Exists());
		Assert.Equal(bytes, file.ReadAllBytes());
		
		CleanupFile(file);
		CleanupFile(file2);
		CleanupFile(file3);
		CleanupDir(dir);
	}

	[Theory]
	[InlineData(0)]
	[InlineData(1)]
	[InlineData(2)]
	[InlineData(3)]
	[InlineData(4)]
	[InlineData(5)]
	[InlineData(6)]
	[InlineData(7)]
	[InlineData(8)]
	public void StreamUploadTest(int size)
	{
		try
		{
			FileStore.GetFile("stream/getfilestreamtest").DeleteFile();
		}
		catch (Exception)
		{
			// ignored
		}
		
		var random = new Random();
		var buffer = new byte[(int)(Math.Pow(10, size))];
		random.NextBytes(buffer);
		var newFile = FileStore.GetFile("stream/getfilestreamtest");
		using (var str = newFile.WriteFile())
		{
			str.Write(buffer);
		}
		
		Assert.Equal(buffer, newFile.ReadAllBytes());
		try
		{
			FileStore.GetFile("stream/getfilestreamtest").DeleteFile();
		}
		catch (Exception)
		{
			// ignored
		}
	}
	
	[Fact]
	public void StreamUpload1Test()
	{
		int size = 8;
		try
		{
			FileStore.GetFile("stream/getfilestreamtest").DeleteFile();
			FileStore.GetFile("stream/getfilestreamtest1").DeleteFile();
		}
		catch (Exception)
		{
			// ignored
		}
		
		var random = new Random();
		var buffer = new byte[(int)(Math.Pow(10, size))];
		random.NextBytes(buffer);
		var baseFile = FileStore.GetFile("stream/getfilestreamtest");
		var newFile = FileStore.GetFile("stream/getfilestreamtest1");
		baseFile.WriteFile(buffer);

		using (var readStream = baseFile.ReadFile())
			using (var writeStream = newFile.WriteFile())
			{
				readStream.CopyTo(writeStream);
			}
		
		Assert.Equal(buffer, newFile.ReadAllBytes());
		Assert.Equal(buffer, baseFile.ReadAllBytes());
		try
		{
			FileStore.GetFile("stream/getfilestreamtest").DeleteFile();
			FileStore.GetFile("stream/getfilestreamtest1").DeleteFile();
		}
		catch (Exception)
		{
			// ignored
		}
	}
	
	protected virtual byte[] ReadFully(Stream input)
	{
		byte[] buffer = new byte[16 * 1024];
		using (MemoryStream ms = new MemoryStream())
		{
			int read;
			while ((read = input.Read(buffer, 0, buffer.Length)) > 0)
			{
				ms.Write(buffer, 0, read);
			}
			
			return ms.ToArray();
		}
	}
	
	protected virtual void CleanupDir(DirectoryInfo fullpath)
	{
		try
		{
			fullpath.Delete();
		}
		catch (Exception)
		{
			// ignored
		}
	}
	
	protected virtual void CleanupFile(FileInfo fullpath)
	{
		try
		{
			fullpath.DeleteFile();
		}
		catch (Exception)
		{
			// ignored
		}
	}
}