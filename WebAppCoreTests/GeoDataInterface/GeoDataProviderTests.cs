using Levelbuild.Core.GeoDataInterface;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Core.WebAppCoreTests.GeoDataInterface;

public abstract class GeoDataProviderTests
{
	protected IGeoDataProvider? Provider;
	
	[Trait("Category", "Default Interface Tests")]
	[Fact(DisplayName = "Provider instantiated properly")]
	public virtual void GetInstanceNotNull()
	{
		Assert.NotNull(Provider);
	}
	
	[Trait("Category", "Default Interface Tests")]
	[Fact(DisplayName = "Location geo data resolved properly")]
	public virtual async Task GetLocationWithCorrectAddress()
	{
		var result  = await Provider!.GetLocation("Petersstr. 15, 04109 Leipzig");

		Assert.NotNull(result);
		Assert.InRange(result.Latitude, 51.33, 51.34);
		Assert.InRange(result.Longitude, 12.37, 12.38);
		
		// Area is allowed to be either null or valid
		if (result.Area == null)
			return;
		
		Assert.InRange(result.Area.LatitudeFrom, 51.3, 51.4);
		Assert.InRange(result.Area.LatitudeTo, 51.3, 51.4);
		Assert.True(result.Area.LatitudeFrom < result.Area.LatitudeTo);
		
		Assert.InRange(result.Area.LongitudeFrom, 12.3, 12.4);
		Assert.InRange(result.Area.LongitudeTo, 12.3, 12.4);
		Assert.True(result.Area.LongitudeFrom < result.Area.LongitudeTo);
	}
	
	[Trait("Category", "Default Interface Tests")]
	[Fact(DisplayName = "Invalid address leads to empty answer")]
	public virtual async Task GetLocationWithInvalidAddress()
	{
		var result  = await Provider!.GetLocation("qwert123");
		Assert.Null(result);
	}
}