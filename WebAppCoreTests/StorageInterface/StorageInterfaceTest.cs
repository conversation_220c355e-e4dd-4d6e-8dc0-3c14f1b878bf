using System.Globalization;
using AngleSharp.Diffing.Extensions;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Dto.Interfaces;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.DataStoreInterface.Filter;
using Levelbuild.Core.DataStoreInterface.Filter.FilterTypes;
using Levelbuild.Core.StorageInterface;
using Levelbuild.Core.StorageInterface.Dto;
using Levelbuild.Core.StorageInterface.Dto.SearchSuggestions;
using Levelbuild.Core.WebAppCoreTests.DataStoreInterface;
using Xunit.Abstractions;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Core.WebAppCoreTests.StorageInterface;

/// <summary>
/// This abstract class implements tests for the <see cref="IStorage"/>. Especially CRUD of DataSources is being tested, but also tests from the <see cref="DataStoreInterfaceTest"/> are being run.
/// <inheritdoc cref="DataStoreInterfaceTest"/>
/// </summary>
public abstract class StorageInterfaceTest : DataStoreInterfaceTest
{
	/// <summary>
	/// The storage instance to test against
	/// </summary>
	protected IStorage Storage;

	/// <summary>
	/// Test instantiation
	/// </summary>
	/// <param name="storage"><inheritdoc cref="Storage"/></param>
	/// <param name="idCol">The name of the primary key column</param>
	/// <param name="testOutputHelper">used for logging</param>
	public StorageInterfaceTest(IStorage storage, String idCol, ITestOutputHelper testOutputHelper)
		: base(storage, "test_" + RandomDataHelper.Random.NextString(20, false), testOutputHelper, new TestDataSourceCols(
				   idCol,
				   "index",
				   "test1",
				   "test2",
				   "intTest2",
				   "double_Test",
				   "test3",
				   "test4",
				   "mvfTestCol",
				   "translatableCol"
			   ))
	{
		Storage = storage;
		var connection = (IStorageConnection)DataStore.GetConnection(storage.GetContexts().First().Identifier, null);
		var dataSource = connection.GetDataSource(TestDataSourceName);
		if (dataSource != null)
			return;

		StorageDataSourceConfig sdc = new(TestDataSourceName)
		{
			StoreRevisions = false
		};
		connection.CreateDataSource(sdc);
		connection.CreateField(TestDataSourceName, new StorageField("index")
		{
			Type = DataStoreFieldType.String,
			Length = 255,
			Nullable = false
		});
		connection.CreateField(TestDataSourceName, new StorageField("test1")
		{
			Length = 255,
			Type = DataStoreFieldType.String,
			Nullable = true
		});

		connection.CreateField(TestDataSourceName, new StorageField("test2")
		{
			Type = DataStoreFieldType.Integer,
			Nullable = true
		});
		connection.CreateField(TestDataSourceName, new StorageField("intTest2")
		{
			Type = DataStoreFieldType.Integer,
			Nullable = true
		});

		connection.CreateField(TestDataSourceName, new StorageField("double_Test")
		{
			Type = DataStoreFieldType.Double,
			Nullable = true
		});

		connection.CreateField(TestDataSourceName, new StorageField("test3")
		{
			Type = DataStoreFieldType.DateTime,
			Nullable = true
		});

		connection.CreateField(TestDataSourceName, new StorageField("test4")
		{
			Type = DataStoreFieldType.Boolean
		});

		connection.CreateField(TestDataSourceName, new StorageField("mvfTestCol")
		{
			Type = DataStoreFieldType.String,
			Length = 255,
			MultiValue = true
		});

		connection.CreateField(TestDataSourceName, new StorageField("translatableCol")
		{
			Type = DataStoreFieldType.String,
			Length = 255,
			Translatable = true,
			Languages = new List<string>() { "en", "de", "fr", "it" }
		});
	}

	/// <summary>
	/// Tests CRUD for DataSources
	/// </summary>
	[Fact(DisplayName = "Create Read Update Delete for DataSources working")]
	public void DataSourceCrudTest()
	{
		// use 2 different connections to check that changes are persisted 
		var connection = (IStorageConnection)DataStore.GetConnection(null);
		var compareConnection = (IStorageConnection)DataStore.GetConnection(null);
		var randomTableName = "test_" + RandomDataHelper.Random.NextString(20, false);

		// test create
		var config = new StorageDataSourceConfig(randomTableName)
		{
			StoreRevisions = false
		};
		var definition = connection.CreateDataSource(config);

		Assert.Equal(randomTableName, definition.Name);
		Assert.Equal(config.StoreRevisions, definition.StoreRevisions);
		var res = compareConnection.GetDataSource(randomTableName);
		Assert.Equivalent(definition, res, true);

		var sfd1 = new StorageField("testcol1")
		{
			Readonly = false,
			Nullable = true,
			Type = DataStoreFieldType.String,
			Unique = false,
			Length = 99,
			Encrypted = false
		};
		var field1 = connection.CreateField(definition.Name, sfd1);
		// all int, string, bool fields are always not nullable
		var sfd1Comp = new StorageField("testcol1")
		{
			Readonly = false,
			Nullable = false,
			DefaultValue = "",
			Type = DataStoreFieldType.String,
			Unique = false,
			Length = 99,
			Encrypted = false
		};
		Assert.Equivalent(sfd1Comp, field1, true);

		var sfd2 = new StorageField("testcol2")
		{
			Readonly = false,
			Nullable = true,
			Type = DataStoreFieldType.Integer,
			Unique = false,
			Encrypted = false
		};
		var field2 = connection.CreateField(definition.Name, sfd2);
		var sfd2Comp = new StorageField("testcol2")
		{
			Readonly = false,
			Nullable = false,
			DefaultValue = 0,
			Type = DataStoreFieldType.Integer,
			Unique = false,
			Encrypted = false
		};
		Assert.Equivalent(sfd2Comp, field2, true);

		Assert.Contains(randomTableName, compareConnection.GetDataSources());
		var dataSource = connection.GetDataSource(randomTableName);
		var def = new StorageDataSource(randomTableName, new List<IDataStoreField>())
		{
			StoreRevisions = false,
			StoragePath = ((StorageDataSource)dataSource!).StoragePath
		};

		if (dataSource.Fields.First() is StorageField)
		{
			foreach (var dataStoreField in dataSource.Fields.Where(it => ((StorageField)it).SystemField))
			{
				def.Fields.Add(dataStoreField);
			}
		}

		def.Fields.Add(sfd1Comp);
		def.Fields.Add(sfd2Comp);
		Assert.Equivalent(def,
						  dataSource, true);
		Assert.Equivalent(def,
						  compareConnection.GetDataSource(randomTableName), true);

		// test update
		sfd2 = new StorageField("testcol2")
		{
			Readonly = true,
			Nullable = false,
			Type = DataStoreFieldType.String,
			Length = 255,
			Unique = false,
			Encrypted = false
		};
		field2 = connection.UpdateField(randomTableName, sfd2);
		sfd2 = new StorageField("testcol2")
		{
			Readonly = true,
			Nullable = false,
			Type = DataStoreFieldType.String,
			Length = 255,
			Unique = false,
			Encrypted = false,
			DefaultValue = ""
		};
		Assert.Equivalent(sfd2, field2, true);

		def.Fields.RemoveAt(def.Fields.Count - 1);
		def.Fields.Add(field2);

		var getDef = connection.GetDataSource(randomTableName);
		var getCompareDef = compareConnection.GetDataSource(randomTableName);

		Assert.Equivalent(def, getDef, true);
		Assert.Equivalent(def, getCompareDef, true);

		// test delete
		// field
		connection.RemoveField(randomTableName, "testcol1");
		def.Fields.RemoveAt(def.Fields.Count - 2);

		getDef = connection.GetDataSource(randomTableName);
		getCompareDef = compareConnection.GetDataSource(randomTableName);

		Assert.Equivalent(def, getDef, true);
		Assert.Equivalent(def, getCompareDef, true);

		// table
		connection.RemoveDataSource(randomTableName);
		Assert.Null(connection.GetDataSource(randomTableName));
		Assert.Null(compareConnection.GetDataSource(randomTableName));
	}

	/// <summary>
	/// Tests update StoragePath for DataSources
	/// </summary>
	[Fact(DisplayName = "Tests update StoragePath for DataSources")]
	public void DataSourceStoragePathTest()
	{
		var connection = (IStorageConnection)DataStore.GetConnection(null);
		var customerConnection = (IStorageConnection)DataStore.GetConnection(Storage.GetContexts().First().Identifier, new DataStoreAuthentication("me", Guid.NewGuid(), new List<string>() {"testgroup"}));
		
		var randomTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		var randomTableName1 = "test_" + RandomDataHelper.Random.NextString(20, false);
		
		connection.CreateDataSource(GetDataSourceConfig(randomTableName, null));
		connection.CreateDataSource(GetDataSourceConfig(randomTableName1, randomTableName1));
		customerConnection.GetElements(new DataStoreQuery(randomTableName, null));
		customerConnection.GetElements(new DataStoreQuery(randomTableName1, null));

		connection.UpdateDataSource(GetDataSourceConfig(randomTableName, null));
		connection.UpdateDataSource(GetDataSourceConfig(randomTableName1, randomTableName1));
		customerConnection.GetElements(new DataStoreQuery(randomTableName, null));
		customerConnection.GetElements(new DataStoreQuery(randomTableName1, null));
		
		connection.UpdateDataSource(GetDataSourceConfig(randomTableName, null));
		connection.UpdateDataSource(GetDataSourceConfig(randomTableName1, randomTableName1));
		customerConnection.GetElements(new DataStoreQuery(randomTableName, null));
		customerConnection.GetElements(new DataStoreQuery(randomTableName1, null));
		
		connection.UpdateDataSource(GetDataSourceConfig(randomTableName, "stuff"));
		connection.UpdateDataSource(GetDataSourceConfig(randomTableName1, "stuff"));
		customerConnection.GetElements(new DataStoreQuery(randomTableName, null));
		customerConnection.GetElements(new DataStoreQuery(randomTableName1, null));
		
		connection.UpdateDataSource(GetDataSourceConfig(randomTableName, randomTableName));
		connection.UpdateDataSource(GetDataSourceConfig(randomTableName1, randomTableName1));
		customerConnection.GetElements(new DataStoreQuery(randomTableName, null));
		customerConnection.GetElements(new DataStoreQuery(randomTableName1, null));
	}

	private StorageDataSourceConfig GetDataSourceConfig(string name, string? path)
	{
		return new StorageDataSourceConfig(name)
		{
			StoreRevisions = false,
			StoragePath = path
		};
	}

	/// <summary>
	/// Tests whether exceptions are being thrown on invalid operations
	/// </summary>
	[Fact(DisplayName = "Exceptions on invalid operations are being thrown")]
	public virtual void DataSourceExceptionsTest()
	{
		var connection = (IStorageConnection)DataStore.GetConnection(null);
		var randomTableName = "test_" + RandomDataHelper.Random.NextString(20, false);

		Assert.Throws<DataSourceNotFoundException>(() => connection.CreateField(randomTableName, new StorageFieldConfig("testField")));
		Assert.Throws<DataSourceNotFoundException>(() => connection.RemoveField(randomTableName, "testField"));
		Assert.Throws<DataSourceNotFoundException>(() => connection.RenameField(randomTableName, "testField", "testField2"));
		Assert.Throws<DataSourceNotFoundException>(() => connection.UpdateField(randomTableName, new StorageFieldConfig("testField")));
		Assert.Throws<DataSourceNotFoundException>(() => connection.RemoveDataSource(randomTableName));
		Assert.Throws<DataSourceNotFoundException>(() => connection.UpdateDataSource(new StorageDataSourceConfig(randomTableName)));
		Assert.Null(connection.GetDataSource(randomTableName));

		Assert.Throws<DataStoreOperationException>(() => connection.CreateDataSource(new StorageDataSourceConfig("test_t.e!$%&§_:;*`st")));


		connection.CreateDataSource(new StorageDataSourceConfig(randomTableName));
		// '.' is not allowed in fieldName
		Assert.Throws<DataStoreOperationException>(() => connection.CreateField(randomTableName, new StorageFieldConfig("definition.test")));
		connection.CreateField(randomTableName, new StorageFieldConfig("TestField")
		{
			Length = 255
		});
		Assert.Throws<DataStoreOperationException>(() => connection.RenameField(randomTableName, "TestField", "DefinitionTest.Field"));
		Assert.Throws<DataStoreOperationException>(() => connection.RemoveField(randomTableName, "NonExistentField"));

		// update for nonexistent field should fail
		Assert.Throws<DataStoreOperationException>(() => connection.UpdateField(randomTableName, new StorageFieldConfig("TestField123")));

		// change field to primary key afterwards should fail
		Assert.Throws<ArgumentException>(() => connection.UpdateField(randomTableName, new StorageFieldConfig("TestField")
		{
			PrimaryKey = true
		}));
	}

	[Fact(DisplayName = "Create, Read, Update, Delete functions for data in linked data sources")]
	public void CrudLinkedDataTest()
	{
		var connection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		var mainConnection = (IStorageConnection)DataStore.GetConnection(null);
		var compareConnection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);

		var mainTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, mainTableName);
		AddTestFields(mainConnection, mainTableName);

		var nstTableName1 = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, nstTableName1);
		AddTestFields(mainConnection, nstTableName1);
		mainConnection.CreateField(mainTableName, new StorageFieldConfig("IdFieldFromNst1")
		{
			Nullable = true,
			LookupSource = nstTableName1
		});

		var nstTableName2 = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, nstTableName2);
		AddTestFields(mainConnection, nstTableName2);

		mainConnection.CreateField(mainTableName, new StorageFieldConfig("IdFieldFromNst2")
		{
			Nullable = true,
			LookupSource = nstTableName2
		});

		var insertInfo1 = InsertData(connection, nstTableName1, "dummy data 1");
		var insertInfo2 = InsertData(connection, nstTableName2, "dummy data 2");
		var nstElements1AfterInsert = compareConnection.GetElements(new DataStoreQuery(nstTableName1, null));
		var nstElements2AfterInsert = compareConnection.GetElements(new DataStoreQuery(nstTableName2, null));

		UpdateData(connection, nstTableName1, insertInfo1.ElementId, "update dummy data 1");
		UpdateData(connection, nstTableName2, insertInfo2.ElementId, "update dummy data 2");

		var nstElements1AfterUpdate = compareConnection.GetElements(new DataStoreQuery(nstTableName1, null));
		var nstElements2AfterUpdate = compareConnection.GetElements(new DataStoreQuery(nstTableName2, null));

		List<string> allNstValues = new List<string>();
		foreach (var dataStoreElement in nstElements1AfterUpdate)
		{
			foreach (var dataStoreElementValue in dataStoreElement.Values)
			{
				if (dataStoreElementValue.Value != null)
					allNstValues.Add(dataStoreElementValue.Value.ToString()!);
			}
		}

		foreach (var dataStoreElement in nstElements2AfterUpdate)
		{
			foreach (var dataStoreElementValue in dataStoreElement.Values)
			{
				if (dataStoreElementValue.Value != null)
					allNstValues.Add(dataStoreElementValue.Value.ToString()!);
			}
		}

		var insertMainLast = AddMainData(connection, mainTableName, insertInfo1, insertInfo2);

		UpdateMainData(connection, mainTableName, insertMainLast, insertInfo1, insertInfo2);
		var elementsAfterUpdate = compareConnection.GetElements(new DataStoreQuery(mainTableName, null));

		var deleteInfo = connection.DeleteElement(mainTableName, insertMainLast.ElementId,
												  new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "ariebau")
												  {
													  Comment = "delete main data"
												  });

		var elementsAfterDelete = compareConnection.GetElements(new DataStoreQuery(mainTableName, null));

		List<DataStoreQueryField> fields = GetQueryFields();
		var elementsWithForeignFields = compareConnection.GetElements(new DataStoreQuery(mainTableName, fields));

		var nstElementList = new List<string>
		{
			insertInfo1.ElementId, insertInfo2.ElementId
		};

		// checks //

		// test linked element count
		foreach (var dataStoreElement in elementsAfterUpdate)
		{
			var countElementLinked = dataStoreElement.Values.Where(it => it.Key.Contains("FromNst") && nstElementList.Contains(it.Value)).Count();
			Assert.True(countElementLinked == 2);
		}

		// test foreign key field existence
		foreach (var elementWithForeignFields in elementsWithForeignFields)
		{
			foreach (var dataStoreQueryField in fields)
			{
				Assert.True(elementWithForeignFields.Values.ContainsKey(dataStoreQueryField.Name));

				if (dataStoreQueryField.Name.Contains("FromNst"))
				{
					Assert.Contains(elementWithForeignFields.Values[dataStoreQueryField.Name]!.ToString(), allNstValues);
				}
			}
		}

		// test insert and update of NSTs
		for (var i = 0; i < nstElements1AfterInsert.Count; i++)
		{
			var testElement1Insert = nstElements1AfterInsert[i];
			var testElement1Update = nstElements1AfterUpdate[i];
			var testElement2Insert = nstElements2AfterInsert[i];
			var testElement2Update = nstElements2AfterUpdate[i];

			foreach (var (key, insert1Val) in testElement1Insert.Values)
			{
				if (key.EndsWith("Field"))
				{
					var update1Val = testElement1Update.Values[key];
					Assert.NotEqual(insert1Val, update1Val);

					var insert2Val = testElement2Insert.Values[key];
					var update2Val = testElement2Update.Values[key];
					Assert.NotEqual(insert2Val, update2Val);
				}
			}
		}

		// test deletion type and count
		Assert.True(deleteInfo.OperationType == DataStoreOperationType.Delete);
		Assert.True(elementsAfterUpdate.Count > elementsAfterDelete.Count);
	}

	[Fact(DisplayName = "Filter tests on linked data sources")]
	public void FilterLinkedDataTest()
	{
		var connection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		var mainConnection = (IStorageConnection)DataStore.GetConnection(null);
		var compareConnection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);

		var mainTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, mainTableName);
		AddTestFields(mainConnection, mainTableName);

		var nstTableName1 = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, nstTableName1);
		AddTestFields(mainConnection, nstTableName1);

		var nstTableName2 = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, nstTableName2);
		AddTestFields(mainConnection, nstTableName2);

		AddNstFields(mainConnection, mainTableName, nstTableName1, nstTableName2);

		var insertInfo1 = InsertData(connection, nstTableName1, "dummy data 1");
		var insertInfo2 = InsertData(connection, nstTableName2, "dummy data 2");
		var insertInfo3 = InsertData(connection, nstTableName1, "dummy data 3");
		var insertInfo4 = InsertData(connection, nstTableName2, "dummy data 4");
		var insertInfo5 = InsertData(connection, nstTableName1, "dummy data 5");
		var insertInfo6 = InsertData(connection, nstTableName2, "dummy data 6");
		var insertInfo7 = InsertData(connection, nstTableName1, "dummy data 7");
		var insertInfo8 = InsertData(connection, nstTableName2, "dummy data 8");
		var insertInfo9 = InsertData(connection, nstTableName1, "dummy data 9");
		var insertInfo10 = InsertData(connection, nstTableName2, "dummy data 10");

		for (var i = 0; i < 10; i++)
		{
			AddMainData(connection, mainTableName, insertInfo1, insertInfo2);
			AddMainData(connection, mainTableName, insertInfo3, insertInfo4);
			AddMainData(connection, mainTableName, insertInfo5, insertInfo6);
			AddMainData(connection, mainTableName, insertInfo7, insertInfo8);
			AddMainData(connection, mainTableName, insertInfo9, insertInfo10);
		}

		List<DataStoreQueryField> fields = GetAggregateFields(true);
		DataStoreQuery query = new DataStoreQuery(mainTableName, fields);
		var aggregatedElements = compareConnection.GetElements(query);
		var element = aggregatedElements.First();

		fields = GetQueryFields(true);
		DataStoreQuery filterQuery = new DataStoreQuery(mainTableName, fields);
		var filter = new QueryFilterGroup(QueryFilterLinkType.And)
		{
			Filters =
			{
				new LessThanFilter(new QueryFilterField("IdFieldFromNst1.IntField"), Math.Round(Double.Parse(element.Values["AvgInt"]!.ToString()!))),
				new LessThanFilter(new QueryFilterField("IdFieldFromNst2.DoubleField"), element.Values["AvgDouble2"]!)
			}
		};

		filterQuery.WithFilter(filter);

		var elementsWithForeignFields = compareConnection.GetElements(filterQuery);

		int lessIntCount = elementsWithForeignFields.Where(it => int.Parse(it.Values["IdFieldFromNst1.IntField"]!.ToString()!) <
																 Math.Round(Double.Parse(element.Values["AvgInt"]!.ToString()!))).Count();

		int lessDoubleCount = elementsWithForeignFields.Where(it => Double.Parse(it.Values["IdFieldFromNst2.DoubleField"]!.ToString()!) <
																	Double.Parse(element.Values["AvgDouble2"]!.ToString()!)).Count();


		filterQuery = new DataStoreQuery(mainTableName, fields);
		// all linked source fields exist, when both linked data sources are used in filter fields
		filter = new QueryFilterGroup(QueryFilterLinkType.And)
		{
			Filters =
			{
				new LessThanFilter(Math.Round(Double.Parse(element.Values["AvgInt"]!.ToString()!)), new QueryFilterField("IdFieldFromNst1.IntField")),
				new LessThanFilter(element.Values["AvgDouble2"]!, new QueryFilterField("IdFieldFromNst2.DoubleField"))
			}
		};

		filterQuery.WithFilter(filter);

		var elementsWithForeignFieldsReverse = compareConnection.GetElements(filterQuery);
		int lessIntCountReverse = elementsWithForeignFieldsReverse.Where(it => int.Parse(it.Values["IdFieldFromNst1.IntField"]!.ToString()!) >
																			   Math.Round(Double.Parse(element.Values["AvgInt"]!.ToString()!))).Count();

		int lessDoubleCountReverse = elementsWithForeignFieldsReverse.Where(it => Double.Parse(it.Values["IdFieldFromNst2.DoubleField"]!.ToString()!) >
																				  Double.Parse(element.Values["AvgDouble2"]!.ToString()!)).Count();

		var filterQuery2 = new DataStoreQuery(mainTableName, fields);
		var elementsWithForeignFieldsNoFilter = compareConnection.GetElements(filterQuery2);
		Console.Out.WriteLine("FilterLinkedDataTest() - elementsWithForeignFieldsNoFilter: " + elementsWithForeignFieldsNoFilter.Count);

		var filterQuery3 = new DataStoreQuery(mainTableName, fields);
		// all linked source fields exist, when only one linked data source is used as filter field
		var filter3 = new QueryFilterGroup(QueryFilterLinkType.And)
		{
			Filters =
			{
				new GreaterThanEqualsFilter(Math.Round(Double.Parse(element.Values["AvgInt"]!.ToString()!)), new QueryFilterField("IdFieldFromNst1.IntField"))
			}
		};

		filterQuery3.WithFilter(filter3);
		var elementsWithForeignFieldsSameJoinInt = compareConnection.GetElements(filterQuery3);
		Console.Out.WriteLine("FilterLinkedDataTest() - elementsWithForeignFieldsSameJoinInt: " + elementsWithForeignFieldsSameJoinInt.Count);


		var filterQuery4 = new DataStoreQuery(mainTableName, fields);
		// all linked source fields exist, when only one linked data source is used as filter field
		var filter4 = new QueryFilterGroup(QueryFilterLinkType.And)
		{
			Filters =
			{
				new GreaterThanEqualsFilter(element.Values["AvgDouble"]!, new QueryFilterField("IdFieldFromNst1.DoubleField"))
			}
		};

		filterQuery4.WithFilter(filter4);
		var elementsWithForeignFieldsSameJoinDouble = compareConnection.GetElements(filterQuery4);
		Console.Out.WriteLine("FilterLinkedDataTest() - elementsWithForeignFieldsSameJoinDouble: " + elementsWithForeignFieldsSameJoinDouble.Count);


		var filterQuery5 = new DataStoreQuery(mainTableName, fields);
		// all linked source fields exist, when only one linked data source is used as filter field
		var filter5 = new QueryFilterGroup(QueryFilterLinkType.Or)
		{
			Filters =
			{
				new GreaterThanEqualsFilter(Math.Round(Double.Parse(element.Values["AvgInt"]!.ToString()!)), new QueryFilterField("IdFieldFromNst1.IntField")),
				new GreaterThanEqualsFilter(element.Values["AvgDouble"]!, new QueryFilterField("IdFieldFromNst1.DoubleField"))
			}
		};

		filterQuery5.WithFilter(filter5);
		var elementsWithForeignFieldsSameJoin = compareConnection.GetElements(filterQuery5);
		Console.Out.WriteLine("FilterLinkedDataTest() - elementsWithForeignFieldsSameJoin: " + elementsWithForeignFieldsSameJoin.Count);

		// checks //
		Assert.Equal(lessIntCount, elementsWithForeignFields.Count);
		Assert.Equal(lessDoubleCount, elementsWithForeignFields.Count);

		Assert.Equal(lessIntCountReverse, elementsWithForeignFieldsReverse.Count);
		Assert.Equal(lessDoubleCountReverse, elementsWithForeignFieldsReverse.Count);

		Assert.True(elementsWithForeignFieldsNoFilter.Count > 0);
		Assert.True(elementsWithForeignFieldsSameJoin.Count > 0);
	}
	
	[Fact(DisplayName = "Return linked data on insert and update")]
	public void InsertUpdateLinkedDataTest()
	{
		var connection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		var mainConnection = (IStorageConnection)DataStore.GetConnection(null);

		var mainTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, mainTableName);
		AddTestFields(mainConnection, mainTableName);
		var mainTable = connection.GetDataSource(mainTableName);

		var nstTableName1 = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, nstTableName1);
		AddTestFields(mainConnection, nstTableName1);

		var nstTableName2 = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, nstTableName2);
		AddTestFields(mainConnection, nstTableName2);

		AddNstFields(mainConnection, mainTableName, nstTableName1, nstTableName2);

		var insertInfo1 = InsertData(connection, nstTableName1, "dummy data 1");
		var insertInfo2 = InsertData(connection, nstTableName2, "dummy data 2");
		var insertInfo3 = InsertData(connection, nstTableName1, "dummy data 3");
		var insertInfo4 = InsertData(connection, nstTableName2, "dummy data 4");
		var insertInfo5 = InsertData(connection, nstTableName1, "dummy data 5");
		var insertInfo6 = InsertData(connection, nstTableName2, "dummy data 6");
		var insertInfo7 = InsertData(connection, nstTableName1, "dummy data 7");
		var insertInfo8 = InsertData(connection, nstTableName2, "dummy data 8");
		var insertInfo9 = InsertData(connection, nstTableName1, "dummy data 9");
		var insertInfo10 = InsertData(connection, nstTableName2, "dummy data 10");

		for (var i = 0; i < 10; i++)
		{
			AddMainData(connection, mainTableName, insertInfo1, insertInfo2);
			AddMainData(connection, mainTableName, insertInfo3, insertInfo4);
			AddMainData(connection, mainTableName, insertInfo5, insertInfo6);
			AddMainData(connection, mainTableName, insertInfo7, insertInfo8);
			AddMainData(connection, mainTableName, insertInfo9, insertInfo10);
		}

		Dictionary<string, object?> additionalDict = new Dictionary<string, object?>()
		{
			["IdFieldFromNst1"] = insertInfo1.ElementId,
			["IdFieldFromNst2"] = insertInfo2.ElementId
		};

		var data = RandomDataHelper.GetDataSourceData(mainTable!, additionalDict);

		var dataStoreQueryFields = new List<DataStoreQueryField>()
		{
			new DataStoreQueryField("IdFieldFromNst1.StringField"),
			new DataStoreQueryField("IdFieldFromNst1.IntField"),
			new DataStoreQueryField("IdFieldFromNst2.StringField"),
			new DataStoreQueryField("IdFieldFromNst2.IntField"),
		};

		var createElementData = connection.CreateElement(mainTableName, new DataStoreElementData(data, TestAuth.Groups!),
								 new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 0, "me"), lookupFields: dataStoreQueryFields);

		var selectElementData = connection.GetElement(mainTableName, createElementData.ElementId, dataStoreQueryFields);
		
		Assert.Equivalent(selectElementData, createElementData.ElementData);
		
		var updateElementData = connection.UpdateElement(mainTableName, new DataStoreElementData(createElementData.ElementId, new Dictionary<string, object?>()
														 {
															 ["IdFieldFromNst2"] = insertInfo4.ElementId
														 }, TestAuth.Groups),
														 new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 0, "me"), lookupFields: dataStoreQueryFields);
		
		var selectElementData2 = connection.GetElement(mainTableName, createElementData.ElementId, dataStoreQueryFields);
		
		Assert.Equivalent(selectElementData2, updateElementData.ElementData);
	}

	[Fact(DisplayName = "Order by tests on linked data sources")]
	public void OrderByLinkedDataTest()
	{
		var connection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		var mainConnection = (IStorageConnection)DataStore.GetConnection(null);
		var compareConnection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);

		var mainTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, mainTableName);
		AddTestFields(mainConnection, mainTableName);

		var nstTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, nstTableName);
		AddTestFields(mainConnection, nstTableName);
		AddNstFields(mainConnection, mainTableName, nstTableName, null);

		var insertInfo1 = InsertData(connection, nstTableName, "dummy data 1");
		var insertInfo2 = InsertData(connection, nstTableName, "dummy data 2");
		var insertInfo3 = InsertData(connection, nstTableName, "dummy data 3");
		var insertInfo4 = InsertData(connection, nstTableName, "dummy data 4");
		var insertInfo5 = InsertData(connection, nstTableName, "dummy data 5");
		var insertInfo6 = InsertData(connection, nstTableName, "dummy data 6");
		var insertInfo7 = InsertData(connection, nstTableName, "dummy data 7");
		var insertInfo8 = InsertData(connection, nstTableName, "dummy data 8");
		var insertInfo9 = InsertData(connection, nstTableName, "dummy data 9");
		var insertInfo10 = InsertData(connection, nstTableName, "dummy data 10");

		for (var i = 0; i < 10; i++)
		{
			AddMainDataSingle(connection, mainTableName, insertInfo1);
			AddMainDataSingle(connection, mainTableName, insertInfo2);
			AddMainDataSingle(connection, mainTableName, insertInfo3);
			AddMainDataSingle(connection, mainTableName, insertInfo4);
			AddMainDataSingle(connection, mainTableName, insertInfo5);
			AddMainDataSingle(connection, mainTableName, insertInfo6);
			AddMainDataSingle(connection, mainTableName, insertInfo7);
			AddMainDataSingle(connection, mainTableName, insertInfo8);
			AddMainDataSingle(connection, mainTableName, insertInfo9);
			AddMainDataSingle(connection, mainTableName, insertInfo10);
		}
		
		var elementsOrderedByIdAsc = GetOrderByElements(compareConnection, mainTableName,
															new()
															{
																new DataStoreElementSort("IdFieldFromNst1.Id")
															});

		var elementsOrderedByStringAsc = GetOrderByElements(compareConnection, mainTableName,
															new()
															{
																new DataStoreElementSort("IdFieldFromNst1.StringField")
															});

		var elementsOrderedByStringDesc = GetOrderByElements(compareConnection, mainTableName,
															 new()
															 {
																 new DataStoreElementSort("IdFieldFromNst1.StringField", DataStoreElementSortDirection.Desc)
															 });

		var elementsOrderedByIntAsc = GetOrderByElements(compareConnection, mainTableName,
														 new() { new DataStoreElementSort("IdFieldFromNst1.IntField") });

		var elementsOrderedByIntDesc = GetOrderByElements(compareConnection, mainTableName,
														  new() { new DataStoreElementSort("IdFieldFromNst1.IntField", DataStoreElementSortDirection.Desc) });

		var elementsOrderedByDoubleAsc = GetOrderByElements(compareConnection, mainTableName,
															new()
															{
																new DataStoreElementSort("IdFieldFromNst1.DoubleField")
															});

		var elementsOrderedByDoubleDesc = GetOrderByElements(compareConnection, mainTableName,
															 new()
															 {
																 new DataStoreElementSort("IdFieldFromNst1.DoubleField", DataStoreElementSortDirection.Desc)
															 });

		var elementsOrderedByBoolAsc = GetOrderByElements(compareConnection, mainTableName,
														  new()
														  {
															  new DataStoreElementSort("IdFieldFromNst1.BooleanField")
														  });
		var elementsOrderedByBoolDesc = GetOrderByElements(compareConnection, mainTableName,
														   new()
														   {
															   new DataStoreElementSort("IdFieldFromNst1.BooleanField", DataStoreElementSortDirection.Desc)
														   });

		var elementsOrderedByDateTimeAsc = GetOrderByElements(compareConnection, mainTableName,
															  new()
															  {
																  new DataStoreElementSort("IdFieldFromNst1.DateTimeField")
															  });
		var elementsOrderedByDateTimeDesc = GetOrderByElements(compareConnection, mainTableName,
															   new()
															   {
																   new DataStoreElementSort("IdFieldFromNst1.DateTimeField",
																							DataStoreElementSortDirection.Desc)
															   });

		var elementsOrderedByIntDoubleBoolAsc = GetOrderByElements(compareConnection, mainTableName,
																   new()
																   {
																	   new DataStoreElementSort("IdFieldFromNst1.BooleanField"),
																	   new DataStoreElementSort("IdFieldFromNst1.DoubleField"),
																	   new DataStoreElementSort("IdFieldFromNst1.IntField"),
																	   new DataStoreElementSort("StringField")
																   });

		var elementsOrderedByIntDoubleBoolDesc = GetOrderByElements(compareConnection, mainTableName,
																	new()
																	{
																		new DataStoreElementSort(
																			"IdFieldFromNst1.BooleanField", DataStoreElementSortDirection.Desc),
																		new DataStoreElementSort(
																			"IdFieldFromNst1.DoubleField", DataStoreElementSortDirection.Desc),
																		new DataStoreElementSort(
																			"IdFieldFromNst1.IntField", DataStoreElementSortDirection.Desc),
																		new DataStoreElementSort("StringField", DataStoreElementSortDirection.Desc)
																	});

		// checks //
		Assert.Equal(300, elementsOrderedByIdAsc.Count);
		Assert.Equal(elementsOrderedByIdAsc.OrderBy(it => it.Values["IdFieldFromNst1"]).ToList(), elementsOrderedByIdAsc);
		Assert.Equal(elementsOrderedByStringAsc.Last().Values["IdFieldFromNst1.StringField"],
					 elementsOrderedByStringDesc.First().Values["IdFieldFromNst1.StringField"]);

		Assert.Equal(elementsOrderedByStringAsc.First().Values["IdFieldFromNst1.StringField"],
					 elementsOrderedByStringDesc.Last().Values["IdFieldFromNst1.StringField"]);

		Assert.Equal(elementsOrderedByIntAsc.Last().Values["IdFieldFromNst1.IntField"],
					 elementsOrderedByIntDesc.First().Values["IdFieldFromNst1.IntField"]);

		Assert.Equal(elementsOrderedByIntAsc.First().Values["IdFieldFromNst1.IntField"],
					 elementsOrderedByIntDesc.Last().Values["IdFieldFromNst1.IntField"]);

		Assert.Equal(elementsOrderedByDoubleAsc.Last().Values["IdFieldFromNst1.DoubleField"],
					 elementsOrderedByDoubleDesc.First().Values["IdFieldFromNst1.DoubleField"]);

		Assert.Equal(elementsOrderedByDoubleAsc.First().Values["IdFieldFromNst1.DoubleField"],
					 elementsOrderedByDoubleDesc.Last().Values["IdFieldFromNst1.DoubleField"]);

		Assert.Equal(elementsOrderedByBoolAsc.Last().Values["IdFieldFromNst1.BooleanField"],
					 elementsOrderedByBoolDesc.First().Values["IdFieldFromNst1.BooleanField"]);

		Assert.Equal(elementsOrderedByBoolAsc.First().Values["IdFieldFromNst1.BooleanField"],
					 elementsOrderedByBoolDesc.Last().Values["IdFieldFromNst1.BooleanField"]);

		Assert.Equal(elementsOrderedByDateTimeAsc.Last().Values["IdFieldFromNst1.DateTimeField"],
					 elementsOrderedByDateTimeDesc.First().Values["IdFieldFromNst1.DateTimeField"]);

		Assert.Equal(elementsOrderedByDateTimeAsc.First().Values["IdFieldFromNst1.DateTimeField"],
					 elementsOrderedByDateTimeDesc.Last().Values["IdFieldFromNst1.DateTimeField"]);

		Assert.Equal(elementsOrderedByIntDoubleBoolAsc.Last().Values["StringField"],
					 elementsOrderedByIntDoubleBoolDesc.First().Values["StringField"]);

		Assert.Equal(elementsOrderedByIntDoubleBoolAsc.First().Values["StringField"],
					 elementsOrderedByIntDoubleBoolDesc.Last().Values["StringField"]);
	}

	[Fact(DisplayName = "Order by grouped tests on linked data sources")]
	public void OrderByGroupedLinkedDataTest()
	{
		var connection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		var mainConnection = (IStorageConnection)DataStore.GetConnection(null);
		var compareConnection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);

		var mainTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, mainTableName);
		AddTestFields(mainConnection, mainTableName);

		var nstTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, nstTableName);
		AddTestFields(mainConnection, nstTableName);
		AddNstFields(mainConnection, mainTableName, nstTableName, null);

		var insertInfo1 = InsertData(connection, nstTableName, "dummy data 1", withSpecialCharacters: false);
		var insertInfo2 = InsertData(connection, nstTableName, "dummy data 2", withSpecialCharacters: false);
		var insertInfo3 = InsertData(connection, nstTableName, "dummy data 3", withSpecialCharacters: false);
		var insertInfo4 = InsertData(connection, nstTableName, "dummy data 4", withSpecialCharacters: false);
		var insertInfo5 = InsertData(connection, nstTableName, "dummy data 5", withSpecialCharacters: false);

		var entries = new List<DataStoreSuccessInfo>
		{
			insertInfo1,
			insertInfo2,
			insertInfo3,
			insertInfo4
		};

		for (var i = 0; i < 2; i++)
		{
			// creates 3 entries for each call
			AddMainDataSingle(connection, mainTableName, insertInfo1);
			AddMainDataSingle(connection, mainTableName, insertInfo2);
			AddMainDataSingle(connection, mainTableName, insertInfo3);
			AddMainDataSingle(connection, mainTableName, insertInfo4);
		}
		
		AddMainDataSingle(connection, mainTableName, insertInfo5);
		
		List<DataStoreQueryField> fields = new List<DataStoreQueryField>()
		{
			new DataStoreQueryField("IdFieldFromNst1.StringField"),
			new DataStoreQueryField("COUNT(IdFieldFromNst1.StringField)"),
			new DataStoreQueryField("COUNT(*)")
		};
		DataStoreQuery orderByQuery = new DataStoreQuery(mainTableName, fields);
		orderByQuery.WithOrderBy(new List<DataStoreElementSort>()
		{
			new DataStoreElementSort("COUNT(IdFieldFromNst1.StringField)"),
			new DataStoreElementSort("IdFieldFromNst1.StringField")
		});

		orderByQuery.WithGroupBy(new List<string>()
		{
			"IdFieldFromNst1.StringField"
		});
		
		DataStoreQuery orderByQuery1 = new DataStoreQuery(mainTableName, fields);
		orderByQuery1.WithOrderBy(new List<DataStoreElementSort>()
		{
			new DataStoreElementSort("COUNT(*)"),
			new DataStoreElementSort("IdFieldFromNst1.StringField")
		});

		orderByQuery1.WithGroupBy(new List<string>()
		{
			"IdFieldFromNst1.StringField"
		});

		var elements = compareConnection.GetElements(orderByQuery);
		var elements1 = compareConnection.GetElements(orderByQuery1);
		Assert.Equal(5, elements.Count);
		Assert.Equal(insertInfo5.ElementData!.Values["StringField"], elements.First().Values["IdFieldFromNst1.StringField"]);
		Assert.Equal(3, (long)elements.First().Values["COUNT(IdFieldFromNst1.StringField)"]!);
		Assert.Equal(3, (long)elements.First().Values["COUNT(*)"]!);

		var stringFields = entries.Select(it => (string)it.ElementData!.Values["StringField"]!).Order().ToList();
		Assert.Equal(stringFields.First(), elements[1].Values["IdFieldFromNst1.StringField"]);
		Assert.Equal(6, (long)elements[1].Values["COUNT(IdFieldFromNst1.StringField)"]!);
		Assert.Equal(6, (long)elements[1].Values["COUNT(*)"]!);

		Assert.Equal(stringFields.Last(), elements.Last().Values["IdFieldFromNst1.StringField"]);
		Assert.Equal(6, (long)elements.Last().Values["COUNT(IdFieldFromNst1.StringField)"]!);
		Assert.Equal(6, (long)elements.Last().Values["COUNT(*)"]!);

		Assert.Equal(5, elements1.Count);
		Assert.Equal(insertInfo5.ElementData!.Values["StringField"], elements1.First().Values["IdFieldFromNst1.StringField"]);
		Assert.Equal(3, (long)elements1.First().Values["COUNT(IdFieldFromNst1.StringField)"]!);
		Assert.Equal(3, (long)elements1.First().Values["COUNT(*)"]!);

		var stringFields1 = entries.Select(it => (string)it.ElementData!.Values["StringField"]!).Order().ToList();
		Assert.Equal(stringFields1.First(), elements1[1].Values["IdFieldFromNst1.StringField"]);
		Assert.Equal(6, (long)elements1[1].Values["COUNT(*)"]!);
		
		Assert.Equal(stringFields1.Last(), elements1.Last().Values["IdFieldFromNst1.StringField"]);
		Assert.Equal(6, (long)elements1.Last().Values["COUNT(IdFieldFromNst1.StringField)"]!);
		Assert.Equal(6, (long)elements1.Last().Values["COUNT(*)"]!);
	}

	[Fact(DisplayName = "Limit + offset tests for linked data sources")]
	public void LimitOffsetLinkedTest()
	{
		var connection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		var mainConnection = (IStorageConnection)DataStore.GetConnection(null);
		var compareConnection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);

		var mainTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, mainTableName);
		AddTestFields(mainConnection, mainTableName);

		var nstTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, nstTableName);
		AddTestFields(mainConnection, nstTableName);
		AddNstFields(mainConnection, mainTableName, nstTableName, null);

		var insertInfo1 = InsertData(connection, nstTableName, "dummy data 1");
		var insertInfo2 = InsertData(connection, nstTableName, "dummy data 2");
		var insertInfo3 = InsertData(connection, nstTableName, "dummy data 3");
		var insertInfo4 = InsertData(connection, nstTableName, "dummy data 4");
		var insertInfo5 = InsertData(connection, nstTableName, "dummy data 5");
		var insertInfo6 = InsertData(connection, nstTableName, "dummy data 6");
		var insertInfo7 = InsertData(connection, nstTableName, "dummy data 7");
		var insertInfo8 = InsertData(connection, nstTableName, "dummy data 8");
		var insertInfo9 = InsertData(connection, nstTableName, "dummy data 9");
		var insertInfo10 = InsertData(connection, nstTableName, "dummy data 10");

		for (var i = 0; i < 10; i++)
		{
			AddMainDataSingle(connection, mainTableName, insertInfo1);
			AddMainDataSingle(connection, mainTableName, insertInfo2);
			AddMainDataSingle(connection, mainTableName, insertInfo3);
			AddMainDataSingle(connection, mainTableName, insertInfo4);
			AddMainDataSingle(connection, mainTableName, insertInfo5);
			AddMainDataSingle(connection, mainTableName, insertInfo6);
			AddMainDataSingle(connection, mainTableName, insertInfo7);
			AddMainDataSingle(connection, mainTableName, insertInfo8);
			AddMainDataSingle(connection, mainTableName, insertInfo9);
			AddMainDataSingle(connection, mainTableName, insertInfo10);
		}

		List<string> selectedElements = new List<string>();

		int lastCircle;
		// test limit and paging
		for (lastCircle = 0; lastCircle < 12; lastCircle++)
		{
			DataStoreQuery query = new DataStoreQuery(mainTableName, null).WithPaging(25, (lastCircle * 25));
			var elements = compareConnection.GetElements(query);

			Assert.Equal(25, elements.Count);

			// check, if every element is selected once
			foreach (var element in elements)
			{
				Assert.True(!selectedElements.Contains(element.Id));
				selectedElements.Add(element.Id);
			}
		}

		lastCircle++;
		DataStoreQuery query2 = new DataStoreQuery(mainTableName, null).WithPaging(25, (lastCircle * 25));
		var elements2 = compareConnection.GetElements(query2);
		Assert.Empty(elements2);
	}

	[Fact(DisplayName = "Group by tests for linked data sources")]
	public void GroupLinkedTest()
	{
		var connection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		var mainConnection = (IStorageConnection)DataStore.GetConnection(null);
		var compareConnection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);

		var mainTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, mainTableName);
		AddTestFields(mainConnection, mainTableName);

		var nstTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, nstTableName);
		AddTestFields(mainConnection, nstTableName);
		AddNstFields(mainConnection, mainTableName, nstTableName, null);

		var insertInfo1 = InsertData(connection, nstTableName, "dummy data 1");
		var insertInfo2 = InsertData(connection, nstTableName, "dummy data 2");
		var insertInfo3 = InsertData(connection, nstTableName, "dummy data 3");
		var insertInfo4 = InsertData(connection, nstTableName, "dummy data 4");
		var insertInfo5 = InsertData(connection, nstTableName, "dummy data 5");
		var insertInfo6 = InsertData(connection, nstTableName, "dummy data 6");
		var insertInfo7 = InsertData(connection, nstTableName, "dummy data 7");
		var insertInfo8 = InsertData(connection, nstTableName, "dummy data 8");
		var insertInfo9 = InsertData(connection, nstTableName, "dummy data 9");
		var insertInfo10 = InsertData(connection, nstTableName, "dummy data 10");

		for (var i = 0; i < 10; i++)
		{
			AddMainDataSingle(connection, mainTableName, insertInfo1);
			AddMainDataSingle(connection, mainTableName, insertInfo2);
			AddMainDataSingle(connection, mainTableName, insertInfo3);
			AddMainDataSingle(connection, mainTableName, insertInfo4);
			AddMainDataSingle(connection, mainTableName, insertInfo5);
			AddMainDataSingle(connection, mainTableName, insertInfo6);
			AddMainDataSingle(connection, mainTableName, insertInfo7);
			AddMainDataSingle(connection, mainTableName, insertInfo8);
			AddMainDataSingle(connection, mainTableName, insertInfo9);
			AddMainDataSingle(connection, mainTableName, insertInfo10);
		}

		var elementsGroupedByString = GetGroupByElements(compareConnection, mainTableName, new List<string>() { "IdFieldFromNst1.StringField" });
		var elementsGroupedByInt = GetGroupByElements(compareConnection, mainTableName, new List<string>() { "IdFieldFromNst1.IntField" });
		var elementsGroupedByDouble = GetGroupByElements(compareConnection, mainTableName, new List<string>() { "IdFieldFromNst1.DoubleField" });
		var elementsGroupedByBool = GetGroupByElements(compareConnection, mainTableName, new List<string>() { "IdFieldFromNst1.BooleanField" });
		var elementsGroupedByDate = GetGroupByElements(compareConnection, mainTableName, new List<string>() { "IdFieldFromNst1.DateTimeField" });

		var elementsGroupedByDoubleBool = GetGroupByElements(compareConnection, mainTableName,
															 new List<string>() { "IdFieldFromNst1.DoubleField", "IdFieldFromNst1.BooleanField" });

		// group by count max. inserted possibilities
		Assert.True(elementsGroupedByString.Count <= 10);
		Assert.True(elementsGroupedByInt.Count <= 10);
		Assert.True(elementsGroupedByDouble.Count <= 10);
		Assert.True(elementsGroupedByBool.Count <= 1);
		Assert.True(elementsGroupedByDate.Count <= 10);
		Assert.True(elementsGroupedByDoubleBool.Count <= 10);

		foreach (var elementGroupedByString in elementsGroupedByString)
		{
			Assert.True(elementGroupedByString.Values.ContainsKey("IdFieldFromNst1.StringField"));
		}

		foreach (var elementGroupedByInt in elementsGroupedByInt)
		{
			Assert.True(elementGroupedByInt.Values.ContainsKey("IdFieldFromNst1.IntField"));
		}

		foreach (var elementGroupedByDouble in elementsGroupedByDouble)
		{
			Assert.True(elementGroupedByDouble.Values.ContainsKey("IdFieldFromNst1.DoubleField"));
		}

		foreach (var elementGroupedByBool in elementsGroupedByBool)
		{
			Assert.True(elementGroupedByBool.Values.ContainsKey("IdFieldFromNst1.BooleanField"));
		}

		foreach (var elementGroupedByDate in elementsGroupedByDate)
		{
			Assert.True(elementGroupedByDate.Values.ContainsKey("IdFieldFromNst1.DateTimeField"));
		}

		foreach (var elementGroupedByDoubleBool in elementsGroupedByDoubleBool)
		{
			Assert.True(elementGroupedByDoubleBool.Values.ContainsKey("IdFieldFromNst1.DoubleField"));
			Assert.True(elementGroupedByDoubleBool.Values.ContainsKey("IdFieldFromNst1.BooleanField"));
		}
	}


	[Fact(DisplayName = "Aggregate tests for linked data sources")]
	public void AggregateLinkedTest()
	{
		var connection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		var mainConnection = (IStorageConnection)DataStore.GetConnection(null);
		var compareConnection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);

		var mainTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, mainTableName);
		AddTestFields(mainConnection, mainTableName);

		var nstTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, nstTableName);
		AddTestFields(mainConnection, nstTableName);
		AddNstFields(mainConnection, mainTableName, nstTableName, null);

		var insertInfo1 = InsertData(connection, nstTableName, "dummy data 1");
		var insertInfo2 = InsertData(connection, nstTableName, "dummy data 2");
		var insertInfo3 = InsertData(connection, nstTableName, "dummy data 3");
		var insertInfo4 = InsertData(connection, nstTableName, "dummy data 4");
		var insertInfo5 = InsertData(connection, nstTableName, "dummy data 5");
		var insertInfo6 = InsertData(connection, nstTableName, "dummy data 6");
		var insertInfo7 = InsertData(connection, nstTableName, "dummy data 7");
		var insertInfo8 = InsertData(connection, nstTableName, "dummy data 8");
		var insertInfo9 = InsertData(connection, nstTableName, "dummy data 9");
		var insertInfo10 = InsertData(connection, nstTableName, "dummy data 10");

		for (var i = 0; i < 50; i++)
		{
			AddMainDataSingle(connection, mainTableName, insertInfo1);
			AddMainDataSingle(connection, mainTableName, insertInfo2);
			AddMainDataSingle(connection, mainTableName, insertInfo3);
			AddMainDataSingle(connection, mainTableName, insertInfo4);
			AddMainDataSingle(connection, mainTableName, insertInfo5);
			AddMainDataSingle(connection, mainTableName, insertInfo6);
			AddMainDataSingle(connection, mainTableName, insertInfo7);
			AddMainDataSingle(connection, mainTableName, insertInfo8);
			AddMainDataSingle(connection, mainTableName, insertInfo9);
			AddMainDataSingle(connection, mainTableName, insertInfo10);
		}

		List<DataStoreQueryField> fields = GetAggregateFields();

		DataStoreQuery query = new DataStoreQuery(mainTableName, GetQueryFields());
		var selectedElements = compareConnection.GetElements(query);

		double sumDoubleTest = 0;
		double avgIntTest = 0;
		double minDoubleTest = 1;
		double maxDoubleTest = 0;
		var maxDateTest = new DateTime(1970, 1, 1);
		int countBoolTest = 0;
		int countAvgNotNull = 0;

		foreach (var selectedElement in selectedElements)
		{
			if (selectedElement.Values["IdFieldFromNst1.DoubleField"] != null)
			{
				sumDoubleTest += Double.Parse(selectedElement.Values["IdFieldFromNst1.DoubleField"]!.ToString()!);

				if (selectedElement.Values["IdFieldFromNst1.DoubleField"] != null &&
					Double.Parse(selectedElement.Values["IdFieldFromNst1.DoubleField"]!.ToString()!) < minDoubleTest)
					minDoubleTest = Double.Parse(selectedElement.Values["IdFieldFromNst1.DoubleField"]!.ToString()!);

				if (Double.Parse(selectedElement.Values["IdFieldFromNst1.DoubleField"]!.ToString()!) > maxDoubleTest)
					maxDoubleTest = Double.Parse(selectedElement.Values["IdFieldFromNst1.DoubleField"]!.ToString()!);
			}

			if (selectedElement.Values["IdFieldFromNst1.DateTimeField"] != null)
			{
				if (DateTime.Parse(selectedElement.Values["IdFieldFromNst1.DateTimeField"]!.ToString()!) > maxDateTest)
					maxDateTest = DateTime.Parse(selectedElement.Values["IdFieldFromNst1.DateTimeField"]!.ToString()!);
			}

			if (selectedElement.Values["IdFieldFromNst1.IntField"] != null)
			{
				avgIntTest += int.Parse(selectedElement.Values["IdFieldFromNst1.IntField"]!.ToString()!);
				countAvgNotNull++;
			}

			if (selectedElement.Values["IdFieldFromNst1.BooleanField"] != null)
				countBoolTest++;
		}

		avgIntTest = avgIntTest / countAvgNotNull;

		query = new DataStoreQuery(mainTableName, fields);
		var aggregatedElements = compareConnection.GetElements(query);
		var element = aggregatedElements.First();

		Assert.Equal(Math.Round(sumDoubleTest, 7).ToString(CultureInfo.CurrentCulture), Math.Round((double)element.Values["SumDouble"]!, 7).ToString(CultureInfo.CurrentCulture));
		Assert.Equal(minDoubleTest.ToString(CultureInfo.CurrentCulture), element.Values["MinDouble"]!.ToString());
		Assert.Equal(maxDoubleTest.ToString(CultureInfo.CurrentCulture), element.Values["MaxDouble"]!.ToString());
		Assert.Equal(maxDateTest.ToString(CultureInfo.CurrentCulture), element.Values["MaxDate"]!.ToString());
		Assert.Equal(countBoolTest.ToString(), element.Values["CountBool"]!.ToString());

		Assert.True((avgIntTest - 0.1) <= Double.Parse(element.Values["AvgInt"]!.ToString()!));
		Assert.True(Double.Parse(element.Values["AvgInt"]!.ToString()!) <= (avgIntTest + 0.1));
	}
	
	[Fact(DisplayName = "Create element(s) and update case insensitive")]
	public void InsertAndUpdateCaseInsensitiveTest()
	{
		var mainConnection = (IStorageConnection)DataStore.GetConnection(null);
		var connection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		var compareConnection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		
		var mainTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, mainTableName);
		var testFields = AddTestFields(mainConnection, mainTableName);
		
		var insertInfo = InsertData(connection, mainTableName, "dummy data", null, true, false, true);
		var insertedElement = compareConnection.GetElement(mainTableName, insertInfo.ElementId);
		
		var updateInfo = UpdateData(connection, mainTableName, insertInfo.ElementId, "dummy data", true);
		var updatedElement = compareConnection.GetElement(mainTableName, updateInfo.ElementId);
		
		InsertMultiData(connection, mainTableName, "multi dummy data", 5, true);
		var elements = compareConnection.GetElements(new DataStoreQuery(mainTableName, null));
		
		foreach (StorageFieldConfig storageFieldConfig in testFields)
		{
			Assert.True(insertedElement.Values.ContainsKey(storageFieldConfig.Name));
			Assert.NotNull(insertedElement.Values[storageFieldConfig.Name]);
		}
		
		foreach (StorageFieldConfig storageFieldConfig in testFields)
		{
			Assert.True(updatedElement.Values.ContainsKey(storageFieldConfig.Name));
			Assert.NotNull(updatedElement.Values[storageFieldConfig.Name]);
		}
		
		foreach (DataStoreElement dataStoreElement in elements)
		{
			foreach (StorageFieldConfig storageFieldConfig in testFields)
			{
				Assert.True(dataStoreElement.Values.ContainsKey(storageFieldConfig.Name));
				Assert.NotNull(dataStoreElement.Values[storageFieldConfig.Name]);
			}
		}
	}

	[Fact(DisplayName = "Basic operations for virtual columns")]
	public void BasicOperationsVirtualColumnsTest()
	{
		var connection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		var mainConnection = (IStorageConnection)DataStore.GetConnection(null);

		var mainTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, mainTableName);
		AddTestFields(mainConnection, mainTableName);

		var nstTableName1 = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, nstTableName1);
		AddTestFields(mainConnection, nstTableName1);

		var nstTableName2 = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, nstTableName2);
		AddTestFields(mainConnection, nstTableName2);

		AddNstFields(mainConnection, mainTableName, nstTableName1, nstTableName2);

		var insertInfo1 = InsertData(connection, nstTableName1, "dummy data 1", null, true);
		var insertInfo2 = InsertData(connection, nstTableName2, "dummy data 2", null, true);

		AddMainData(connection, mainTableName, insertInfo1, insertInfo2, true);

		List<DataStoreQueryField> fields = new List<DataStoreQueryField>();
		DataStoreQueryField testField1 = new DataStoreQueryField("IntField");
		fields.Add(testField1);
		DataStoreQueryField testField2 = new DataStoreQueryField("IdFieldFromNst1.IntField");
		fields.Add(testField2);

		// test, second join works
		DataStoreQueryField testField3 = new DataStoreQueryField("IdFieldFromNst2.DoubleField");
		fields.Add(testField3);

		DataStoreQueryField opField1 = new DataStoreQueryField("IntField * (1 + IdFieldFromNst2.DoubleField * 0.01)", "OperationsField1");
		fields.Add(opField1);

		DataStoreQuery query = new DataStoreQuery(mainTableName, fields);
		var elements1 = connection.GetElements(query);

		DataStoreQueryField testField4 = new DataStoreQueryField("SUM(IntField)", "SumIntField");
		DataStoreQueryField testField5 = new DataStoreQueryField("AVG(IdFieldFromNst1.IntField)", "AverageIntField");
		DataStoreQueryField opField2 = new DataStoreQueryField("(SUM(IntField) - AVG(IdFieldFromNst2.IntField)) / 100", "OperationsField2");
		DataStoreQueryField opField3 = new DataStoreQueryField("(17 + 4) * (0+815)", "OperationsField3");

		fields = new List<DataStoreQueryField>();
		fields.Add(testField4);
		fields.Add(testField5);
		fields.Add(opField2);
		fields.Add(opField3);

		query = new DataStoreQuery(mainTableName, fields);
		var elements2 = connection.GetElements(query);

		// Test basic operations
		foreach (var dataStoreElement in elements1)
		{
			int intField = (int)dataStoreElement.Values["IntField"]!;
			double doubleFieldFromNst = (double)dataStoreElement.Values["IdFieldFromNst2.DoubleField"]!;
			double operationsField1 = (double)dataStoreElement.Values["OperationsField1"]!;
			double operationsField1Test = intField * (1 + doubleFieldFromNst * 0.01);
			Assert.Equal(operationsField1, operationsField1Test);
		}

		// Test basic operations with aggregate functions
		foreach (var dataStoreElement in elements2)
		{
			int operationsField3 = (int)dataStoreElement.Values["OperationsField3"]!;
			double operationsField3Test = (17 + 4) * (0 + 815);
			Assert.Equal(operationsField3, operationsField3Test);
		}
	}
	
	[Fact(DisplayName = "Two tables that contain the same column name are joined")]
	public void SameNameInVirtuallyReferencedTablesTest()
	{
		var connection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		var mainConnection = (IStorageConnection)DataStore.GetConnection(null);

		var mainTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, mainTableName);
		AddTestFields(mainConnection, mainTableName);

		var nstTableName1 = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, nstTableName1);
		AddTestFields(mainConnection, nstTableName1);

		var nstTableName2 = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, nstTableName2);
		AddTestFields(mainConnection, nstTableName2);

		AddNstFields(mainConnection, mainTableName, nstTableName1, nstTableName2);

		var insertInfo1 = InsertData(connection, nstTableName1, "dummy data 1", new Dictionary<string, object?>()
		{
			["StringField"] = "string field nst1"
		}, true);
		var insertInfo2 = InsertData(connection, nstTableName2, "dummy data 2", new Dictionary<string, object?>()
		{
			["StringField"] = "string field nst2"
		}, true);

		AddMainData(connection, mainTableName, insertInfo1, insertInfo2, true);

		List<DataStoreQueryField> fields = new List<DataStoreQueryField>();
		DataStoreQueryField testField2 = new DataStoreQueryField("IdFieldFromNst1.StringField");
		fields.Add(testField2);

		// test, second join works
		DataStoreQueryField testField3 = new DataStoreQueryField("IdFieldFromNst2.StringField");
		fields.Add(testField3);

		var query = new DataStoreQuery(mainTableName, fields);
		query.WithPaging(1);
		var elements = connection.GetElements(query);

		Assert.Single(elements);
		var element = elements.First();
		Assert.Equal("string field nst1", element.Values["IdFieldFromNst1.StringField"]);
		Assert.Equal("string field nst2", element.Values["IdFieldFromNst2.StringField"]);
	}
	
	[Fact(DisplayName = "Stacking operations for virtual columns")]
	public void StackingOperationsVirtualColumnsTest()
	{
		var connection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		var mainConnection = (IStorageConnection)DataStore.GetConnection(null);

		var mainTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, mainTableName);
		AddTestFields(mainConnection, mainTableName);

		var nstTableName1 = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, nstTableName1);
		AddTestFields(mainConnection, nstTableName1);

		var nstTableName2 = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, nstTableName2);
		AddTestFields(mainConnection, nstTableName2);

		AddNstFields(mainConnection, mainTableName, nstTableName1, nstTableName2);
		AddNstFields(mainConnection, nstTableName1, mainTableName, nstTableName2);

		var insertInfo2 = InsertData(connection, nstTableName2, "dummy data 2", null, true);
		var insertInfo1 = InsertData(connection, nstTableName1, "dummy data 1", new Dictionary<string, object?>()
		{
			["IdFieldFromNst2"] = insertInfo2.ElementId
		}, true);

		var mainData = AddMainData(connection, mainTableName, insertInfo1, insertInfo2, true);
		AddMainData(connection, nstTableName1, mainData, insertInfo2, true);

		
		List<DataStoreQueryField> fields = new List<DataStoreQueryField>();
		DataStoreQueryField testField1 = new DataStoreQueryField("IntField");
		fields.Add(testField1);
		DataStoreQueryField testField2 = new DataStoreQueryField("IdFieldFromNst1.IdFieldFromNst1.IntField");
		fields.Add(testField2);

		// test, second join works
		DataStoreQueryField testField3 = new DataStoreQueryField("IdFieldFromNst2.DoubleField");
		fields.Add(testField3);
		
		DataStoreQueryField testField6 = new DataStoreQueryField("IdFieldFromNst1.IdFieldFromNst2.IntField");
		fields.Add(testField6);

		DataStoreQueryField opField1 = new DataStoreQueryField("IntField * (1 + IdFieldFromNst1.IdFieldFromNst2.DoubleField * 0.01)", "OperationsField1");
		fields.Add(opField1);

		DataStoreQuery query = new DataStoreQuery(mainTableName, fields);
		var elements1 = connection.GetElements(query);

		DataStoreQueryField testField4 = new DataStoreQueryField("SUM(IntField)", "SumIntField");
		DataStoreQueryField testField5 = new DataStoreQueryField("AVG(IdFieldFromNst1.IdFieldFromNst2.IntField)", "AverageIntField");
		DataStoreQueryField opField2 = new DataStoreQueryField("(SUM(IntField) - AVG(IdFieldFromNst2.IntField)) / 100", "OperationsField2");

		fields = new List<DataStoreQueryField>();
		fields.Add(testField4);
		fields.Add(testField5);
		fields.Add(opField2);

		query = new DataStoreQuery(mainTableName, fields);
		var elements2 = connection.GetElements(query);
		
		query = new DataStoreQuery(nstTableName1, null);
		query.WithFilter(new QueryFilterGroup(new List<QueryFilter>()
		{
			new NotNullFilter(new QueryFilterField("IdFieldFromNst1.IdFieldFromNst2.DoubleField"))
		}))
		.WithOrderBy(new List<DataStoreElementSort>()
		{
			new DataStoreElementSort("IdFieldFromNst1.IdFieldFromNst2.DoubleField")
		});
		var elements3 = connection.GetElements(query);

		// Test basic operations
		foreach (var dataStoreElement in elements1)
		{
			int intField = (int)dataStoreElement.Values["IntField"]!;
			double doubleFieldFromNst = (double)dataStoreElement.Values["IdFieldFromNst2.DoubleField"]!;
			double operationsField1 = (double)dataStoreElement.Values["OperationsField1"]!;
			double operationsField1Test = intField * (1 + doubleFieldFromNst * 0.01);
			Assert.Equal(operationsField1Test, operationsField1);
		}

/*
select SUM(main."IntField") as "SumIntField", AVG(nst2."IntField") as "AverageIntField", (SUM(main."IntField") - AVG(nst1."IntField")) / 100 as "OperationsField2"
from "test_KDCSVOBSDXSNEBIPVANU" main
left join "test_TJDABUQCGUHQDBPNBKSQ" nst1 on main."IdFieldFromNst1" = nst1."Id"
left join "test_FAJGBSBMAULNMAVAABRA" nst2 on nst1."IdFieldFromNst2" = nst2."Id"
*/
		// Test basic operations with aggregate functions
		foreach (var dataStoreElement in elements2)
		{
			Assert.Equal(75, long.Parse(dataStoreElement.Values["SumIntField"]!.ToString()!));
			Assert.Equal(25, (double)dataStoreElement.Values["AverageIntField"]!);
			Assert.Equal(0.5, dataStoreElement.Values["OperationsField2"]);
		}
		
/*
select nst1.*
from "test_TJDABUQCGUHQDBPNBKSQ" nst1
left join "test_KDCSVOBSDXSNEBIPVANU" main on nst1."IdFieldFromNst1" = main."Id"
left join "test_FAJGBSBMAULNMAVAABRA" nst2 on main."IdFieldFromNst2" = nst2."Id"
where
nst2."DoubleField" is not null
*/
		Assert.Equal(3, elements3.Count);
	}
	
	/// <summary>
	/// Tests unique columns
	/// </summary>
	[Fact(DisplayName = "Assert correct behaviour of unique columns.")]
	public void UniqueColumnIndexTest()
	{
		// use 2 different connections to check that changes are persisted 
		var connection = (IStorageConnection)DataStore.GetConnection(null);
		var userConnection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		var randomTableName = "test_" + RandomDataHelper.Random.NextString(20, false);

		// test create
		var config = new StorageDataSourceConfig(randomTableName)
		{
			StoreRevisions = false
		};
		var definition = connection.CreateDataSource(config);

		var sfd1 = new StorageField("testcol1")
		{
			Nullable = false,
			Type = DataStoreFieldType.String,
			Unique = true,
			Length = 99,
		};
		
		var sfd2 = new StorageField("testcol2")
		{
			Nullable = true,
			Type = DataStoreFieldType.Integer,
			Unique = false,
		};

		var sfd3 = new StorageField("testcol3")
		{
			Nullable = false,
			Type = DataStoreFieldType.Integer,
			Unique = true,
		};
		
		connection.CreateField(definition.Name, sfd1);
		connection.CreateField(definition.Name, sfd2);
		connection.CreateField(definition.Name, sfd3);
		
		connection.GetDataSource(randomTableName);

		userConnection.CreateElement(randomTableName, new DataStoreElementData(new Dictionary<string, object?>()
		{
			["testcol1"] = "stuff",
			["testcol2"] = 5,
			["testcol3"] = 6
		}, new List<string>() { "test" }), new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 11, "me"));
		
		Assert.Throws<DataStoreOperationException>(() => userConnection.CreateElement(randomTableName, new DataStoreElementData(new Dictionary<string, object?>()
		{
			["testcol1"] = "stuff",
			["testcol2"] = 5,
			["testcol3"] = 7
		}, new List<string>() { "test" }), new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 11, "me")));
		
		Assert.Throws<DataStoreOperationException>(() => userConnection.CreateElement(randomTableName, new DataStoreElementData(new Dictionary<string, object?>()
		{
			["testcol1"] = "stuff1",
			["testcol2"] = 5,
			["testcol3"] = 6
		}, new List<string>() { "test" }), new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 11, "me")));

		var entryToRemove = userConnection.CreateElement(randomTableName, new DataStoreElementData(new Dictionary<string, object?>()
		{
			["testcol1"] = "stuff1",
			["testcol2"] = 5,
			["testcol3"] = 7
		}, new List<string>() { "test" }), new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 11, "me"));


		Assert.Throws<DataStoreOperationException>(() => userConnection.CreateElements(randomTableName,
																					   [
																						   new DataStoreElementData(new Dictionary<string, object?>()
																						   {
																							   ["testcol1"] = "stuff2",
																							   ["testcol2"] = 5,
																							   ["testcol3"] = 8
																						   }, new List<string>() { "test" }),
																						   new DataStoreElementData(new Dictionary<string, object?>()
																						   {
																							   ["testcol1"] = "stuff3",
																							   ["testcol2"] = 5,
																							   ["testcol3"] = 9
																						   }, new List<string>() { "test" }),
																						   new DataStoreElementData(new Dictionary<string, object?>()
																						   {
																							   ["testcol1"] = "stuff2",
																							   ["testcol2"] = 5,
																							   ["testcol3"] = 10
																						   }, new List<string>() { "test" }),
																					   ],
																					   new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 11, "me")));
		
		Assert.Empty(userConnection.GetElements(new StorageQuery(randomTableName, null, null)
													.WithFilter(new QueryFilterGroup().AddFilter(new EqualsFilter(new QueryFilterField("testcol1"), "stuff2")))));
		
		// test for deleted duplicates - delete entry and just create the same entry again
		userConnection.DeleteElement(randomTableName, entryToRemove.ElementId, new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 12, "myself"));
		userConnection.CreateElement(randomTableName, new DataStoreElementData(new Dictionary<string, object?>()
		{
			["testcol1"] = "stuff1",
			["testcol2"] = 5,
			["testcol3"] = 7
		}, new List<string>() { "test" }), new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 11, "me"));
		
		// disable unique
		sfd1 = new StorageField("testcol1")
		{
			Nullable = false,
			Type = DataStoreFieldType.String,
			Unique = false,
			Length = 99,
		};

		connection.UpdateField(randomTableName, sfd1);
		Thread.Sleep(1000);
		
		userConnection.CreateElement(randomTableName, new DataStoreElementData(new Dictionary<string, object?>()
		{
			["testcol1"] = "stuff",
			["testcol2"] = 5,
			["testcol3"] = 11
		}, new List<string>() { "test" }), new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 11, "me"));
		
		// enable unique again - should fail
		sfd1 = new StorageField("testcol1")
		{
			Nullable = false,
			Type = DataStoreFieldType.String,
			Unique = true,
			Length = 99,
		};

		// this will fail for the customer db because of duplicate entries
		Assert.Throws<DataStoreOperationException>(() => userConnection.UpdateField(randomTableName, sfd1));
	}

	#region field translation tests

	[Fact(DisplayName = "test translatable fields")]
	public virtual void TranslatableFieldsTest()
	{
		Guid testuserId = Guid.NewGuid();
		var connection = (IStorageConnection)DataStore.GetConnection(new DataStoreAuthentication("testuser", testuserId, new List<string>{"testgroup"}));
		var dataConnection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, new DataStoreAuthentication("testuser", testuserId, new List<string>{"testgroup"}));

		var dataConnectionDe = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, new DataStoreAuthentication("testuser", testuserId, new List<string>{"testgroup"})
		{
			Language = "de"
		});

		var dataConnectionIt = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, new DataStoreAuthentication("testuser", testuserId, new List<string>{"testgroup"})
		{
			Language = "it"
		});

		var dataConnectionEs = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, new DataStoreAuthentication("testuser", testuserId, new List<string>{"testgroup"})
		{
			Language = "es"
		});

		var dataSourceName = RandomDataHelper.Random.NextString(21, false);
		connection.CreateDataSource(new StorageDataSourceConfig(dataSourceName));

		string fieldName = "TranslatableField";

		// fail on no languages supplied
		Assert.Throws<DataStoreOperationException>(() => connection.CreateField(dataSourceName, new StorageFieldConfig(fieldName)
		{
			Translatable = true,
			Length = 255
		}));

		// only normal string & text fields can be translatable
		Assert.Throws<DataStoreConfigurationException>(() => connection.CreateField(dataSourceName, new StorageFieldConfig(fieldName)
		{
			MultiValue = true,
			Translatable = true,
			Languages = new List<string>() { "en", "de" },
			Length = 255
		}));

		// only normal string & text fields can be translatable
		Assert.Throws<DataStoreOperationException>(() => connection.CreateField(dataSourceName, new StorageFieldConfig(fieldName)
		{
			Type = DataStoreFieldType.Integer,
			Translatable = true,
			Languages = new List<string>() { "en", "de" },
			Length = 255
		}));

		// only normal string & text fields can be translatable
		Assert.Throws<DataStoreOperationException>(() => connection.CreateField(dataSourceName, new StorageFieldConfig(fieldName)
		{
			Type = DataStoreFieldType.Date,
			Translatable = true,
			Languages = new List<string>() { "en", "de" },
			Length = 255
		}));

		// dont allow double '__' in field names. they are reserved for system fields. 
		Assert.Throws<DataStoreOperationException>(() => connection.CreateField(dataSourceName, new StorageFieldConfig($"{fieldName}__en")));
		Assert.Throws<DataStoreOperationException>(() => connection.CreateField(dataSourceName, new StorageFieldConfig("Tran__slatableFielden")));

		var field = connection.CreateField(dataSourceName, new StorageFieldConfig(fieldName)
		{
			Translatable = true,
			Languages = { "de", "en", "fr", "it" },
			Length = 255
		});

		Assert.False(FieldExistsInDb(dataSourceName, fieldName), $"{fieldName} should not be saved to db, as it is a 'virtual' field");
		Assert.True(FieldExistsInDb(dataSourceName, $"{fieldName}__de"));
		Assert.True(FieldExistsInDb(dataSourceName, $"{fieldName}__en"));
		Assert.True(FieldExistsInDb(dataSourceName, $"{fieldName}__fr"));
		Assert.True(FieldExistsInDb(dataSourceName, $"{fieldName}__it"));

		field.Languages.Remove("fr");
		connection.UpdateField(dataSourceName, field);

		Assert.False(FieldExistsInDb(dataSourceName, fieldName), $"{fieldName} should not be saved to db, as it is a 'virtual' field");
		Assert.True(FieldExistsInDb(dataSourceName, $"{fieldName}__de"));
		Assert.True(FieldExistsInDb(dataSourceName, $"{fieldName}__en"));
		Assert.False(FieldExistsInDb(dataSourceName, $"{fieldName}__fr"));
		Assert.True(FieldExistsInDb(dataSourceName, $"{fieldName}__it"));

		var ds = connection.GetDataSource(dataSourceName);
		Assert.Single(ds!.Fields, it => it.Name == fieldName);
		Assert.Single(ds.Fields, it => it.Name == $"{fieldName}__de");
		Assert.Single(ds.Fields, it => it.Name == $"{fieldName}__en");
		Assert.DoesNotContain(ds.Fields, it => it.Name == $"{fieldName}__fr");
		Assert.Single(ds.Fields, it => it.Name == $"{fieldName}__it");

		var mainTranslationField = ds.Fields.First(it => it.Name == fieldName);
		Assert.True(mainTranslationField.Readonly);
		Assert.False(((StorageField)mainTranslationField).SystemField);

		var translationFieldDe = ds.Fields.First(it => it.Name == $"{fieldName}__de");
		Assert.False(translationFieldDe.Readonly);
		Assert.True(((StorageField)translationFieldDe).SystemField);

		var created = dataConnection.CreateElement(dataSourceName, new DataStoreElementData(new Dictionary<string, object?>()
		{
			{
				$"{fieldName}__de", "es ist eine falle!!!"
			},
			{
				$"{fieldName}__en", "its a trap!!!"
			},
			{
				$"{fieldName}__it", ""
			},
		}, new List<string>{"testgroup"}), new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "testuser"));

		var eles = dataConnection.GetElements(new StorageQuery(dataSourceName, null, null));
		Assert.Single(eles);
		Assert.Equal("its a trap!!!", eles.First().Values[fieldName]);
		Assert.Equal("es ist eine falle!!!", eles.First().Values[$"{fieldName}__de"]);
		Assert.Equal("its a trap!!!", eles.First().Values[$"{fieldName}__en"]);
		Assert.False(eles.First().Values.ContainsKey($"{fieldName}__fr"));
		Assert.Null(eles.First().Values[$"{fieldName}__it"]);

		connection.UpdateField(dataSourceName, new StorageField(fieldName)
		{
			Translatable = false,
			Languages = { "de", "en", "fr", "it" },
			Length = 255
		});

		Assert.True(FieldExistsInDb(dataSourceName, fieldName));
		Assert.False(FieldExistsInDb(dataSourceName, $"{fieldName}__de"));
		Assert.False(FieldExistsInDb(dataSourceName, $"{fieldName}__en"));
		Assert.False(FieldExistsInDb(dataSourceName, $"{fieldName}__fr"));
		Assert.False(FieldExistsInDb(dataSourceName, $"{fieldName}__it"));

		ds = connection.GetDataSource(dataSourceName);
		Assert.Single(ds!.Fields, it => it.Name == fieldName);
		Assert.DoesNotContain(ds.Fields, it => it.Name == $"{fieldName}__de");
		Assert.DoesNotContain(ds.Fields, it => it.Name == $"{fieldName}__en");
		Assert.DoesNotContain(ds.Fields, it => it.Name == $"{fieldName}__fr");
		Assert.DoesNotContain(ds.Fields, it => it.Name == $"{fieldName}__it");

		eles = dataConnection.GetElements(new StorageQuery(dataSourceName, null, null));
		Assert.Single(eles);
		Assert.Equal("its a trap!!!", eles.First().Values[fieldName]);

		// and back to translatable
		connection.UpdateField(dataSourceName, new StorageField(fieldName)
		{
			Translatable = true,
			Languages = { "de", "en", "fr", "it" },
			Length = 255
		});

		Assert.False(FieldExistsInDb(dataSourceName, fieldName));
		Assert.True(FieldExistsInDb(dataSourceName, $"{fieldName}__de"));
		Assert.True(FieldExistsInDb(dataSourceName, $"{fieldName}__en"));
		Assert.True(FieldExistsInDb(dataSourceName, $"{fieldName}__fr"));
		Assert.True(FieldExistsInDb(dataSourceName, $"{fieldName}__it"));

		eles = dataConnection.GetElements(new StorageQuery(dataSourceName, null, null));
		Assert.Single(eles);
		Assert.Equal("its a trap!!!", eles.First().Values[fieldName]);
		Assert.Null(eles.First().Values[$"{fieldName}__de"]);
		Assert.Equal("its a trap!!!", eles.First().Values[$"{fieldName}__en"]);
		Assert.Null(eles.First().Values[$"{fieldName}__fr"]);
		Assert.Null(eles.First().Values[$"{fieldName}__it"]);

		// check select linked data
		var dataSourceLink = connection.CreateDataSource(new StorageDataSourceConfig(RandomDataHelper.Random.NextString(21, false)));
		connection.CreateField(dataSourceLink.Name, new StorageFieldConfig("stringtest")
		{
			Length = 255
		});
		connection.CreateField(dataSourceLink.Name, new StorageFieldConfig("reference")
		{
			Nullable = true,
			LookupSource = dataSourceName
		});

		dataConnection.UpdateElement(dataSourceName, new DataStoreElementData(created.ElementId, new Dictionary<string, object?>()
		{
			{
				$"{fieldName}__de", "es ist eine falle!!!"
			},
			{
				$"{fieldName}__en", "its a trap!!!"
			},
			{
				$"{fieldName}__it", ""
			},
		}), new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "testuser"));

		var created1 = dataConnection.CreateElement(dataSourceLink.Name, new DataStoreElementData(new Dictionary<string, object?>()
		{
			{
				"reference", created.ElementId
			},
			{
				"stringtest", "testt"
			}
		}, new List<string>{"testgroup"}), new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "testuser"));

		var fieldNameRef = "reference." + fieldName;
		var lookupFields = new List<DataStoreQueryField>()
		{
			new DataStoreQueryField(fieldNameRef),
			new DataStoreQueryField(fieldNameRef + "__de"),
			new DataStoreQueryField(fieldNameRef + "__en"),
			new DataStoreQueryField(fieldNameRef + "__fr"),
			new DataStoreQueryField(fieldNameRef + "__it"),
		};
		var ele = dataConnection.GetElement(dataSourceLink.Name, created1.ElementId, lookupFields);
		Assert.Equal("its a trap!!!", ele.Values[fieldNameRef]);
		Assert.Equal("es ist eine falle!!!", ele.Values[$"{fieldNameRef}__de"]);
		Assert.Equal("its a trap!!!", ele.Values[$"{fieldNameRef}__en"]);
		Assert.Null(ele.Values[$"{fieldNameRef}__fr"]);
		Assert.Null(ele.Values[$"{fieldNameRef}__it"]);

		ele = dataConnectionDe.GetElement(dataSourceLink.Name, created1.ElementId, lookupFields);
		Assert.Equal("es ist eine falle!!!", ele.Values[fieldNameRef]);
		Assert.Equal("es ist eine falle!!!", ele.Values[$"{fieldNameRef}__de"]);
		Assert.Equal("its a trap!!!", ele.Values[$"{fieldNameRef}__en"]);
		Assert.Null(ele.Values[$"{fieldNameRef}__fr"]);
		Assert.Null(ele.Values[$"{fieldNameRef}__it"]);

		ele = dataConnectionIt.GetElement(dataSourceLink.Name, created1.ElementId, lookupFields);
		Assert.Equal("its a trap!!!", ele.Values[fieldNameRef]);
		Assert.Equal("es ist eine falle!!!", ele.Values[$"{fieldNameRef}__de"]);
		Assert.Equal("its a trap!!!", ele.Values[$"{fieldNameRef}__en"]);
		Assert.Null(ele.Values[$"{fieldNameRef}__fr"]);
		Assert.Null(ele.Values[$"{fieldNameRef}__it"]);

		ele = dataConnectionEs.GetElement(dataSourceLink.Name, created1.ElementId, lookupFields);
		Assert.Equal("its a trap!!!", ele.Values[fieldNameRef]);
		Assert.Equal("es ist eine falle!!!", ele.Values[$"{fieldNameRef}__de"]);
		Assert.Equal("its a trap!!!", ele.Values[$"{fieldNameRef}__en"]);
		Assert.Null(ele.Values[$"{fieldNameRef}__fr"]);
		Assert.Null(ele.Values[$"{fieldNameRef}__it"]);

		var elements = dataConnection.GetElements(
			new StorageQuery(dataSourceLink.Name, null, null).WithFilter(
				new QueryFilterGroup().AddFilter(new LikeFilter(new QueryFilterField(fieldNameRef), "%trap%"))));
		Assert.Single(elements);

		elements = dataConnectionDe.GetElements(
			new StorageQuery(dataSourceLink.Name, null, null).WithFilter(
				new QueryFilterGroup().AddFilter(new LikeFilter(new QueryFilterField(fieldNameRef), "%trap%"))));
		Assert.Empty(elements);

		elements = dataConnectionIt.GetElements(
			new StorageQuery(dataSourceLink.Name, null, null).WithFilter(
				new QueryFilterGroup().AddFilter(new LikeFilter(new QueryFilterField(fieldNameRef), "%trap%"))));
		Assert.Single(elements);

		elements = dataConnectionEs.GetElements(
			new StorageQuery(dataSourceLink.Name, null, null).WithFilter(
				new QueryFilterGroup().AddFilter(new LikeFilter(new QueryFilterField(fieldNameRef), "%trap%"))));
		Assert.Single(elements);

		elements = dataConnectionDe.GetElements(
			new StorageQuery(dataSourceLink.Name, null, null).WithFilter(
				new QueryFilterGroup().AddFilter(new LikeFilter(new QueryFilterField(fieldNameRef), "%falle%"))));
		Assert.Single(elements);

		elements = dataConnection.GetElements(
			new StorageQuery(dataSourceLink.Name, null, null).WithFilter(
				new QueryFilterGroup().AddFilter(new LikeFilter(new QueryFilterField(fieldNameRef), "%falle%"))));
		Assert.Empty(elements);

		elements = dataConnectionIt.GetElements(
			new StorageQuery(dataSourceLink.Name, null, null).WithFilter(
				new QueryFilterGroup().AddFilter(new LikeFilter(new QueryFilterField(fieldNameRef), "%falle%"))));
		Assert.Empty(elements);

		elements = dataConnectionEs.GetElements(
			new StorageQuery(dataSourceLink.Name, null, null).WithFilter(
				new QueryFilterGroup().AddFilter(new LikeFilter(new QueryFilterField(fieldNameRef), "%falle%"))));
		Assert.Empty(elements);

		// check field default language
		var context = (StorageContext)((IStorage)DataStore).GetContexts().First();
		context.Config["language"] = "de";
		connection.UpdateContext(context);
		
		// for the moment, we have to reload the connection in order to get the correct context - https://gitlab.com/dev6283740/levelbuild/WebApp/-/issues/209
		dataConnectionEs = (IStorageConnection)DataStore.GetConnection(context.Identifier, new DataStoreAuthentication("testuser", testuserId, new List<string>{"testgroup"})
		{
			Language = "es"
		});

		ele = dataConnectionEs.GetElement(dataSourceLink.Name, created1.ElementId, lookupFields);
		Assert.Equal("es ist eine falle!!!", ele.Values[fieldNameRef]);
		Assert.Equal("es ist eine falle!!!", ele.Values[$"{fieldNameRef}__de"]);
		Assert.Equal("its a trap!!!", ele.Values[$"{fieldNameRef}__en"]);
		Assert.Null(ele.Values[$"{fieldNameRef}__fr"]);
		Assert.Null(ele.Values[$"{fieldNameRef}__it"]);

		elements = dataConnectionEs.GetElements(
			new StorageQuery(dataSourceLink.Name, null, null).WithFilter(
				new QueryFilterGroup().AddFilter(new LikeFilter(new QueryFilterField(fieldNameRef), "%trap%"))));
		Assert.Empty(elements);

		elements = dataConnectionEs.GetElements(
			new StorageQuery(dataSourceLink.Name, null, null).WithFilter(
				new QueryFilterGroup().AddFilter(new LikeFilter(new QueryFilterField(fieldNameRef), "%falle%"))));
		Assert.Single(elements);

		connection.RemoveField(dataSourceName, fieldName);
		Assert.False(FieldExistsInDb(dataSourceName, fieldName));
		Assert.False(FieldExistsInDb(dataSourceName, $"{fieldName}__de"));
		Assert.False(FieldExistsInDb(dataSourceName, $"{fieldName}__en"));
		Assert.False(FieldExistsInDb(dataSourceName, $"{fieldName}__fr"));
		Assert.False(FieldExistsInDb(dataSourceName, $"{fieldName}__it"));
		
		context.Config["language"] = "en";
		connection.UpdateContext(context);
	}

	#endregion

	[Fact(DisplayName = "Long table and column names")]
	public void TestLongTableAndColumnNames()
	{
		var context = (StorageContext)DataStore.GetContexts().First();
		var mainConnection = (IStorageConnection)DataStore.GetConnection(null);
		var connection = (IStorageConnection)DataStore.GetConnection(context.Identifier, TestAuth);
		Assert.Throws<DataStoreOperationException>(
			() => mainConnection.CreateDataSource(
				new StorageDataSourceConfig("This_Is_A_Really_Long_Database_Table_Name_And_it_is_really_hard_to_make_it_long_enough")));
		
		var tableName = "test_" + RandomDataHelper.Random.NextString(50, false);
		var referencedTableName = "test_" + RandomDataHelper.Random.NextString(55, false);
		CreateDataSource(mainConnection, tableName);
		CreateDataSource(mainConnection, referencedTableName);
		
		Assert.Throws<DataStoreOperationException>(() => mainConnection.CreateField(tableName, new StorageFieldConfig("This_Is_A_Really_Long_Database_Column_Name_And_it_is_really_hard_to_make_it_long_enough")
		{
			Length = 50
		}));

		var longFieldName = "Long_TestCol" + RandomDataHelper.Random.NextString(50, false);
		mainConnection.CreateField(tableName, new StorageFieldConfig(longFieldName)
		{
			Length = 50
		});
		mainConnection.CreateField(referencedTableName, new StorageFieldConfig("reallyReallyLongTestFieldName")
		{
			Length = 50
		});
		mainConnection.CreateField(tableName, new StorageFieldConfig("reallyLongReferencedTableForeignKey")
		{
			LookupSource = referencedTableName
		});
		
		var foreignElement = connection.CreateElement(referencedTableName, new DataStoreElementData(new Dictionary<string, object?>()
		{
			["reallyReallyLongTestFieldName"] = "hello there!"
		}, TestAuth.Groups!), new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "me"));

		var dataSource = mainConnection.GetDataSource(tableName)!;
		var randomData = RandomDataHelper.GetDataSourceData(dataSource, new Dictionary<string, object?>()
		{
			["reallyLongReferencedTableForeignKey"] = foreignElement.ElementId
		});
		var newElement = connection.CreateElement(tableName, new DataStoreElementData(randomData, TestAuth.Groups!), new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "me"));
		var elements = connection.GetElements(new DataStoreQuery(tableName, new List<DataStoreQueryField>()
		{
			new DataStoreQueryField("reallyLongReferencedTableForeignKey.reallyReallyLongTestFieldName"),
			new DataStoreQueryField(longFieldName)
		}));
		var elements1 = connection.GetElements(new DataStoreQuery(tableName, new List<DataStoreQueryField>()
		{
			new DataStoreQueryField(longFieldName,  longFieldName + "_this_will_bring_you_over_63_chars"),
			new DataStoreQueryField("reallyLongReferencedTableForeignKey.reallyReallyLongTestFieldName", "reallyLongReferencedTableForeignKey.reallyReallyLongTestFieldName")
		}));
		
		Assert.Equal(randomData[longFieldName], newElement.ElementData!.Values[longFieldName]);
		Assert.Single(elements);
		Assert.Single(elements1);
		Assert.Equal(randomData[longFieldName], elements.First().Values[longFieldName]);
		Assert.Equal("hello there!", elements.First().Values["reallyLongReferencedTableForeignKey.reallyReallyLongTestFieldName"]);
		Assert.Equal(randomData[longFieldName], elements1.First().Values[longFieldName + "_this_will_bring_you_over_63_chars"]);
		Assert.Equal("hello there!", elements1.First().Values["reallyLongReferencedTableForeignKey.reallyReallyLongTestFieldName"]);
	}
	
	[Fact(DisplayName = "Add fulltext search to datasource")]
	public void FulltextsearchTest()
	{
		var context = (StorageContext)DataStore.GetContexts().First();
		var connection = (IStorageConnection)DataStore.GetConnection(context.Identifier, TestAuth);
		var compareConnection = (IStorageConnection)DataStore.GetConnection(context.Identifier, TestAuth);

		// per default, fulltext search should be disabled.
		var nonFulltextsearchTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		var table2 = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(connection, nonFulltextsearchTableName);
		CreateDataSource(connection, table2);
		AddTestFields(connection, nonFulltextsearchTableName);
		AddTestFields(connection, table2);
		
		var nonFulltextsearchDataSource = (StorageDataSource)compareConnection.GetDataSource(nonFulltextsearchTableName)!;
		Assert.False(nonFulltextsearchDataSource.FulltextSearch);

		QueryFilterGroup filters = new QueryFilterGroup();
		filters.AddFilter(new FulltextSearchFilter("test2"));
		DataStoreQuery ftsQuery = new DataStoreQuery(nonFulltextsearchTableName, null).WithFilter(filters);
		Assert.Throws<DataStoreQueryException>(() => connection.GetElements(ftsQuery));


		// check fulltext search is supported across schema changes and data modifications
		var mainTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		StorageDataSourceConfig config = new StorageDataSourceConfig(mainTableName)
		{
			StoreRevisions = false,
			FulltextSearch = true
		};
		var mainDataSource = connection.CreateDataSource(config);
		AddTestFields(connection, mainTableName);
		var dataSource = (StorageDataSource)compareConnection.GetDataSource(mainTableName)!;
		Assert.True(dataSource.FulltextSearch);

		var info = InsertData(connection, table2, "dummy data 1",
				   new Dictionary<string, object?>()
				   {
					   ["DateTimeField"] = DateTime.SpecifyKind(new DateTime(2025, 05, 14), DateTimeKind.Utc)
				   }, true);

		connection.CreateField(mainTableName, new StorageFieldConfig("referencedField") { LookupSource = table2 });
		var insertInfo1 = InsertData(connection, mainTableName, "dummy data 1", new Dictionary<string, object?>()
		{
			["referencedField"] = info.ElementId
		}, true);


		Thread.Sleep(2500);
		QueryFilterGroup filters1 = new QueryFilterGroup();
		filters1.AddFilter(new FulltextSearchFilter(insertInfo1.ElementData!.Values["StringField"]!.ToString()!.Substring(0, 5)));
		DataStoreQuery ftsQuery1 = new DataStoreQuery(mainTableName, null).WithFilter(filters1);
		var ftsResult = connection.GetElements(ftsQuery1);
		Assert.Single(ftsResult);

		QueryFilterGroup filters2 = new QueryFilterGroup();
		filters2.AddFilter(new FulltextSearchFilter("thisvalueshouldneverexist"));
		DataStoreQuery ftsQuery2 = new DataStoreQuery(mainTableName, null).WithFilter(filters2);
		var ftsResult2 = connection.GetElements(ftsQuery2);
		Assert.Empty(ftsResult2);
		
		QueryFilterGroup filters3 = new QueryFilterGroup();
		filters3.AddFilter(new FulltextSearchFilter("05/14/25"));
		DataStoreQuery ftsQuery3 = new DataStoreQuery(mainTableName, [new DataStoreQueryField("referencedField.DateTimeField")]).WithFilter(filters3);
		var ftsResult3 = connection.GetElements(ftsQuery3);
		Assert.Single(ftsResult3);
		
		QueryFilterGroup filters4 = new QueryFilterGroup();
		filters4.AddFilter(new FulltextSearchFilter("05/14/22"));
		DataStoreQuery ftsQuery4 = new DataStoreQuery(mainTableName, [new DataStoreQueryField("referencedField.DateTimeField")]).WithFilter(filters4);
		var ftsResult4 = connection.GetElements(ftsQuery4);
		Assert.Empty(ftsResult4);

		mainDataSource.FulltextSearch = false;
		connection.UpdateDataSource(mainDataSource);
		AssertFulltextResourcesCleared(mainDataSource);
	}

	[Fact(DisplayName = "Fulltext search suggestions elastic")]
	public void SuggestionsTest()
	{
		var context = (StorageContext)DataStore.GetContexts().First();
		var connection = (IStorageConnection)DataStore.GetConnection(context.Identifier, TestAuth);
		var compareConnection = (IStorageConnection)DataStore.GetConnection(context.Identifier, TestAuth);

		// per default, fulltext search should be disabled.
		var nonFulltextsearchTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(connection, nonFulltextsearchTableName);
		AddTestFields(connection, nonFulltextsearchTableName);

		var filter = new FulltextSearchFilter("test2");
		var ftsQuery = new StorageSuggestionQuery(nonFulltextsearchTableName, filter);
		Assert.Throws<DataStoreQueryException>(() => connection.GetSuggestions(ftsQuery));


		// check fulltext search is supported across schema changes and data modifications
		var mainTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		StorageDataSourceConfig config = new StorageDataSourceConfig(mainTableName)
		{
			StoreRevisions = false,
			FulltextSearch = true
		};
		var mainDataSource = connection.CreateDataSource(config);
		AddTestFields(connection, mainTableName);
		var dataSource = (StorageDataSource)compareConnection.GetDataSource(mainTableName)!;
		Assert.True(dataSource.FulltextSearch);
		var insertInfo1 = InsertData(connection, mainTableName, "dummy data 1", null, true);

		Thread.Sleep(2500);

		var ftsFilter = new FulltextSearchFilter(insertInfo1.ElementData!.Values["StringField"]!.ToString()!.Substring(0, 5));
		var ftsQuery1 = new StorageSuggestionQuery(mainTableName, ftsFilter);
		var ftsResult = connection.GetSuggestions(ftsQuery1);
		Assert.NotEmpty(ftsResult);

		var filters2 = new FulltextSearchFilter("thisvalueshouldneverexist");
		var ftsQuery2 = new StorageSuggestionQuery(mainTableName, filters2);
		var ftsResult2 = connection.GetSuggestions(ftsQuery2);
		Assert.Empty(ftsResult2);

		mainDataSource.FulltextSearch = false;
		connection.UpdateDataSource(mainDataSource);
		AssertFulltextResourcesCleared(mainDataSource);
	}

	[Fact(DisplayName = "Fulltext search fullsync elastic")]
	public void FulltextSearchFullSync()
	{
		var context = (StorageContext)DataStore.GetContexts().First();
		var connection = (IStorageConnection)DataStore.GetConnection(context.Identifier, TestAuth);
		var compareConnection = (IStorageConnection)DataStore.GetConnection(context.Identifier, TestAuth);
	
		// check fulltext search is supported across schema changes and data modifications
		var mainTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		StorageDataSourceConfig config = new StorageDataSourceConfig(mainTableName)
		{
			StoreRevisions = false,
			FulltextSearch = false
		};
		connection.CreateDataSource(config);
		AddTestFields(connection, mainTableName);
		var dataSource = (StorageDataSource)compareConnection.GetDataSource(mainTableName)!;
		Assert.False(dataSource.FulltextSearch);
		var insertInfo1 = InsertData(connection, mainTableName, "dummy data 1", null, true);
		
		StorageDataSourceConfig config1 = new StorageDataSourceConfig(mainTableName)
		{
			StoreRevisions = false,
			FulltextSearch = true
		};
		dataSource = connection.UpdateDataSource(config1);
		Assert.True(dataSource.FulltextSearch);
		Thread.Sleep(2500);

		var query = new StorageSuggestionQuery(dataSource.Name,
											   new FulltextSearchFilter(insertInfo1.ElementData!.Values["StringField"]!.ToString()!.Substring(0, 5)));
		var ftsResult = connection.GetSuggestions(query);
		Assert.NotEmpty(ftsResult);
	}

	[Fact(DisplayName = "Fulltext search elastic indexing")]
	public void FulltextSearchIndexing()
	{
		var context = (StorageContext)DataStore.GetContexts().First();
		var connection = (IStorageConnection)DataStore.GetConnection(context.Identifier, TestAuth);
		var compareConnection = (IStorageConnection)DataStore.GetConnection(context.Identifier, TestAuth);

		// check fulltext search is supported across schema changes and data modifications
		var mainTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		StorageDataSourceConfig config = new StorageDataSourceConfig(mainTableName)
		{
			StoreRevisions = false,
			FulltextSearch = false
		};
		connection.CreateDataSource(config);
		AddTestFields(connection, mainTableName, true);
		var dataSource = (StorageDataSource)compareConnection.GetDataSource(mainTableName)!;
		Assert.False(dataSource.FulltextSearch);
		var insertInfo1 = InsertData(connection, mainTableName, "dummy data 1", new Dictionary<string, object?>()
		{
			{
				"FulltextField", "testapplication or anything else"
			}
		}, true);

		StorageDataSourceConfig config1 = new StorageDataSourceConfig(mainTableName)
		{
			StoreRevisions = false,
			FulltextSearch = true
		};
		dataSource = connection.UpdateDataSource(config1);
		Assert.True(dataSource.FulltextSearch);
		Thread.Sleep(2500);

		var query = new StorageSuggestionQuery(dataSource.Name,
											   new FulltextSearchFilter(insertInfo1.ElementData!.Values["StringField"]!.ToString()!.Substring(0, 5)));
		
		var ftsResult = connection.GetSuggestions(query);
		for (int i = 0; i < 5 && ftsResult.Count == 0; i++)
		{
			Thread.Sleep(1000);
			ftsResult = connection.GetSuggestions(query);
		}
		
		Assert.NotEmpty(ftsResult);
	}
	
	[Fact(DisplayName = "Fulltext search with file content")]
	public void FileFulltextTest()
	{
		var context = (StorageContext)DataStore.GetContexts().First();
		var connection = (IStorageConnection)DataStore.GetConnection(context.Identifier, TestAuth);
		var compareConnection = (IStorageConnection)DataStore.GetConnection(context.Identifier, TestAuth);

		// check fulltext search is supported across schema changes and data modifications
		var mainTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		StorageDataSourceConfig config = new StorageDataSourceConfig(mainTableName)
		{
			StoreRevisions = false,
			FulltextSearch = true
		};
		connection.CreateDataSource(config);
		AddTestFields(connection, mainTableName);
		var dataSource = (StorageDataSource)compareConnection.GetDataSource(mainTableName)!;
		Assert.True(dataSource.FulltextSearch);
		InsertData(connection, mainTableName, "dummy data 1", null, true, true);

		Thread.Sleep(2500);

		// file contains teersandverwiegung
		var ftsFilter = new FulltextSearchFilter("teer");
		var ftsQuery1 = new StorageSuggestionQuery(mainTableName, ftsFilter);
		var suggestions = connection.GetSuggestions(ftsQuery1);
		Assert.NotEmpty(suggestions);
		
		var elements = connection.GetElements(new StorageQuery(mainTableName, null, new Dictionary<string, QueryFilterGroup>()
		{
			{ "test", new QueryFilterGroup(new List<QueryFilter>()
			{
				new FulltextSearchFilter("teersandverwiegung")
			}) }
		}));
		
		Assert.Single(elements);
	}

	[Fact(DisplayName = "Multivalue Lookup field CRUD")]
	public void CrudMultivalueLookupFieldTest()
	{
		var context = (StorageContext)DataStore.GetContexts().First();
		var mainConnection = (IStorageConnection)DataStore.GetConnection(null);
		var connection = (IStorageConnection)DataStore.GetConnection(context.Identifier, TestAuth);
		var mainTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		var referenceTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		
		CreateDataSource(mainConnection, mainTableName);
		CreateDataSource(mainConnection, referenceTableName);

		var field = mainConnection.CreateField(mainTableName, new StorageFieldConfig("MultiValueLookup")
		{
			LookupSource = referenceTableName,
			MultiValue = true
		});

		var fields = mainConnection.GetDataSource(mainTableName)!;
		mainConnection.RemoveField(mainTableName, "MultiValueLookup");
		var fieldsAfterRemove = mainConnection.GetDataSource(mainTableName)!;
		
		Assert.Throws<DataStoreConfigurationException>(() => mainConnection.CreateField(mainTableName, new StorageFieldConfig("MultiValueLookup1")
		{
			LookupSource = "nonexistendtable",
			MultiValue = true
		}));
		
		Assert.Equal(referenceTableName, field.LookupSource);
		Assert.True(field.MultiValue);
		Assert.Contains(fields.Fields, f => f.Name == "MultiValueLookup");
		Assert.DoesNotContain(fieldsAfterRemove.Fields, f => f.Name == "MultiValueLookup");
	}

	[Fact(DisplayName = "Multivalue Lookup field data CRUD")]
	public void CrudMultivalueLookupFieldDataTest()
	{
		var context = (StorageContext)DataStore.GetContexts().First();
		var mainConnection = (IStorageConnection)DataStore.GetConnection(null);
		var connection = (IStorageConnection)DataStore.GetConnection(context.Identifier, TestAuth);
		var mainTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		var referenceTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		var stackingTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		
		CreateDataSource(mainConnection, mainTableName);
		CreateDataSource(mainConnection, referenceTableName);
		CreateDataSource(mainConnection, stackingTableName);
		
		AddTestFields(connection, referenceTableName, true);
		AddTestFields(mainConnection, stackingTableName);
		
		var field = mainConnection.CreateField(mainTableName, new StorageFieldConfig("MultiValueLookup")
		{
			LookupSource = referenceTableName,
			MultiValue = true
		});
		
		mainConnection.CreateField(mainTableName, new StorageFieldConfig("ValueLookup")
		{
			LookupSource = referenceTableName
		});
		
		mainConnection.CreateField(stackingTableName, new StorageFieldConfig("LookupField")
		{
			LookupSource = mainTableName
		});
		
		var insertInfo1 = InsertData(connection, referenceTableName, "dummy data 1", null, true);
		var insertInfo2 = InsertData(connection, referenceTableName, "dummy data 1", null, true);
		var insertInfo3 = InsertData(connection, referenceTableName, "dummy data 1", null, true);
		var element = connection.CreateElement(mainTableName, new DataStoreElementData()
		{
			Values =
			{
				["MultiValueLookup"] = new List<string> { insertInfo1.ElementId, insertInfo2.ElementId },
				["ValueLookup"] = insertInfo1.ElementId,
			},
			Groups = TestAuth.Groups
		}, new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "me"));

		var lookupElement = connection.CreateElement(stackingTableName, new DataStoreElementData()
		{
			Values =
			{
				["LookupField"] = element.ElementId
			},
			Groups = TestAuth.Groups
		}, new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 13, "me"));

		var elementsBeforeFailure = connection.GetElements(new DataStoreQuery(mainTableName, null));
		Assert.Throws<DataStoreOperationException>(() => connection.CreateElement(mainTableName, new DataStoreElementData()
		{
			Values =
			{
				// elementid does not exist
				["MultiValueLookup"] = new List<string> { insertInfo1.ElementId, Guid.NewGuid().ToString() }
			},
			Groups = TestAuth.Groups
		}, new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "me")));
		var elementsAfterFailure = connection.GetElements(new DataStoreQuery(mainTableName, null));
		
		Assert.Throws<DataStoreOperationException>(() => connection.UpdateElement(mainTableName, new DataStoreElementData()
		{
			ElementId = element.ElementId,
			Values =
			{
				// elementid does not exist
				["MultiValueLookup"] = new List<string> { insertInfo1.ElementId, Guid.NewGuid().ToString() }
			},
			Groups = TestAuth.Groups
		}, new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "me")));
		var elementsAfterUpdateFailure = connection.GetElements(new DataStoreQuery(mainTableName, null));

		var selectedElement = connection.GetElement(mainTableName, element.ElementId, new List<DataStoreQueryField>()
		{
			new DataStoreQueryField("MultiValueLookup.StringField"),
			new DataStoreQueryField("MultiValueLookup.IntField"),
			new DataStoreQueryField("ValueLookup.StringField"),
			new DataStoreQueryField("ValueLookup.IntField"),
			new DataStoreQueryField("ValueLookup.MultiValueIntField"),
		});
		
		var selectedElements = connection.GetElements(new DataStoreQuery(mainTableName, new List<DataStoreQueryField>()
		{
			new DataStoreQueryField("Id"),
			new DataStoreQueryField("MultiValueLookup"),
			new DataStoreQueryField("MultiValueLookup.StringField"),
			new DataStoreQueryField("MultiValueLookup.IntField"),
			new DataStoreQueryField("ValueLookup"),
			new DataStoreQueryField("ValueLookup.StringField"),
			new DataStoreQueryField("ValueLookup.IntField"),
			new DataStoreQueryField("ValueLookup.MultiValueIntField"),
		}));
		
		// order should be persisted
		var updatedElement = connection.UpdateElement(mainTableName, new DataStoreElementData()
		{
			ElementId = element.ElementId,
			Values =
			{
				["MultiValueLookup"] = new List<string> { insertInfo2.ElementId, insertInfo1.ElementId },
				["ValueLookup"] = insertInfo2.ElementId
			},
			Groups = TestAuth.Groups
		}, new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "me"));
		
		var selectedElementAfterUpdate = connection.GetElement(mainTableName, element.ElementId, new List<DataStoreQueryField>()
		{
			new DataStoreQueryField("MultiValueLookup.StringField"),
			new DataStoreQueryField("MultiValueLookup.IntField"),
			new DataStoreQueryField("ValueLookup.StringField"),
			new DataStoreQueryField("ValueLookup.IntField"),
			new DataStoreQueryField("ValueLookup.MultiValueIntField"),
		});
		
		var selectMultivalueFieldStacking = connection.GetElements(new DataStoreQuery(stackingTableName, new List<DataStoreQueryField>()
		{
			new DataStoreQueryField("Id"),
			new DataStoreQueryField("LookupField"),
			new DataStoreQueryField("LookupField.MultiValueLookup"),
			new DataStoreQueryField("LookupField.MultiValueLookup.StringField"),
			new DataStoreQueryField("LookupField.MultiValueLookup.IntField"),
			new DataStoreQueryField("LookupField.ValueLookup"),
			new DataStoreQueryField("LookupField.ValueLookup.StringField"),
			new DataStoreQueryField("LookupField.ValueLookup.IntField"),
			new DataStoreQueryField("LookupField.ValueLookup.MultiValueIntField"),
		}));
		
		mainConnection.CreateField(mainTableName, new StorageFieldConfig("MultiValueLookup1")
		{
			LookupSource = referenceTableName,
			MultiValue = true
		});
		
		var selectMultivalueFieldStackingNoData = connection.GetElements(new DataStoreQuery(stackingTableName, new List<DataStoreQueryField>()
		{
			new DataStoreQueryField("Id"),
			new DataStoreQueryField("LookupField"),
			new DataStoreQueryField("LookupField.MultiValueLookup1"),
			new DataStoreQueryField("LookupField.MultiValueLookup1.StringField"),
			new DataStoreQueryField("LookupField.MultiValueLookup1.IntField"),
		}));
		
		mainConnection.RemoveDataSource(stackingTableName);

		connection.DeleteElement(mainTableName, element.ElementId, new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 0, "me"));
		var selectedElementsAfterDelete = connection.GetElements(new DataStoreQuery(mainTableName, new List<DataStoreQueryField>()
		{
			new DataStoreQueryField("Id"),
			new DataStoreQueryField("MultiValueLookup"),
			new DataStoreQueryField("MultiValueLookup.StringField"),
			new DataStoreQueryField("MultiValueLookup.IntField"),
		}));
		
		Assert.Equivalent(elementsBeforeFailure, elementsAfterFailure);
		Assert.Equivalent(elementsBeforeFailure, elementsAfterUpdateFailure);

		Assert.Equal(element.ElementData!.Values["Id"], selectedElement.Values["Id"]);
		Assert.Equal(new List<string> { insertInfo1.ElementId, insertInfo2.ElementId }, selectedElement.Values["MultiValueLookup"]);
		Assert.Equal(insertInfo1.ElementId, selectedElement.Values["ValueLookup"]);
		Assert.Equal(new List<string> { (string)insertInfo1.ElementData!.Values["StringField"]!, (string)insertInfo2.ElementData!.Values["StringField"]! }, selectedElement.Values["MultiValueLookup.StringField"]);
		Assert.Equal((string)insertInfo1.ElementData!.Values["StringField"]!, selectedElement.Values["ValueLookup.StringField"]);
		Assert.Equal((int)insertInfo1.ElementData!.Values["IntField"]!, selectedElement.Values["ValueLookup.IntField"]);
		Assert.Equal(insertInfo1.ElementData!.Values["MultiValueIntField"]!, selectedElement.Values["ValueLookup.MultiValueIntField"]);

		Assert.Single(selectedElements);
		Assert.Equal(element.ElementData!.Values["Id"], selectedElements[0].Values["Id"]);
		Assert.Equal(new List<string> { insertInfo1.ElementId, insertInfo2.ElementId }, selectedElements[0].Values["MultiValueLookup"]);
		Assert.Equal(insertInfo1.ElementId, selectedElements[0].Values["ValueLookup"]);
		Assert.Equal(new List<string> { (string)insertInfo1.ElementData!.Values["StringField"]!, (string)insertInfo2.ElementData!.Values["StringField"]! }, selectedElements[0].Values["MultiValueLookup.StringField"]);
		Assert.Equal((string)insertInfo1.ElementData!.Values["StringField"]!, selectedElements[0].Values["ValueLookup.StringField"]);
		Assert.Equal(new List<int> { (int)insertInfo1.ElementData!.Values["IntField"]!, (int)insertInfo2.ElementData!.Values["IntField"]! }, selectedElements[0].Values["MultiValueLookup.IntField"]);
		Assert.Equal((int)insertInfo1.ElementData!.Values["IntField"]!, selectedElements[0].Values["ValueLookup.IntField"]);
		Assert.Equal(insertInfo1.ElementData!.Values["MultiValueIntField"]!, selectedElements[0].Values["ValueLookup.MultiValueIntField"]);

		Assert.Equal(element.ElementData!.Values["Id"], selectedElementAfterUpdate.Values["Id"]);
		Assert.Equal(new List<string> { insertInfo2.ElementId, insertInfo1.ElementId }, selectedElementAfterUpdate.Values["MultiValueLookup"]);
		Assert.Equal(insertInfo2.ElementId, selectedElementAfterUpdate.Values["ValueLookup"]);
		Assert.Equal(new List<string> { (string)insertInfo2.ElementData!.Values["StringField"]!, (string)insertInfo1.ElementData!.Values["StringField"]! }, selectedElementAfterUpdate.Values["MultiValueLookup.StringField"]);
		Assert.Equal((string)insertInfo2.ElementData!.Values["StringField"]!, selectedElementAfterUpdate.Values["ValueLookup.StringField"]);
		Assert.Equal(new List<int> { (int)insertInfo2.ElementData!.Values["IntField"]!, (int)insertInfo1.ElementData!.Values["IntField"]! }, selectedElementAfterUpdate.Values["MultiValueLookup.IntField"]);
		Assert.Equal((int)insertInfo2.ElementData!.Values["IntField"]!, selectedElementAfterUpdate.Values["ValueLookup.IntField"]);
		Assert.Equal(insertInfo2.ElementData!.Values["MultiValueIntField"]!, selectedElementAfterUpdate.Values["ValueLookup.MultiValueIntField"]);
		
		Assert.Single(selectMultivalueFieldStacking);
		var multivalueFieldStack = selectMultivalueFieldStacking.First();
		Assert.Equal(element.ElementId, multivalueFieldStack.Values["LookupField"]);
		Assert.Equal(new List<string> { insertInfo2.ElementId, insertInfo1.ElementId }, multivalueFieldStack.Values["LookupField.MultiValueLookup"]);
		Assert.Equal(insertInfo2.ElementId, multivalueFieldStack.Values["LookupField.ValueLookup"]);
		Assert.Equal(new List<string> { (string)insertInfo2.ElementData!.Values["StringField"]!, (string)insertInfo1.ElementData!.Values["StringField"]! }, multivalueFieldStack.Values["LookupField.MultiValueLookup.StringField"]);
		Assert.Equal((string)insertInfo2.ElementData!.Values["StringField"]!, multivalueFieldStack.Values["LookupField.ValueLookup.StringField"]);
		Assert.Equal(new List<int> { (int)insertInfo2.ElementData!.Values["IntField"]!, (int)insertInfo1.ElementData!.Values["IntField"]! }, multivalueFieldStack.Values["LookupField.MultiValueLookup.IntField"]);
		Assert.Equal((int)insertInfo2.ElementData!.Values["IntField"]!, multivalueFieldStack.Values["LookupField.ValueLookup.IntField"]);
		Assert.Equal(insertInfo2.ElementData!.Values["MultiValueIntField"]!, multivalueFieldStack.Values["LookupField.ValueLookup.MultiValueIntField"]);
		
		Assert.Single(selectMultivalueFieldStackingNoData);
		var multivalueFieldStackNoData = selectMultivalueFieldStackingNoData.First();
		Assert.Equal(element.ElementId, multivalueFieldStackNoData.Values["LookupField"]);
		Assert.Equal(new List<string>(), multivalueFieldStackNoData.Values["LookupField.MultiValueLookup1"]);
		Assert.Equal(new List<string>(), multivalueFieldStackNoData.Values["LookupField.MultiValueLookup1.StringField"]);
		Assert.Equal(new List<int>(), multivalueFieldStackNoData.Values["LookupField.MultiValueLookup1.IntField"]);
		
		Assert.Empty(selectedElementsAfterDelete);
	}

	public static IEnumerable<object[]> MultivalueLookupFilters()
	{
		return [
			[
				"get by id",
				(string[] ids) => new QueryFilterGroup().AddFilter(new EqualsFilter(new QueryFilterField("MultiValueLookup"), ids)),
				(int[]) [3]
			],
			[
				"string equals",
				new QueryFilterGroup().AddFilter(new EqualsFilter(new QueryFilterField("MultiValueLookup.StringField"), (string[])["teststring123", "abcde", "test"])),
				(int[]) [3]
			],
			[
				"string like",
				new QueryFilterGroup().AddFilter(new LikeFilter(new QueryFilterField("MultiValueLookup.StringField"), "tEst%")),
				(int[]) [1, 2, 3]
			],
			[
				"string in",
				new QueryFilterGroup().AddFilter(new InFilter("test", new QueryFilterField("MultiValueLookup.StringField"))),
				(int[]) [3]
			]
		];
	}
	
	[Theory(DisplayName = "Multivalue Lookup field filters")]
	[MemberData(nameof(MultivalueLookupFilters))]
	public void CrudMultivalueLookupFieldFilterTest(string description, object queryFilter, int[] resultSet)
	{
		var context = (StorageContext)DataStore.GetContexts().First();
		var mainConnection = (IStorageConnection)DataStore.GetConnection(null);
		var connection = (IStorageConnection)DataStore.GetConnection(context.Identifier, TestAuth);
		var mainTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		var referenceTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		
		CreateDataSource(mainConnection, mainTableName);
		CreateDataSource(mainConnection, referenceTableName);
		
		AddTestFields(connection, referenceTableName);
		
		mainConnection.CreateField(mainTableName, new StorageFieldConfig("MultiValueLookup")
		{
			LookupSource = referenceTableName,
			MultiValue = true
		});
		
		mainConnection.CreateField(mainTableName, new StorageFieldConfig("Number")
		{
			Type = DataStoreFieldType.Integer,
			Nullable = false
		});
		
		var insertInfo1 = InsertData(connection, referenceTableName, "dummy data 1", new Dictionary<string, object>()
		{
			["StringField"] = "teststring123"
		}, true);
		var insertInfo2 = InsertData(connection, referenceTableName, "dummy data 1", new Dictionary<string, object>()
		{
			["StringField"] = "abcde"
		}, true);
		var insertInfo3 = InsertData(connection, referenceTableName, "dummy data 1", new Dictionary<string, object>()
		{
			["StringField"] = "test"
		}, true);
		connection.CreateElement(mainTableName, new DataStoreElementData()
		{
			Values =
			{
				["Number"] = 1,
				["MultiValueLookup"] = new List<string> { insertInfo1.ElementId, insertInfo2.ElementId }
			},
			Groups = TestAuth.Groups
		}, new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "me"));
		connection.CreateElement(mainTableName, new DataStoreElementData()
		{
			Values =
			{
				["Number"] = 2,
				["MultiValueLookup"] = new List<string> { insertInfo2.ElementId, insertInfo1.ElementId }
			},
			Groups = TestAuth.Groups
		}, new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "me"));
		connection.CreateElement(mainTableName, new DataStoreElementData()
		{
			Values =
			{
				["Number"] = 3,
				["MultiValueLookup"] = new List<string> { insertInfo1.ElementId, insertInfo2.ElementId, insertInfo3.ElementId }
			},
			Groups = TestAuth.Groups
		}, new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "me"));
		connection.CreateElement(mainTableName, new DataStoreElementData()
		{
			Values =
			{
				["Number"] = 4,
				["MultiValueLookup"] = new List<string> { insertInfo2.ElementId }
			},
			Groups = TestAuth.Groups
		}, new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "me"));

		QueryFilterGroup queryFilterGroup;
		if (queryFilter is QueryFilterGroup qfg)
			queryFilterGroup = qfg;
		else if (queryFilter is Func<string[], QueryFilterGroup> func)
			queryFilterGroup = func([insertInfo1.ElementId, insertInfo2.ElementId, insertInfo3.ElementId]);
		else
			throw new ArgumentException("unknown argument type: " + queryFilter);
		
		var selectedElements = connection.GetElements(new DataStoreQuery(mainTableName, new List<DataStoreQueryField>()
														  {
															  new DataStoreQueryField("Number"),
														  })
														  .WithFilter(queryFilterGroup)
														  .WithOrderBy(new List<DataStoreElementSort>()
														  {
															  new DataStoreElementSort("Number")
														  }));
		
		Assert.Equal(resultSet, selectedElements.Select(it => (int)it.Values["Number"]!));
	}
	
	[Fact(DisplayName = "Multivalue Lookup field batch insert test")]
	public void MultivalueLookupFieldBatchInsertTest()
	{
		var context = (StorageContext)DataStore.GetContexts().First();
		var mainConnection = (IStorageConnection)DataStore.GetConnection(null);
		var connection = (IStorageConnection)DataStore.GetConnection(context.Identifier, TestAuth);
		var mainTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		var referenceTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		
		CreateDataSource(mainConnection, mainTableName);
		CreateDataSource(mainConnection, referenceTableName);
		
		AddTestFields(connection, mainTableName);
		AddTestFields(connection, referenceTableName);
		
		var field = mainConnection.CreateField(mainTableName, new StorageFieldConfig("MultiValueLookup")
		{
			LookupSource = referenceTableName,
			MultiValue = true
		});
		
		var insertInfo1 = InsertData(connection, referenceTableName, "dummy data 1", null, true);
		var insertInfo2 = InsertData(connection, referenceTableName, "dummy data 1", null, true);
		var insertInfo3 = InsertData(connection, referenceTableName, "dummy data 1", null, true);
		
		Assert.Throws<DataStoreOperationException>(() => connection.CreateElements(mainTableName, new List<DataStoreElementData>()
		{
			new()
			{
				Values =
				{
					["MultiValueLookup"] = new List<string> { insertInfo1.ElementId, insertInfo2.ElementId }
				},
				Groups = TestAuth.Groups
			},
			new()
			{
				Values =
				{
					["MultiValueLookup"] = new List<string> { insertInfo1.ElementId, insertInfo3.ElementId }
				},
				Groups = TestAuth.Groups
			},
			new()
			{
				Values =
				{
					["MultiValueLookup"] = new List<string> { insertInfo2.ElementId, Guid.NewGuid().ToString() }
				},
				Groups = TestAuth.Groups
			},
		}, new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "me")));

		var firstQuery = connection.GetElements(new DataStoreQuery(mainTableName, null));
		
		var elements = connection.CreateElements(mainTableName, new List<DataStoreElementData>()
		{
			new()
			{
				Values =
				{
					["MultiValueLookup"] = new List<string> { insertInfo1.ElementId, insertInfo2.ElementId }
				},
				Groups = TestAuth.Groups
			},
			new()
			{
				Values =
				{
					["MultiValueLookup"] = new List<string> { insertInfo1.ElementId, insertInfo3.ElementId }
				},
				Groups = TestAuth.Groups
			},
			new()
			{
				Values =
				{
					["MultiValueLookup"] = new List<string> { insertInfo2.ElementId, insertInfo1.ElementId }
				},
				Groups = TestAuth.Groups
			},
			new()
			{
				Values =
				{
					["StringField"] = "dummy string",
				},
				Groups = TestAuth.Groups
			},
		}, new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "me"));

		var elementsSelect = connection.GetElements(new DataStoreQuery(mainTableName, null));
		
		Assert.Empty(firstQuery);
		Assert.Equal(4, elements.Count);
		Assert.Equal(4, elementsSelect.Count);

		Assert.Equal(new List<string> { insertInfo1.ElementId, insertInfo2.ElementId }, elementsSelect[0].Values["MultiValueLookup"]);
		Assert.Equal(new List<string> { insertInfo1.ElementId, insertInfo3.ElementId }, elementsSelect[1].Values["MultiValueLookup"]);
		Assert.Equal(new List<string> { insertInfo2.ElementId, insertInfo1.ElementId }, elementsSelect[2].Values["MultiValueLookup"]);
		Assert.Equal(new List<string>(), elementsSelect[3].Values["MultiValueLookup"]);
	}

	[Fact(DisplayName = "Update field to Multivalue")]
	public void UpdateFieldToMultivalueTest()
	{
		var context = (StorageContext)DataStore.GetContexts().First();
		var mainConnection = (IStorageConnection)DataStore.GetConnection(null);
		var connection = (IStorageConnection)DataStore.GetConnection(context.Identifier, TestAuth);
		var mainTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		
		CreateDataSource(mainConnection, mainTableName);
		var testFields = AddTestFields(mainConnection, mainTableName);
		
		var insertInfo1 = InsertData(connection, mainTableName, "dummy data 1", null, true);
		var insertInfo2 = InsertData(connection, mainTableName, "dummy data 2", new Dictionary<string, object?>
		{
			["StringField"] = "",
			["IntField"] = 0,
			["DoubleField"] = 0,
			["BooleanField"] = null,
			["DateTimeField"] = null
		});
		foreach (var testField in testFields)
		{
			UpdateFieldToArray(mainConnection, mainTableName, testField);
		}

		var elements = connection.GetElements(new DataStoreQuery(mainTableName, null));

		foreach (var testField in testFields)
		{
			Assert.Equal(new List<object?>(){insertInfo1.ElementData!.Values[testField.Name]}, elements[0].Values[testField.Name]);

			var compArr = new List<object?>();
			if (!string.IsNullOrEmpty(insertInfo2.ElementData!.Values[testField.Name]?.ToString()))
				compArr.Add(insertInfo2.ElementData!.Values[testField.Name]);
			Assert.Equal(compArr, elements[1].Values[testField.Name]);
		}
	}

	[Fact(DisplayName = "Update lookup field to multivalue lookup field")]
	public void UpdateLookupFieldToMultivalueLookupFieldTest()
	{
		var context = (StorageContext)DataStore.GetContexts().First();
		var mainConnection = (IStorageConnection)DataStore.GetConnection(null);
		var connection = (IStorageConnection)DataStore.GetConnection(context.Identifier, TestAuth);
		var mainTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		var referenceTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		
		CreateDataSource(mainConnection, mainTableName);
		CreateDataSource(mainConnection, referenceTableName);
		
		AddTestFields(connection, mainTableName);
		AddTestFields(connection, referenceTableName);
		
		var field = mainConnection.CreateField(mainTableName, new StorageFieldConfig("MultiValueLookup")
		{
			LookupSource = referenceTableName
		});
		
		var insertInfo1 = InsertData(connection, referenceTableName, "dummy data 1", null, true);
		var insertInfo2 = InsertData(connection, referenceTableName, "dummy data 1", null, true);
		var insertInfo3 = InsertData(connection, referenceTableName, "dummy data 1", null, true);

		var firstQuery = connection.GetElements(new DataStoreQuery(mainTableName, null));
		
		var elements = connection.CreateElements(mainTableName, new List<DataStoreElementData>()
		{
			new()
			{
				Values =
				{
					["MultiValueLookup"] = insertInfo1.ElementId
				},
				Groups = TestAuth.Groups
			},
			new()
			{
				Values =
				{
					["MultiValueLookup"] = insertInfo3.ElementId
				},
				Groups = TestAuth.Groups
			},
			new()
			{
				Values =
				{
					["StringField"] = "dummy string",
				},
				Groups = TestAuth.Groups
			},
		}, new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "me"));

		mainConnection.UpdateField(mainTableName, new StorageFieldConfig("MultiValueLookup")
		{
			LookupSource = referenceTableName,
			MultiValue = true
		});
		
		var elementsSelect = connection.GetElements(new DataStoreQuery(mainTableName, null));
		
		Assert.Empty(firstQuery);
		Assert.Equal(3, elements.Count);
		Assert.Equal(3, elementsSelect.Count);

		Assert.Equal(new List<string> { insertInfo1.ElementId }, elementsSelect[0].Values["MultiValueLookup"]);
		Assert.Equal(new List<string> { insertInfo3.ElementId }, elementsSelect[1].Values["MultiValueLookup"]);
		Assert.Equal(new List<string>(), elementsSelect[2].Values["MultiValueLookup"]);
	}

	private void UpdateFieldToArray(IStorageConnection mainConnection, string dataSourceName, StorageFieldConfig testField)
	{
		var newField = new StorageFieldConfig(testField.Name)
		{
			Length = testField.Length,
			Nullable = testField.Nullable,
			Type = testField.Type,
			DefaultValue = testField.DefaultValue,
			MultiValue = true
		};
		mainConnection.UpdateField(dataSourceName, newField);
	}
	
	[Fact(DisplayName = "Active/Inactive states")]
	public void InactiveTest()
	{
		InactiveSubTest();
	}

	[Fact(DisplayName = "Active/Inactive states async")]
	public void InactiveAsyncTest()
	{
		InactiveSubTest(true);
	}

	private void InactiveSubTest(bool async = false)
	{
		var connection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		var mainConnection = (IStorageConnection)DataStore.GetConnection(null);
		var compareConnection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		var mainTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, mainTableName);
		AddTestFields(mainConnection, mainTableName);

		var dataSource = mainConnection.GetDataSource(mainTableName)!;
		var createInfo1 = InsertData(connection, mainTableName, "", RandomDataHelper.GetDataSourceData(dataSource), true, true);
		var createInfo2 = InsertData(connection, mainTableName, "", RandomDataHelper.GetDataSourceData(dataSource), true, false);
		var createInfo3 = InsertData(connection, mainTableName, "", RandomDataHelper.GetDataSourceData(dataSource), true, true);
		var createInfo4 = InsertData(connection, mainTableName, "", RandomDataHelper.GetDataSourceData(dataSource), true, false);
		var createInfo5 = InsertData(connection, mainTableName, "", RandomDataHelper.GetDataSourceData(dataSource), true, true);
		var createInfo6 = InsertData(connection, mainTableName, "", RandomDataHelper.GetDataSourceData(dataSource), true, false);

		List<DataStoreQueryField>? fields = new List<DataStoreQueryField>();
		fields.Add(new DataStoreQueryField("IntField"));
		fields.Add(new DataStoreQueryField("StringField"));
		var elementsBeforeInactive = connection.GetElements(new DataStoreQuery(mainTableName, fields));
		
		List<Task> asyncTasks = new List<Task>();

		if (async)
		{
			asyncTasks.Add(connection.SetInactiveAsync(mainTableName, createInfo1.ElementId));
			asyncTasks.Add(connection.SetInactiveAsync(mainTableName, createInfo3.ElementId));
			asyncTasks.Add(connection.SetInactiveAsync(mainTableName, createInfo5.ElementId));
			asyncTasks.Add(connection.SetInactiveAsync(mainTableName, createInfo6.ElementId));
		}
		else
		{
			connection.SetInactive(mainTableName, createInfo1.ElementId);
			connection.SetInactive(mainTableName, createInfo3.ElementId);
			connection.SetInactive(mainTableName, createInfo5.ElementId);
			connection.SetInactive(mainTableName, createInfo6.ElementId);
		}

		
		Task.WaitAll(asyncTasks.ToArray());
		asyncTasks.Clear();

		var expectedElementCountAfterSetInactive = 2;
		var elementsAfterInactive = compareConnection.GetElements(new DataStoreQuery(mainTableName, null));

		if (async)
		{
			asyncTasks.Add(connection.SetActiveAsync(mainTableName, createInfo3.ElementId));
			asyncTasks.Add(connection.SetActiveAsync(mainTableName, createInfo5.ElementId));
		}
		else
		{
			connection.SetActive(mainTableName, createInfo3.ElementId);
			connection.SetActive(mainTableName, createInfo5.ElementId);
		}


		Task.WaitAll(asyncTasks.ToArray());
		asyncTasks.Clear();
		
		var expectedElementCountAfterSetActive = 4;
		var elementsAfterActive = compareConnection.GetElements(new DataStoreQuery(mainTableName, fields));
		
		
		QueryFilterField inactiveField = new QueryFilterField("SysInactiveDate");
		var elementsWithInactive = compareConnection.GetElements(new DataStoreQuery(mainTableName, fields)
																   .WithFilter(new QueryFilterGroup(QueryFilterLinkType.Or)
																				   .AddFilter(new NotNullFilter(inactiveField))
																				   .AddFilter(new IsNullFilter(inactiveField))));

		
		Assert.Equal(6, elementsBeforeInactive.Count);
		Assert.Equal(expectedElementCountAfterSetInactive, elementsAfterInactive.Count);
		Assert.Equal(expectedElementCountAfterSetActive, elementsAfterActive.Count);
		Assert.Equal(6, elementsWithInactive.Count);
		Assert.Equal(2, elementsWithInactive.Where(it => it.IsInactive).Count());
	}
	
	[Fact(DisplayName = "Favourites: add and remove of users at favourite data sets")]
	public void FavouriteTest()
	{
		FavouriteSubTest();
	}

	[Fact(DisplayName = "Favourites: add and remove of users at favourite data sets async")]
	public void FavouriteAsyncTest()
	{
		FavouriteSubTest(true);
	}

	private void FavouriteSubTest(bool async = false)
	{
		var connection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		var mainConnection = (IStorageConnection)DataStore.GetConnection(null);
		var compareConnection = (IStorageConnection)DataStore.GetConnection(((IStorage)DataStore).GetContexts().First().Identifier, TestAuth);
		var mainTableName = "test_" + RandomDataHelper.Random.NextString(20, false);
		CreateDataSource(mainConnection, mainTableName);
		AddTestFields(mainConnection, mainTableName);
		
		var dataSource = connection.GetDataSource(mainTableName)!;
		List<DataStoreSuccessInfo> createInfos = new List<DataStoreSuccessInfo>();
		for (int i = 0; i < 10; i++)
		{
			// create
			var info = InsertData(connection, mainTableName, "", RandomDataHelper.GetDataSourceData(dataSource), true, false);
			var infoWithFile = InsertData(connection, mainTableName, "", RandomDataHelper.GetDataSourceData(dataSource), true, true);
			createInfos.Add(info);
			createInfos.Add(infoWithFile);
		}

		List<Task> asyncTasks = new List<Task>();
		
		if (async)
		{
			for (int i = 0; i < 20; i++)
			{
				asyncTasks.Add(connection.AddFavouriteAsync(mainTableName, createInfos[i].ElementId));
			}
		}
		else
		{
			for (int i = 0; i < 20; i++)
			{
				connection.AddFavourite(mainTableName, createInfos[i].ElementId);
			}
		}

		
		Task.WaitAll(asyncTasks.ToArray());
		asyncTasks.Clear();
		
		var expectedElementCountAfterSetFavorite = 20;
		var res20First = compareConnection.GetElements(new DataStoreQuery(mainTableName, null)
																					  .WithFilter(new QueryFilterGroup()
																									  .AddFilter(new FavouriteFilter(true))));


		if (async)
		{
			for (int i = 0; i < 20; i++)
			{
				if (i % 2 == 0)
				{
					asyncTasks.Add(connection.RemoveFavouriteAsync(mainTableName, createInfos[i].ElementId));
				}
			}
		}
		else
		{
			for (int i = 0; i < 20; i++)
			{
				if (i % 2 == 0)
				{
					connection.RemoveFavourite(mainTableName, createInfos[i].ElementId);
				}
			}
		}


		Task.WaitAll(asyncTasks.ToArray());
		asyncTasks.Clear();
		
		var expectedElementCountAfterRemoveFavorite = 10;
		var res10First = compareConnection.GetElements(new DataStoreQuery(mainTableName, null)
																					  .WithFilter(new QueryFilterGroup()
																									  .AddFilter(new FavouriteFilter(true))));


		var res20Second = compareConnection.GetElements(new DataStoreQuery(mainTableName, null)
															.WithFilter(new QueryFilterGroup()
																			.AddFilter(new FavouriteFilter(false))));

		Assert.Equal(expectedElementCountAfterSetFavorite, res20First.Count);
		Assert.Equal(expectedElementCountAfterRemoveFavorite, res10First.Count);
		Assert.Equal(20, res20Second.Count);
	}

	protected abstract void AssertFulltextResourcesCleared(StorageDataSource mainDataSource);

	protected void CreateTableForFieldChange(IStorageConnection schemaConnection, IStorageConnection dataConnection, string mainTableName)
	{
		CreateDataSource(schemaConnection, mainTableName);
		AddTestFields(schemaConnection, mainTableName, true);

		Dictionary<string, object?> additionalDict = new Dictionary<string, object?>();

		additionalDict.Add("FulltextField", RandomDataHelper.Random.NextString(500));
		additionalDict.Add("LongField", RandomDataHelper.Random.Next());
		additionalDict.Add("MultiValueIntField",
						   new List<int>() { RandomDataHelper.Random.Next(), RandomDataHelper.Random.Next(), RandomDataHelper.Random.Next() });
		additionalDict.Add("DateField", RandomDataHelper.Random.NextDay());
		additionalDict.Add("TimeField", RandomDataHelper.Random.NextDay());
		InsertData(dataConnection, mainTableName, "Insert for field change tests", additionalDict);
	}


	private DataStoreSuccessInfo InsertData(IStorageConnection connection, string tableName, string comment, Dictionary<string, object?>? additionalDict = null,
											bool fixedValues = false, bool withFile = false, bool keysLowerCase = false, bool withSpecialCharacters = true)
	{
		string stringFieldName = (keysLowerCase) ? "StringField".ToLower() : "StringField";
		string intFieldName = (keysLowerCase) ? "IntField".ToLower() : "IntField";
		string doubleFieldName = (keysLowerCase) ? "DoubleField".ToLower() : "DoubleField";
		string boolFieldName = (keysLowerCase) ? "BooleanField".ToLower() : "BooleanField";
		string dateTimeFieldName = (keysLowerCase) ? "DateTimeField".ToLower() : "DateTimeField";
		
		Dictionary<string, object?> insertDict = new Dictionary<string, object?>()
		{
			[stringFieldName] = (fixedValues) ? "Testdaten" : RandomDataHelper.Random.NextString(50, extended: withSpecialCharacters),
			[intFieldName] = (fixedValues) ? 25 : RandomDataHelper.Random.Next(int.MaxValue),
			[doubleFieldName] = (fixedValues) ? 9.75 : RandomDataHelper.Random.NextDouble(),
			[boolFieldName] = true,
			[dateTimeFieldName] = DateTime.SpecifyKind((fixedValues) ? new DateTime(2023, 12, 24) : RandomDataHelper.Random.NextDay(), DateTimeKind.Utc)
		};
		if (additionalDict != null)
		{
			foreach (var additionalDictKey in additionalDict.Keys)
			{
				insertDict.Remove(additionalDictKey);
			}

			insertDict.AddRange(additionalDict);
		}

		DataStoreOperationOrigin origin = new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "ariebau")
		{
			Comment = comment
		};
		
		string? fileUploadId = null;
		if (withFile)
		{
			using var memoryStream = new MemoryStream("this is a test with teersandverwiegung."u8.ToArray());
			fileUploadId = connection.UploadFile(tableName, new DataStoreFileStream("testfile.txt", DateTime.Now, memoryStream));
		}
		
		DataStoreElementData dataStoreElementData = new DataStoreElementData(insertDict, new List<string> { "test" })
		{
			FileUploadId = fileUploadId
		};
		var insertInfo = connection.CreateElement(tableName, dataStoreElementData, origin);
		return insertInfo;
	}


	private void InsertMultiData(IStorageConnection connection, string tableName, string comment, int count, bool keysLowerCase = false)
	{
		string stringFieldName = (keysLowerCase) ? "StringField".ToLower() : "StringField";
		string intFieldName = (keysLowerCase) ? "IntField".ToLower() : "IntField";
		string doubleFieldName = (keysLowerCase) ? "DoubleField".ToLower() : "DoubleField";
		string boolFieldName = (keysLowerCase) ? "BooleanField".ToLower() : "BooleanField";
		string dateTimeFieldName = (keysLowerCase) ? "DateTimeField".ToLower() : "DateTimeField";
		
		IList<DataStoreElementData> elementDatas = new List<DataStoreElementData>();
		DataStoreOperationOrigin origin = new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "ariebau")
		{
			Comment = comment
		};

		for (int i = 0; i < count; i++)
		{
			Dictionary<string, object?> insertDict = new Dictionary<string, object?>()
			{
				[stringFieldName] = RandomDataHelper.Random.NextString(50),
				[intFieldName] = RandomDataHelper.Random.Next(int.MaxValue),
				[doubleFieldName] = RandomDataHelper.Random.NextDouble(),
				[boolFieldName] = true,
				[dateTimeFieldName] = DateTime.SpecifyKind(RandomDataHelper.Random.NextDay(), DateTimeKind.Utc)
			};
			
			DataStoreElementData dataStoreElementData = new DataStoreElementData(insertDict, new List<string> { "test" });
			elementDatas.Add(dataStoreElementData);
		}

		connection.CreateElements(tableName, elementDatas, origin);
	}

	protected bool UpdateField(IStorageConnection connection, string mainTableName, string fieldName, DataStoreFieldType dataStoreFieldType)
	{
		StorageFieldConfig fieldConfig;
		if (dataStoreFieldType == DataStoreFieldType.String)
		{
			fieldConfig = new StorageFieldConfig(fieldName)
			{
				Type = dataStoreFieldType,
				Length = 50
			};
		}
		else
		{
			fieldConfig = new StorageFieldConfig(fieldName)
			{
				Type = dataStoreFieldType
			};
		}

		connection.UpdateField(mainTableName, fieldConfig);
		return true;
	}

	private List<DataStoreQueryField> GetAggregateFields(bool full = false)
	{
		List<DataStoreQueryField> aggFieldList = new List<DataStoreQueryField>();
		aggFieldList.AddRange(GetAggregateFieldsSingle("IdFieldFromNst1"));
		if (full)
			aggFieldList.AddRange(GetAggregateFieldsSingle("IdFieldFromNst2", "2"));
		return aggFieldList;
	}

	private List<DataStoreQueryField> GetAggregateFieldsSingle(string lookupFieldName, string part = "")
	{
		List<DataStoreQueryField> fields = new List<DataStoreQueryField>();
		DataStoreQueryField avgIntField = new DataStoreQueryField("avg(" + lookupFieldName + ".IntField)")
		{
			Alias = "AvgInt" + part
		};
		DataStoreQueryField sumDoubleField = new DataStoreQueryField("sum(" + lookupFieldName + ".DoubleField)")
		{
			Alias = "SumDouble" + part
		};
		DataStoreQueryField avgDoubleField = new DataStoreQueryField("avg(" + lookupFieldName + ".DoubleField)")
		{
			Alias = "AvgDouble" + part
		};
		DataStoreQueryField minDoubleField = new DataStoreQueryField("min(" + lookupFieldName + ".DoubleField)")
		{
			Alias = "MinDouble" + part
		};
		DataStoreQueryField maxDoubleField = new DataStoreQueryField("max(" + lookupFieldName + ".DoubleField)")
		{
			Alias = "MaxDouble" + part
		};
		DataStoreQueryField maxDateField = new DataStoreQueryField("max(" + lookupFieldName + ".DateTimeField)")
		{
			Alias = "MaxDate" + part
		};
		DataStoreQueryField countBoolField = new DataStoreQueryField("count(" + lookupFieldName + ".BooleanField)")
		{
			Alias = "CountBool" + part
		};

		fields.Add(avgIntField);
		fields.Add(sumDoubleField);
		fields.Add(avgDoubleField);
		fields.Add(minDoubleField);
		fields.Add(maxDoubleField);
		fields.Add(maxDateField);
		fields.Add(countBoolField);

		return fields;
	}

	private DataStoreResultSet<DataStoreElement> GetOrderByElements(IStorageConnection compareConnection, string mainTableName,
																	List<DataStoreElementSort> orderBy)
	{
		List<DataStoreQueryField> fields = GetQueryFields();
		DataStoreQuery orderByQuery = new DataStoreQuery(mainTableName, fields);
		orderByQuery.WithOrderBy(orderBy);

		return compareConnection.GetElements(orderByQuery);
	}

	private DataStoreResultSet<DataStoreElement> GetGroupByElements(IStorageConnection compareConnection, string mainTableName, IList<string> groupByStrings,
																	List<DataStoreQueryField>? fields = null)
	{
		if (fields == null)
			fields = new List<DataStoreQueryField>();

		IList<string> groupByList = new List<string>();
		foreach (var groupByString in groupByStrings)
		{
			fields.Add(new DataStoreQueryField(groupByString));
			groupByList.Add(groupByString);
		}

		DataStoreQuery query = new DataStoreQuery(mainTableName, fields).WithGroupBy(groupByList);
		return compareConnection.GetElements(query);
	}


	private List<DataStoreQueryField> GetQueryFields(bool addNst2Fields = false)
	{
		List<DataStoreQueryField> fields = new List<DataStoreQueryField>();
		fields.Add(new DataStoreQueryField("StringField"));
		fields.Add(new DataStoreQueryField("IntField"));
		fields.Add(new DataStoreQueryField("DoubleField"));
		fields.Add(new DataStoreQueryField("BooleanField"));
		fields.Add(new DataStoreQueryField("DateTimeField"));
		fields.Add(new DataStoreQueryField("IdFieldFromNst1"));
		fields.Add(new DataStoreQueryField("IdFieldFromNst1.StringField"));
		fields.Add(new DataStoreQueryField("IdFieldFromNst1.IntField"));
		fields.Add(new DataStoreQueryField("IdFieldFromNst1.DoubleField"));
		fields.Add(new DataStoreQueryField("IdFieldFromNst1.BooleanField"));
		fields.Add(new DataStoreQueryField("IdFieldFromNst1.DateTimeField"));

		if (addNst2Fields)
		{
			fields.Add(new DataStoreQueryField("IdFieldFromNst2.StringField"));
			fields.Add(new DataStoreQueryField("IdFieldFromNst2.IntField"));
			fields.Add(new DataStoreQueryField("IdFieldFromNst2.DoubleField"));
			fields.Add(new DataStoreQueryField("IdFieldFromNst2.BooleanField"));
			fields.Add(new DataStoreQueryField("IdFieldFromNst2.DateTimeField"));
		}

		return fields;
	}


	public IList<StorageFieldConfig> AddTestFields(IStorageConnection connection, string randomTableName, bool allTypes = false)
	{
		IList<StorageFieldConfig> fields = new List<StorageFieldConfig>();
		StorageFieldConfig stringField = new StorageFieldConfig("StringField")
		{
			Type = DataStoreFieldType.String,
			Length = 255
		};
		connection.CreateField(randomTableName, stringField);
		fields.Add(stringField);

		StorageFieldConfig intField = new StorageFieldConfig("IntField")
		{
			Type = DataStoreFieldType.Integer
		};
		connection.CreateField(randomTableName, intField);
		fields.Add(intField);

		StorageFieldConfig doubleField = new StorageFieldConfig("DoubleField")
		{
			Type = DataStoreFieldType.Double
		};
		connection.CreateField(randomTableName, doubleField);
		fields.Add(doubleField);

		StorageFieldConfig boolField = new StorageFieldConfig("BooleanField")
		{
			Type = DataStoreFieldType.Boolean
		};
		connection.CreateField(randomTableName, boolField);
		fields.Add(boolField);

		StorageFieldConfig dateTimeField = new StorageFieldConfig("DateTimeField")
		{
			Type = DataStoreFieldType.DateTime
		};
		connection.CreateField(randomTableName, dateTimeField);
		fields.Add(dateTimeField);

		if (allTypes)
		{
			StorageFieldConfig fullTextField = new StorageFieldConfig("FulltextField")
			{
				Type = DataStoreFieldType.Text,
				FulltextIndexingType = FulltextIndexingType.Summary
			};
			connection.CreateField(randomTableName, fullTextField);
			fields.Add(fullTextField);

			StorageFieldConfig longField = new StorageFieldConfig("LongField")
			{
				Type = DataStoreFieldType.Long
			};
			connection.CreateField(randomTableName, longField);
			fields.Add(longField);

			StorageFieldConfig dateField = new StorageFieldConfig("DateField")
			{
				Type = DataStoreFieldType.Date
			};
			connection.CreateField(randomTableName, dateField);
			fields.Add(dateField);

			StorageFieldConfig timeField = new StorageFieldConfig("TimeField")
			{
				Type = DataStoreFieldType.Time
			};
			connection.CreateField(randomTableName, timeField);
			fields.Add(timeField);

			StorageFieldConfig mvf = new StorageFieldConfig("MultiValueIntField")
			{
				Type = DataStoreFieldType.Integer,
				MultiValue = true
			};
			connection.CreateField(randomTableName, mvf);
			fields.Add(mvf);
		}

		return fields;
	}


	private DataStoreSuccessInfo UpdateData(IStorageConnection connection, string nstTableName, string elementId, string comment, bool keysLowerCase = false)
	{
		string stringFieldName = (keysLowerCase) ? "StringField".ToLower() : "StringField";
		string intFieldName = (keysLowerCase) ? "IntField".ToLower() : "IntField";
		string doubleFieldName = (keysLowerCase) ? "DoubleField".ToLower() : "DoubleField";
		string boolFieldName = (keysLowerCase) ? "BooleanField".ToLower() : "BooleanField";
		string dateTimeFieldName = (keysLowerCase) ? "DateTimeField".ToLower() : "DateTimeField";

		Dictionary<string, object?> updateDict = new Dictionary<string, object?>()
		{
			[stringFieldName] = RandomDataHelper.Random.NextString(50),
			[intFieldName] = RandomDataHelper.Random.Next(int.MaxValue),
			[doubleFieldName] = RandomDataHelper.Random.NextDouble(),
			[boolFieldName] = false,
			[dateTimeFieldName] = DateTime.SpecifyKind(RandomDataHelper.Random.NextDay().Subtract(TimeSpan.FromHours(12)), DateTimeKind.Utc)
		};

		DataStoreOperationOrigin origin = new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "ariebau")
		{
			Comment = comment
		};
		DataStoreElementData dataStoreElementData = new DataStoreElementData(updateDict, new List<string> { "test" })
		{
			ElementId = elementId
		};
		DataStoreSuccessInfo updateInfo = connection.UpdateElement(nstTableName, dataStoreElementData, origin);
		return updateInfo;
	}

	private void UpdateMainData(IStorageConnection connection, string mainTableName, DataStoreSuccessInfo insertMainLast,
												DataStoreSuccessInfo insertInfo1,
												DataStoreSuccessInfo insertInfo2)
	{
		Dictionary<string, object?> updateDict = new Dictionary<string, object?>()
		{
			["StringField"] = RandomDataHelper.Random.NextString(50),
			["IntField"] = RandomDataHelper.Random.Next(int.MaxValue),
			["DoubleField"] = RandomDataHelper.Random.NextDouble(),
			["BooleanField"] = true,
			["DateTimeField"] = DateTime.SpecifyKind(RandomDataHelper.Random.NextDay(), DateTimeKind.Utc),
			["IdFieldFromNst1"] = insertInfo1.ElementId,
			["IdFieldFromNst2"] = insertInfo2.ElementId
		};

		DataStoreOperationOrigin origin = new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, "ariebau")
		{
			Comment = "update main data"
		};
		DataStoreElementData dataStoreElementData = new DataStoreElementData(updateDict, new List<string> { "test" })
		{
			ElementId = insertMainLast.ElementId
		};
		connection.UpdateElement(mainTableName, dataStoreElementData, origin);
	}


	private void AddMainDataSingle(IStorageConnection connection, string mainTableName, DataStoreSuccessInfo insertInfo1)
	{
		// incl. null values / empty fields
		Dictionary<string, object?> additionalDict = new Dictionary<string, object?>()
		{
			["IdFieldFromNst1"] = insertInfo1.ElementId
		};
		InsertData(connection, mainTableName, "main data 1", additionalDict);

		// incl. null values / empty fields
		additionalDict = new Dictionary<string, object?>()
		{
			["IdFieldFromNst1"] = insertInfo1.ElementId
		};
		InsertData(connection, mainTableName, "main data 2", additionalDict);

		// incl. null values / empty fields
		additionalDict = new Dictionary<string, object?>()
		{
			["IdFieldFromNst1"] = insertInfo1.ElementId
		};
		InsertData(connection, mainTableName, "main data 3", additionalDict);
	}

	private DataStoreSuccessInfo AddMainData(IStorageConnection connection, string mainTableName, DataStoreSuccessInfo insertInfo1,
											 DataStoreSuccessInfo insertInfo2, bool fixedValues = false)
	{
		// incl. null values / empty fields
		Dictionary<string, object?> additionalDict = new Dictionary<string, object?>()
		{
			["IdFieldFromNst1"] = insertInfo1.ElementId,
			["IdFieldFromNst2"] = insertInfo2.ElementId
		};
		InsertData(connection, mainTableName, "main data 1", additionalDict, fixedValues);

		// incl. null values / empty fields
		additionalDict = new Dictionary<string, object?>()
		{
			["IdFieldFromNst1"] = insertInfo1.ElementId,
			["IdFieldFromNst2"] = insertInfo2.ElementId
		};
		InsertData(connection, mainTableName, "main data 2", additionalDict, fixedValues);

		// incl. null values / empty fields
		additionalDict = new Dictionary<string, object?>()
		{
			["IdFieldFromNst1"] = insertInfo1.ElementId,
			["IdFieldFromNst2"] = insertInfo2.ElementId
		};
		var insertMain3 = InsertData(connection, mainTableName, "main data 3", additionalDict, fixedValues);

		return insertMain3;
	}

	private void AddNstFields(IStorageConnection connection, string mainTableName, string nstTableName1, string? nstTableName2)
	{
		connection.CreateField(mainTableName, new StorageFieldConfig("IdFieldFromNst1")
		{
			Nullable = true,
			LookupSource = nstTableName1
		});

		if (nstTableName2 != null)
		{
			connection.CreateField(mainTableName, new StorageFieldConfig("IdFieldFromNst2")
			{
				Nullable = true,
				LookupSource = nstTableName2
			});
		}
	}

	public static void CreateDataSource(IStorageConnection connection, string randomTableName)
	{
		StorageDataSourceConfig config = new StorageDataSourceConfig(randomTableName)
		{
			StoreRevisions = false
		};
		connection.CreateDataSource(config);
	}

	public abstract bool FieldExistsInDb(string table, string fieldName);
}