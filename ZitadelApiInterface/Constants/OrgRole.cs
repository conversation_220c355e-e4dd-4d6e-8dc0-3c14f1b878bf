namespace Levelbuild.Core.ZitadelApiInterface.Constants;

/// <summary>
/// Collection of auth name constants referring to Zitadel organisation roles.
/// </summary>
public static class OrgRole
{
	/// <summary>
	/// The organisation's owner.
	/// </summary>
	public const string Owner = "ORG_OWNER";
	
	/// <summary>
	/// Can create and manage users.
	/// </summary>
	public const string UserManager = "ORG_USER_MANAGER";
	
	/// <summary>
	/// Has read permissions to the whole organisation.
	/// </summary>
	public const string OwnerViewer = "ORG_OWNER_VIEWER";
	
	/// <summary>
	/// Can manage the organisation's settings.
	/// </summary>
	public const string SettingsManager = "ORG_SETTINGS_MANAGER";
	
	/// <summary>
	/// Can manage user grants.
	/// </summary>
	public const string UserPermissionEditor = "ORG_USER_PERMISSION_EDITOR";
	
	/// <summary>
	/// Can manage project grants to external organisations.
	/// </summary>
	public const string ProjectPermissionEditor = "ORG_PROJECT_PERMISSION_EDITOR";
	
	/// <summary>
	/// Can create projects.
	/// </summary>
	public const string ProjectCreator = "ORG_PROJECT_CREATOR";
	
	/// <summary>
	/// Can impersonate admins and end users of the organisation.
	/// </summary>
	public const string AdminImpersonator = "ORG_ADMIN_IMPERSONATOR";
	
	/// <summary>
	/// Can impersonate end users of the organisation.
	/// </summary>
	public const string EndUserImpersonator = "ORG_END_USER_IMPERSONATOR";
}