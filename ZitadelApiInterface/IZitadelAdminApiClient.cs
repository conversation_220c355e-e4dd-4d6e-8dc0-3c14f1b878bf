using Grpc.Core;

namespace Levelbuild.Core.ZitadelApiInterface;

/// <summary>
/// Implementation of Zitadel's Admin API.
/// Doc: <see href="https://zitadel.com/docs/apis/resources/admin"/>
/// </summary>
public interface IZitadelAdminApiClient
{
	/// <summary>
	/// Lists all existing and active Zitadel orgs.
	/// </summary>
	/// <exception cref="RpcException">Gets thrown if something goes wrong.</exception>
	public IList<Zitadel.Org.V1.Org> ListOrganisiations();
}