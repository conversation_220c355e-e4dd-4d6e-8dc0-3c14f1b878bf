namespace Levelbuild.Core.ZitadelApiInterface;

/// <summary>
/// Produces Zitadel API Client instances.
/// </summary>
public interface IZitadelApiClientFactory
{
	/// <summary>
	/// Produces a new Auth API Client instance.
	/// </summary>
	public IZitadelAuthApiClient GetAuthClient(string accessToken);
	
	/// <summary>
	/// Produces a new Admin API Client instance.
	/// </summary>
	public IZitadelAdminApiClient GetAdminClient();
	
	/// <summary>
	/// Produces a new Management API Client instance.
	/// </summary>
	public IZitadelManagementApiClient GetManagementClient();
	
	/// <summary>
	/// Produces a new User Service API Client instance.
	/// </summary>
	public IZitadelUserServiceApiClient GetUserServiceClient();
}