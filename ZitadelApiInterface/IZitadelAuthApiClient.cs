using Zitadel.User.V1;

namespace Levelbuild.Core.ZitadelApiInterface;

/// <summary>
/// Implementation of Zitadel's Auth API.
/// Doc: <see href="https://zitadel.com/docs/apis/resources/auth/authentication-api-aka-auth"/>
/// </summary>
public interface IZitadelAuthApiClient
{
	/// <summary>
	/// Gets the currently authenticated User.
	/// </summary>
	/// <returns>The <see cref="User"/> instance.</returns>
	public User GetMyUser();

	/// <summary>
	/// Gets a list of the currently authenticated User's roles.
	/// </summary>
	/// <returns>An array of role names.</returns>
	public string[] GetMyProjectRoles();
	
	/// <summary>
	/// Gets a list of the currently authenticated User's organization Ids.
	/// </summary>
	/// <returns>An array of org Ids.</returns>
	public string[] GetMyOrganisationIds();
}