using Grpc.Core;
using Levelbuild.Core.FrontendDtos.User;
using Zitadel.User.V1;

namespace Levelbuild.Core.ZitadelApiInterface;

/// <summary>
/// Implementation of Zitadel's Management API.
/// Doc: <see href="https://zitadel.com/docs/apis/resources/mgmt/management-api"/>
/// </summary>
public interface IZitadelManagementApiClient
{
	/// <summary>
	/// Gets the Zitadel user instance for the given ID.
	/// </summary>
	/// <param name="userId">The user's ID.</param>
	/// <param name="orgId">The orgs's ID.</param>
	/// <exception cref="RpcException">Gets thrown if something goes wrong. (i.e. no user is found)</exception>
	public User GetUser(string userId, string orgId);
	
	/// <summary>
	/// Gets all active human users of a given org.
	/// </summary>
	/// <param name="orgId">The orgs's ID.</param>
	/// <exception cref="RpcException">Gets thrown if something goes wrong. (i.e. no user is found)</exception>
	public IList<User> GetActiveHumanUsers(string orgId);
	
	/// <summary>
	/// Gets all active machine users of a given org.
	/// </summary>
	/// <param name="orgId">The orgs's ID.</param>
	/// <exception cref="RpcException">Gets thrown if something goes wrong. (i.e. no user is found)</exception>
	public IList<User> GetActiveMachineUsers(string orgId);
	
	/// <summary>
	/// Gets all inactive users of a given org.
	/// </summary>
	/// <param name="orgId">The orgs's ID.</param>
	/// <exception cref="RpcException">Gets thrown if something goes wrong. (i.e. no user is found)</exception>
	public IList<User> GetInactiveUsers(string orgId);
	
	/// <summary>
	/// Adds a human user.
	/// </summary>
	/// <param name="dto">A DTO containing user information.</param>
	/// <param name="orgId">The orgs's ID.</param>
	/// <exception cref="RpcException">Gets thrown if something goes wrong. (i.e. the user already exists)</exception>
	public string AddHumanUser(UserDto dto, string orgId);
	
	/// <summary>
	/// Adds a machine user.
	/// </summary>
	/// <param name="dto">A DTO containing user information.</param>
	/// <param name="orgId">The orgs's ID.</param>
	/// <exception cref="RpcException">Gets thrown if something goes wrong. (i.e. the user already exists)</exception>
	public string AddMachineUser(UserDto dto, string orgId);
	
	/// <summary>
	/// Updates a human user.
	/// </summary>
	/// <param name="userId">The ID of the user instance in Zitadel.</param>
	/// <param name="orgId">The orgs's ID.</param>
	/// <param name="dto">A DTO containing user information.</param>
	/// <exception cref="RpcException">Gets thrown if something goes wrong. (i.e. the user doesn't exist)</exception>
	public void UpdateHumanUser(string userId, string orgId, UserDto dto);
	
	/// <summary>
	/// Creates a new PAT.
	/// Only available for machine users.
	/// </summary>
	/// <param name="userId">The ID of the user instance in Zitadel.</param>
	/// <param name="orgId">The orgs's ID.</param>
	/// <param name="expirationDate">The date the token will expire.</param>
	/// <exception cref="RpcException">Gets thrown if something goes wrong. (i.e. the user doesn't exist)</exception>
	public string CreatePersonalAccessToken(string userId, string orgId, DateTime expirationDate);

	/// <summary>
	/// Deactivates a user.
	/// </summary>
	/// <param name="userId">The user's ID.</param>
	/// <param name="orgId">The orgs's ID.</param>
	/// <exception cref="RpcException">Gets thrown if something goes wrong. (i.e. the user doesn't exist)</exception>
	public void DeactivateUser(string userId, string orgId);
	
	/// <summary>
    /// Reactivates a user.
    /// </summary>
    /// <param name="userId">The user's ID.</param>
    /// <param name="orgId">The orgs's ID.</param>
    /// <exception cref="RpcException">Gets thrown if something goes wrong. (i.e. the user doesn't exist)</exception>
    public void ReactivateUser(string userId, string orgId);
	
	/// <summary>
	/// Deletes a user.
	/// </summary>
	/// <param name="userId">The user's ID.</param>
	/// <param name="orgId">The orgs's ID.</param>
	/// <exception cref="RpcException">Gets thrown if something goes wrong. (i.e. the user doesn't exist)</exception>
	public void DeleteUser(string userId, string orgId);
	
	/// <summary>
	/// Get the currently authenticated User's organisation.
	/// </summary>
	/// <exception cref="RpcException">Gets thrown if something goes wrong. (i.e. no org is found)</exception>
	public Zitadel.Org.V1.Org GetMyOrganisation();
	
	/// <summary>
	/// Gets the Zitadel org instance for the given ID.
	/// </summary>
	/// <param name="orgId">The org's ID.</param>
	/// <exception cref="RpcException">Gets thrown if something goes wrong. (i.e. no org is found)</exception>
	public Zitadel.Org.V1.Org GetOrganisation(string orgId);
	
	/// <summary>
	/// Adds an organisation.
	/// </summary>
	/// <param name="name">The name of the organisation.</param>
	/// <param name="roles">A list of roles that should be added to the org's grants.</param>
	/// <exception cref="RpcException">Gets thrown if something goes wrong. (i.e. the org already exists)</exception>
	public string AddOrganisation(string name, IEnumerable<string> roles);

	/// <summary>
	/// Updates an organisation.
	/// </summary>
	/// <param name="orgId">The ID of the organisation.</param>
	/// <param name="name">The new name of the organisation.</param>
	/// <exception cref="RpcException">Gets thrown if something goes wrong. (i.e. the organisation doesn't exist)</exception>
	public void UpdateOrganisation(string orgId, string name);

	/// <summary>
	/// Deactivates an organisation.
	/// </summary>
	/// <param name="orgId">The ID of the organisation.</param>
	/// <exception cref="RpcException">Gets thrown if something goes wrong. (i.e. the organisation doesn't exist)</exception>
	public void DeactivateOrganisation(string orgId);

	/// <summary>
	/// Reactivates an organisation.
	/// </summary>
	/// <param name="orgId">The ID of the organisation.</param>
	/// <exception cref="RpcException">Gets thrown if something goes wrong. (i.e. the organisation doesn't exist)</exception>
	public void ReactivateOrganisation(string orgId);
	
	/// <summary>
	/// Deletes an organisation.
	/// </summary>
	/// <param name="orgId">The ID of the organisation.</param>
	/// <exception cref="RpcException">Gets thrown if something goes wrong. (i.e. the organisation doesn't exist)</exception>
	public void DeleteOrganisation(string orgId);
	
	/// <summary>
	/// Adds a user to a organisation.
	/// </summary>
	/// <param name="userId">The ID of the user.</param>
	/// <param name="orgId">The ID of the organisation.</param>
	/// <param name="roles">Optional list of roles.</param>
	/// <exception cref="RpcException">Gets thrown if something goes wrong. (i.e. the user or organisation doesn't exist)</exception>
	public void AddUserToOrganisation(string userId, string orgId, string[]? roles = null);

	/// <summary>
	/// Removes a user from a organisation.
	/// </summary>
	/// <param name="userId">The ID of the user.</param>
	/// <param name="orgId">The ID of the organisation.</param>
	/// <exception cref="RpcException">Gets thrown if something goes wrong. (i.e. the user or organisation doesn't exist)</exception>
	public void RemoveUserFromOrganisation(string userId, string orgId);

	/// <summary>
	/// Checks if a user has impersonation rights in a given organisation.
	/// </summary>
	/// <param name="userId">The ID of the user.</param>
	/// <param name="orgId">The ID of the organisation.</param>
	/// <exception cref="RpcException">Gets thrown if something goes wrong. (i.e. the user or organisation doesn't exist)</exception>
	public bool IsImpersonator(string userId, string orgId);
	
	/// <summary>
	/// Grants a user the rights to impersonate other user's of the same organization.
	/// </summary>
	/// <param name="userId">The ID of the user.</param>
	/// <param name="orgId">The ID of the organisation.</param>
	/// <exception cref="RpcException">Gets thrown if something goes wrong. (i.e. the user or organisation doesn't exist)</exception>
	public void AddImpersonationRights(string userId, string orgId);

	/// <summary>
	/// Removes a user's impersonation rights.
	/// </summary>
	/// <param name="userId">The ID of the user.</param>
	/// <param name="orgId">The ID of the organisation.</param>
	/// <exception cref="RpcException">Gets thrown if something goes wrong. (i.e. the user or organisation doesn't exist)</exception>
	public void RemoveImpersonationRights(string userId, string orgId);
	
	/// <summary>
	/// Lists all user grants of a given org.
	/// </summary>
	/// <param name="orgId">The ID of the organisation.</param>
	/// <exception cref="RpcException">Gets thrown if something goes wrong. (i.e. the project or organisation doesn't exist)</exception>
	public IList<UserGrant> ListUserGrants(string orgId);
	
	/// <summary>
	/// Adds a grant to a user.
	/// </summary>
	/// <param name="userId">The ID of the user.</param>
	/// <param name="orgId">The ID of the organisation.</param>
	/// <param name="roles">A list of roles that should be added to the users's grants.</param>
	/// <exception cref="RpcException">Gets thrown if something goes wrong. (i.e. the user doesn't exist)</exception>
	public void AddUserGrant(string userId, string orgId, IEnumerable<string> roles);

	/// <summary>
	/// Updates a grant of a user.
	/// </summary>
	/// <param name="userId">The ID of the user.</param>
	/// <param name="orgId">The ID of the organisation.</param>
	/// <param name="roles">A list of roles that should be added to the users's grants.</param>
	/// <exception cref="RpcException">Gets thrown if something goes wrong. (i.e. the user doesn't exist)</exception>
	public void UpdateUserGrant(string userId, string orgId, IEnumerable<string> roles);
	
	/// <summary>
	/// Removes a grant of a user.
	/// </summary>
	/// <param name="userId">The ID of the user.</param>
	/// <param name="orgId">The ID of the organisation.</param>
	/// <exception cref="RpcException">Gets thrown if something goes wrong. (i.e. the user doesn't exist)</exception>
	public void RemoveUserGrant(string userId, string orgId);
}