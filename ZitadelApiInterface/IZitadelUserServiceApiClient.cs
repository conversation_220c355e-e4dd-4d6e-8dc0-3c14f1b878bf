using Google.Protobuf.WellKnownTypes;
using Zitadel.User.V2;

namespace Levelbuild.Core.ZitadelApiInterface;

/// <summary>
/// Implementation of Zitadel's User Service API.
/// Doc: <see href="https://zitadel.com/docs/apis/resources/user_service_v2"/>
/// </summary>
public interface IZitadelUserServiceApiClient
{
	/// <summary>
	/// Get a passkey registration code to start the registration flow.
	/// </summary>
	/// <param name="userId"></param>
	/// <returns>The passkey registration code.</returns>
	public PasskeyRegistrationCode CreatePasskeyRegistrationCode(string userId);

	/// <summary>
	/// Get the credentials that enable passkey registration on the user's device.
	/// </summary>
	/// <param name="userId"></param>
	/// <param name="code"></param>
	/// <param name="domain"></param>
	/// <returns>The public key credential creation options, which are used to register the passkey on device</returns>
	public (string PasskeyId, Struct Credentials) GetPasskeyRegistrationCredentials(string userId, PasskeyRegistrationCode code, string domain);
	
	/// <summary>
	/// Verifies the passkey registration with the public key credential.
	/// </summary>
	/// <param name="userId"></param>
	/// <param name="passkeyId"></param>
	/// <param name="passkeyName"></param>
	/// <param name="publicKeyCredentials"></param>
	/// <returns></returns>
	public (bool Success, string? Message) VerifyPasskeyRegistration(string userId, string passkeyId, string passkeyName, Struct publicKeyCredentials);
	
	/// <summary>
	/// Lists passkeys of a user.
	/// </summary>
	/// <param name="userId"></param>
	/// <returns></returns>
	public IList<Passkey> ListPasskeys(string userId);
	
	/// <summary>
	/// Removes passkey from an user.
	/// </summary>
	/// <param name="userId"></param>
	/// <param name="passkeyId"></param>
	public void RemovePasskey(string userId, string passkeyId);
}