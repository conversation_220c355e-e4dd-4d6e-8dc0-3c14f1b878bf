<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>

        <RootNamespace>Levelbuild.Core.ZitadelApiInterface</RootNamespace>
        <Company>levelbuild AG</Company>
        <PackageId>levelbuild.ZitadelApiInterface</PackageId>
        
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
    </PropertyGroup>

    
    <!-- local solution reference -->
    <ItemGroup>
      <ProjectReference Include="..\FrontendDtos\FrontendDtos.csproj" />
      <ProjectReference Include="..\SharedDtos\SharedDtos.csproj" />
    </ItemGroup>

    
    <ItemGroup>
      <PackageReference Include="Zitadel" Version="7.0.23" />
    </ItemGroup>

</Project>
